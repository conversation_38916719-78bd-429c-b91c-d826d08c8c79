# encoding=utf-8

import json

from handler import <PERSON>piH<PERSON><PERSON>
from store import endpoint_repair_model, optional_accessory_model, repair_model, user_model
from util import com

from tornado import gen


class OAC_List(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        # 获取参数
        only_have = self.get_argument('only_have', True)
        if only_have in ['f', 'false', 'False']:
            only_have = False
        elif only_have <= 0:
            only_have = False
        else:
            only_have = True
        # 读取数据
        oac_list = yield optional_accessory_model.oac_list()
        oac_count_list = yield optional_accessory_model.oac_group_count()
        oac_count_dict = optional_accessory_model.oac_group_count_to_dict(oac_count_list)
        # 格式
        data = []
        for c in oac_list:
            category_id = c.get('id')
            count = oac_count_dict.get(category_id, 0)
            c['count'] = count
            if only_have and count <= 0:
                continue
            data.append(c)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))

class OA_List(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        # 获取参数
        category_id = com.safeInt(self.get_argument('category_id', 0))
        page = com.safeInt(self.get_argument('page', 1))
        count = com.safeInt(self.get_argument('count', 10))
        # 读取数据
        data = yield optional_accessory_model.oa_list(category_id, page, count)
        if data and data.get('data'):
            oa_list = data.get('data')
            oa_ids = [d.get('id') for d in oa_list]
            oa_quantity_dict = yield optional_accessory_model.oa_quantity_dict(oa_ids)
            for d in oa_list:
                d['quantity'] = oa_quantity_dict.get(d.get('id', 0), 0)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_OA_Add(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint_id = com.safeInt(self.get_argument("endpoint_id", ""), 0)
        sn = self.get_argument('order_sn', None)
        if not sn:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        if not isinstance(content, list) and not isinstance(content, tuple):
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        # 读取请求的配件
        pr_oa_from = yield optional_accessory_model.pr_oa_from_input(sn, content)
        # if len(pr_oa_from) == 0:
        #     raise gen.Return(self.error(u'数据为空', com.ERROR_PARAM))
        # 检查订单状态
        if endpoint_id and endpoint_id > 0:
            has_order_cache = yield endpoint_repair_model.check_order_cache_by_sn(uid, sn, endpoint_id)
            if not has_order_cache:
                raise gen.Return(self.error(u'无此单号信息，无法修改', com.ERROR_PARAM))
        else:
            validate, come_exp_type = yield repair_model.check_order_v1(uid, sn)
            if validate == -1:
                raise gen.Return(self.error(u'权限不足，无法操作', com.ERROR_BAD_STATE))
            if validate != 100:
                raise gen.Return(self.error(u'状态不符，无法添加订单自选配件', com.ERROR_BAD_STATE))
        # 检查配件数量
        flag_quantity = True
        oa_ids = [x.get('oa_id') for x in pr_oa_from]
        oa_quantity_dict = yield optional_accessory_model.oa_quantity_dict(oa_ids)
        ## 加上指定sn的数量，以支持修改的情况
        pr_oa_count_dict = yield optional_accessory_model.pr_oa_count_dict(sn)
        for oa_id, count in pr_oa_count_dict.items():
            if oa_quantity_dict.has_key(oa_id):
                oa_quantity_dict[oa_id] = oa_quantity_dict[oa_id] + count
        ## 检查配件数量
        for pr_oa in pr_oa_from:
            oa_id = pr_oa.get('oa_id')
            count = pr_oa.get('count')
            quant = oa_quantity_dict.get(oa_id)
            if count > quant:
                flag_quantity = False
                break
        if not flag_quantity:
            raise gen.Return(self.error(u'请求配件数量超过库存', com.ERROR_PARAM))
        # 操作数据库
        data = yield optional_accessory_model.pr_oa_add(sn, endpoint_id, pr_oa_from)
        ret = {
            'pr_oa_from': pr_oa_from,
            'oa_ids': oa_ids,
            'oa_quantity_dict': oa_quantity_dict,
            'flag_quantity': flag_quantity,
            'data': data,
        }
        raise gen.Return(self.success(True) if data else self.error(u'添加订单自选配件 发生错误', com.ERROR_BAD_STATE))

class PR_OA_Cancel_All(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', None)
        if not sn:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        order_info = yield repair_model.order_info(sn)
        validate, come_exp_type = yield repair_model.check_order_v1(uid, sn)
        if validate == -1:
            u_endpoint_id = yield user_model.get_endpoint_id_by_uid(uid)
            o_endpoint_id = order_info.get('endpoint', 0)
            if u_endpoint_id != o_endpoint_id:
                raise gen.Return(self.error(u'权限不足，无法操作', com.ERROR_BAD_STATE))
        o_status = order_info.get('status')
        if not (o_status >= 100 and o_status <= 500) and o_status not in [-300]:
            raise gen.Return(self.error(u'状态不符，无法取消订单自选配件', com.ERROR_BAD_STATE))
        data = yield optional_accessory_model.pr_oa_cancel_all(sn)
        raise gen.Return(self.success(True) if data else self.error(u'取消订单自选配件 发生错误', com.ERROR_BAD_STATE))

