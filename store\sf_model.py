# coding=utf-8

import tornado
from tornado import gen, httpclient
from tornado.log import app_log
from log import sf_log
import traceback
from conf.config import SF_HEAD, SF_CHECKCODE, SF_CUSTID, SF_VERIFYCODE, SF_HOST
import time
import datetime
from hashlib import md5
import json
from store import DB_PR, DB
import xmltodict
import urllib
import hashlib, base64
import sys
reload(sys)
sys.setdefaultencoding( "utf-8" )

HTTP_TIMEOUT = 5
MAX_CLIENTS = 1000


httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)


key = 'Web'    
secret = 'M4S8tUB8OBBvIUN7'



def verify_code(xml, verify_word):
    """
    构造签名
    :param xml:
    :param verify_word:
    :return:
    """
    # return SF_VERIFYCODE
    # s = xml + verify_word
    s = xml + 'nopDeBdqCQp0uUszvuK5jIWhOwovmK7K'
    s = s.encode('utf-8')
    # print s
    md = hashlib.md5(s).digest()
    # print md
    b64 = base64.b64encode(md)
    # print b64
    return b64


def create_request_xml(service, head, body):
    data = {
        'Request': {
            '@service': service,
            # '@lang':'zh-CN',
            'Head': head,
            'Body': body,
        },
    }
    return xmltodict.unparse(data)


def create_mail_json(data):
    key_need = [
        'orderid',
        'custid',
        'pay_method',
        'is_docall',
        'j_contact',
        'j_tel',
        'j_province',
        'j_city',
        'j_county',
        'j_address',
        'd_contact',
        'd_tel',
        'd_province',
        'd_city',
        'd_county',
        'd_address',
    ]
    value = dict()
    for i in key_need:
        if data.get(i):
            value[i] = data[i]
    default = {
        'need_return_tracking_no': "1",
        'express_type': "1",
    }
    value.update(default)
    order = {'@'+i: value.get(i) for i in value}
    cargo = list()
    c = dict()
    c['@'+'name'] = '电子产品'
    cargo.append(c)
    order['Cargo'] = cargo
    js = {
        'Order': order
    }
    return js

def create_route_json(data):
    key_need = [
        'tracking_number',
    ]
    value = dict()
    for i in key_need:
        if not data.get(i, None):
            return 'missing param:' + i
        else:
            value[i] = data[i]
    default = {
        'tracking_type':'2',
        # 'method_type': '1'
    }
    value.update(default)
    order = {'@'+i: value.get(i) for i in value}
    js = {
        'RouteRequest': order
    }
    return js


def check_create_order(xml):
    js = xmltodict.parse(xml)
    if js.get('Response') and js['Response'].get('Head') and js['Response']['Head'] == 'OK':
        if js['Response'].get('Body') and js['Response']['Body'].get('OrderResponse'):
            if js['Response']['Body']['OrderResponse'].get('@filter_result') and js['Response']['Body']['OrderResponse']['@filter_result'] == '2':
                return js['Response']['Body']['OrderResponse'].get('@mailno')
    return False


def deal_route(xml):
    key = [
        'remark',
        'accept_time',
        'accept_address',
        'opcode',
    ]
    js = xmltodict.parse(xml)
    ret = list()
    if js.get('Response') and js['Response'].get('Head') and js['Response']['Head'] == 'OK':
        if js['Response'].get('Body') and js['Response']['Body'].get('RouteResponse') and js['Response']['Body']['RouteResponse'].get('Route'):
            route = js['Response']['Body']['RouteResponse'].get('Route')
            if route and isinstance(route, list):
                for i in route:
                    d = {k: i.get('@'+k) for k in key}
                    d['readboy_sn'] = js['Response']['Body']['RouteResponse'].get('@orderid')
                    d['exp_sn'] = js['Response']['Body']['RouteResponse'].get('@mailno')
                    ret.append(d)
                return ret
            else:
                return None
    return None

@gen.coroutine
def create_order(data):
    """
    创建快递订单
    :param data: 快递信息
    :return: 快递单号
    """
    body = create_mail_json(data)
    if isinstance(body, str):
        raise gen.Return(Exception(body))
    # url = SF_HOST + '/api/order/placeOrder'
    url = 'http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService'
    client = httpclient.AsyncHTTPClient()
    service = 'OrderService'
    head = SF_HEAD
    xml = create_request_xml(service, head, body)
    param = {
        'xml': xml,
        'verifyCode': verify_code(xml, SF_VERIFYCODE),
        # 'checkCode': SF_CHECKCODE
    }
    body = urllib.urlencode(param)
    request = httpclient.HTTPRequest(url, method='POST', connect_timeout=HTTP_TIMEOUT, body=body,
                                  request_timeout=HTTP_TIMEOUT, validate_cert=False)
    ret = None
    try:
        response = yield client.fetch(request)
        resp_xml = response.body
        ret = check_create_order(resp_xml)
        sf_log.info(resp_xml)
    except:
        sf_log.error("Exception in exception handler: url=%s" % url, exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def mail_route(data):
    body = create_route_json(data)
    if isinstance(body, str):
        raise gen.Return(None)
    # url = SF_HOST + '/api/order/getRoute'
    url = 'http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService'
    client = httpclient.AsyncHTTPClient()
    service = 'RouteService'
    head = SF_HEAD
    xml = create_request_xml(service, head, body)
    param = {
        'xml': xml,
        'verifyCode': verify_code(xml, SF_VERIFYCODE),
        # 'checkCode': SF_CHECKCODE
    }
    body = urllib.urlencode(param)
    request = httpclient.HTTPRequest(url, method='POST', connect_timeout=HTTP_TIMEOUT, body=body,
                                  request_timeout=HTTP_TIMEOUT, validate_cert=False)
    ret = None
    try:
        response = yield client.fetch(request)
        resp_xml = response.body
        ret = deal_route(resp_xml)
        sf_log.info(resp_xml)
    except:
        sf_log.error("Exception in exception handler: url=%s" % url, exc_info=True)
    raise gen.Return(ret)


def push_route(xml):
    """
    处理推送路由xml
    :param xml:
    :return:
    """
    js = xmltodict.parse(xml)
    ret = list()
    if js.get('Request') and js['Request'].get('Body') and js['Request']['Body'].get('WaybillRoute'):
            route = js['Request']['Body']['WaybillRoute']
            if route:
                if isinstance(route, list):
                    for i in route:
                        d = dict()
                        d['remark'] = i.get('@remark')
                        d['accept_time'] = i.get('@acceptTime')
                        d['accept_address'] = i.get('@acceptAddress')
                        d['opcode'] = i.get('@opCode')
                        d['exp_sn'] = i.get('@mailno')
                        d['readboy_sn'] = i.get('@orderid')
                        ret.append(d)
                    return ret
                if isinstance(route, dict):
                    d = dict()
                    i = route
                    d['remark'] = i.get('@remark')
                    d['accept_time'] = i.get('@acceptTime')
                    d['accept_address'] = i.get('@acceptAddress')
                    d['opcode'] = i.get('@opCode')
                    d['exp_sn'] = i.get('@mailno')
                    d['readboy_sn'] = i.get('@orderid')
                    ret.append(d)
                    return ret
            else:
                return None
    return None


def filter_order_json(data):
    if not data or not isinstance(data, dict):
        return None
    key = [
        'province',
        'city',
        'district',
        'address',
        'd_province',
        'd_city',
        'd_district',
        'd_address',
    ]
    if any(data.get(i) is None for i in key):
        return None
    js = {
        'OrderFilter':{
            'OrderFilterOption':{
                '@province': data.get('province'),
                '@city': data.get('city'),
                '@county': data.get('district'),
                '@j_address': data.get('address'),
                '@d_province': data.get('d_province'),
                '@d_city': data.get('d_city'),
                '@d_county': data.get('d_district'),
            },
            '@d_address': data.get('d_address')
        }
    }
    return js


def deal_filter(xml):
    js = xmltodict.parse(xml)
    if js.get('Response') and js['Response'].get('Head') and js['Response']['Head'] == 'OK':
        if js['Response'].get('Body') and js['Response']['Body'].get('OrderFilterResponse'):
            if js['Response']['Body']['OrderFilterResponse'].get('@filter_result') \
                    and js['Response']['Body']['OrderFilterResponse']['@filter_result'] <= '2' \
                    and js['Response']['Body']['OrderFilterResponse'].get('@origincode') \
                    and js['Response']['Body']['OrderFilterResponse'].get('@destcode'):
                return 1
    return -1

@gen.coroutine
def filter_order(data):
    """
    验证快递能否收派
    :param data:
    :return:
    """
    body = filter_order_json(data)
    if not body:
        raise gen.Return(0)
    # url = SF_HOST + '/api/order/orderFilter'
    url = 'http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService'
    client = httpclient.AsyncHTTPClient()
    service = 'OrderFilterService'
    head = SF_HEAD
    xml = create_request_xml(service, head, body)
    param = {
        'xml': xml,
        'verifyCode': verify_code(xml, SF_VERIFYCODE),
        # 'checkCode': SF_CHECKCODE
    }
    body = urllib.urlencode(param)
    request = httpclient.HTTPRequest(url, method='POST', connect_timeout=HTTP_TIMEOUT, body=body,
                                  request_timeout=HTTP_TIMEOUT, validate_cert=False)
    ret = -1
    try:
        response = yield client.fetch(request)
        resp_xml = response.body
        ret = deal_filter(resp_xml)
        sf_log.info(resp_xml)
    except:
        sf_log.error("Exception in exception handler: url=%s" % url, exc_info=True)
    raise gen.Return(ret)

