# encoding=utf-8
# __author__ = 'lch'

from store import DB_PR
# import util.com as com
from tornado import gen
import traceback
from conf.config import APP_SECRET, WXPAY_APPID


# @gen.coroutine
# def getAppAuth(appid):
#     cur = yield DB_PR.execute('Select appid, appkey, status from app_auth where appid=%s', appid)
#     auth = cur.fetchone()
#     raise gen.Return(auth)


@gen.coroutine
def getAppAuth(appid):
    auth = None
    app_key = APP_SECRET.get(appid)
    if app_key:
        auth = {'appid': appid, 'appkey':app_key, 'status':0}
    raise gen.Return(auth)


@gen.coroutine
def listAppAuth():
    cur = yield DB_PR.execute('select id, appid, name, platform, description, status, created_at from app_auth order by created_at')
    auths = cur.fetchall()
    for auth in auths:
        auth['created_at'] = auth['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    raise gen.Return(auths)


@gen.coroutine
def getAppAuthDetail(appid):
    cur = yield DB_PR.execute('Select id, appid, appkey, name, platform, description, status, created_at from app_auth where appid=%s', appid)
    auth = cur.fetchone()
    if auth:
        auth['created_at'] = auth['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    raise gen.Return(auth)


@gen.coroutine
def addAppAuth(appauth):
    fields = ['appid','appkey','name','platform','description','status']
    fields_sql = ','.join(fields)
    holder_sql = ','.join(['%s']*len(fields))
    params = tuple([appauth[f] for f in fields])
    try:
        cur = yield DB_PR.execute('insert into app_auth ('+fields_sql+') values ('+holder_sql+')', params)
    except Exception as e:
        traceback.print_exc()
        raise gen.Return((False, e.message))
    raise gen.Return((True, None))


@gen.coroutine
def delAppAuth(appid):
    cur = yield DB_PR.execute('delete from app_auth where appid=%s', appid)
    if cur.rowcount > 0:
        raise gen.Return(True)
    raise gen.Return(False)
