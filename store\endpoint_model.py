# coding=utf-8

import tornado
from tornado import gen
from tornado.log import app_log
import traceback
import time
import datetime
import json
import math

from store import DB
import conf.config as config
import util.coords as coords

IMG_HOST = 'https://dt.readboy.com/'

HTTP_TIMEOUT = 2
MAX_CLIENTS = 1000

ENDPOINT_OPTIONS = {
    # need read from db
    # "channel_type": [
    #     {
    #         "id": 1,
    #         "name": "商超"
    #     },
    #     {
    #         "id": 2,
    #         "name": "书店"
    #     }
    # ],
    "channel_level": [
        {
            "id": 1,
            "name": "省级"
        },
        {
            "id": 2,
            "name": "市级"
        },
        {
            "id": 3,
            "name": "县级"
        }
    ],
    "position_priority": [
        "1号位",
        "2号位",
        "3号位",
        "4号位",
    ],
    "competitor_brand": [
        '步步高',
        '好记星',
        '优学派',
        '快易典',
    ]
}



@gen.coroutine
def userEndpoint(uid):
    cur = yield DB.execute('Select ep.id, ep.name, ep.address, ep.phone, ep.manager, ep.top_agency from user_endpoint uep LEFT JOIN endpoint ep '+
                           'on uep.endpoint=ep.id where uep.uid=%s and ep.status=1', uid)
    ep = cur.fetchone()
    raise gen.Return(ep)


@gen.coroutine
def getOptions():
    global ENDPOINT_OPTIONS
    if 'channel_type' not in ENDPOINT_OPTIONS:
        cur = yield DB.execute('select id, name from endpoint_channel')
        channel_types = cur.fetchall()
        ENDPOINT_OPTIONS['channel_type'] = channel_types
    raise gen.Return(ENDPOINT_OPTIONS)


@gen.coroutine
def getImage(endpoint):
    cur1, cur2 = yield [
        DB.execute('select ep.name, ep.manager, ep.phone, ep.images, ep.lng, ep.lat, ext.* from endpoint ep left join endpoint_extend ext on ep.id=ext.endpoint where ep.id=%s limit 1', endpoint),
        DB.execute('select id, brand, position_priority, pics from endpoint_compete_brand where endpoint=%s', endpoint)
    ]
    info = cur1.fetchone()
    if info:
        del info['id']
        del info['created_at']
        del info['updated_at']
        info['endpoint'] = endpoint
        info['images'] = _loadImages(info['images'])
        competitors = cur2.fetchall()
        info['competitors'] = competitors
        if competitors:
            for competitor in competitors:
                competitor['pics'] = _loadImages(competitor['pics'])
    raise gen.Return(info)


@gen.coroutine
def getNewImage(endpoint):
    cur1 = yield DB.execute('select ep.name, ep.manager, ep.phone, ext.* from endpoint_audit ext left join endpoint ep on ep.id=ext.endpoint where ext.endpoint=%s and ext.status!=2 limit 1', endpoint)
    info = cur1.fetchone()
    if info:
        create_date = info['created_at']
        update_date = info['updated_at']
        agency_audit_date = info['top_agency_audited_at']
        manager_audit_date = info['manager_audited_at']
        info['created_at'] = create_date.strftime('%Y-%m-%d %H:%M:%S') if create_date else None
        info['updated_at'] = update_date.strftime('%Y-%m-%d %H:%M:%S') if update_date else None
        info['top_agency_audited_at'] = agency_audit_date.strftime('%Y-%m-%d %H:%M:%S') if agency_audit_date else None
        info['manager_audited_at'] = manager_audit_date.strftime('%Y-%m-%d %H:%M:%S') if manager_audit_date else None
        info['images'] = _loadImages(info['images'])

        cur2 = yield DB.execute('select brand, position_priority, pics from endpoint_audit_compete_brand where audit_id=%s', info['id'])
        competitors = cur2.fetchall()
        info['competitors'] = competitors
        if competitors:
            for competitor in competitors:
                competitor['pics'] = _loadImages(competitor['pics'])
    raise gen.Return(info)


def _loadImages(images):
    rets = []
    if images:
        imgs = json.loads(images)
        rets = [IMG_HOST+img for img in imgs]
    return rets


def _dumpImages(images):
    rets = []
    for img in images:
        if img.startswith(config.OSS_CNAME):
            ret = img[len(config.OSS_CNAME):]
            rets.append(ret)
        else:
            raise Exception('invalid image url='+img)
    return json.dumps(rets)


@gen.coroutine
def saveNewImage(info):
    ok = 1
    err = 'success'
    trans = yield DB.begin()
    try:
        img_id = yield _saveExtend(trans, info)
        yield _saveCompetitors(trans, info, img_id)
        yield trans.commit()
    except Exception as e:
        yield trans.rollback()
        traceback.print_exc()
        ok = 0
        err = e.message
    raise gen.Return((ok, err))


@gen.coroutine
def _saveExtend(trans, info):
    id = info.get('id')
    endpoint = info.get('endpoint')
    # lng, lat = float(info['lng']), float(info['lat'])
    # blng, blat = coords.gcj02tobd09(lng, lat) if lng>0 and lat>0 else (0, 0)
    # info['blng'] = round(blng, 6)
    # info['blat'] = round(blat, 6)
    fields = ['endpoint','lng','lat','position_priority','area','sales','channel_level','channel_type','images']
    if id:
        m = {k+'=%s':info[k] for k in fields if k in info}
        m['updated_at=%s'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        m['status=%s'] = 0
        m['top_agency_opinion=%s'] = None
        m['top_agency_audited_at=%s'] = None
        m['manager_opinion=%s'] = None
        m['manager_audited_at=%s'] = None
        images = info.get('images')
        if images is not None and type(images) is list:
            m['images=%s'] = _dumpImages(images)
        if len(m)>0:
            upd_sql = ','.join(m.keys())
            params = tuple(m.values())+(id,)
            sql = 'Update endpoint_audit set '+upd_sql+' where id=%s AND status<=0'
            cur = yield trans.execute(sql, params)
            if cur.rowcount <= 0:
                raise Exception(u'形象变更申请不存在或正在审批中，无法修改')
    elif endpoint:
        m = {k: info[k] for k in fields if k in info}
        images = info.get('images')
        if images is not None and type(images) is list:
            m['images'] = _dumpImages(images)
        fields_sql = ','.join(m.keys())
        values_sql = ','.join(['%s as '+k for k in m.keys()])
        sql = 'Insert into endpoint_audit ('+fields_sql+\
              ') select * from (select '+values_sql+\
              ') as tmp where not exists (select id from endpoint_audit where endpoint=%s and status!=2) limit 1'
        params = tuple(m.values())+(endpoint,)
        cur = yield trans.execute(sql, params)
        if cur.lastrowid > 0:
            id = cur.lastrowid
        else:
            raise Exception(u'形象变更申请正在等待审批，无法创建新的申请')
    else:
        raise Exception(u'缺少参数【变更申请编号】或【终端编号】')
    raise gen.Return(id)


@gen.coroutine
def _saveCompetitors(trans, info, audit_id):
    if 'competitors' in info:
        audit = info.get('id', audit_id)
        if 'id' in info and audit:
            yield trans.execute('Delete from endpoint_audit_compete_brand where audit_id=%s', audit)
        competitors = info.get("competitors")
        if competitors and isinstance(competitors, list):
            fields = ['audit_id', 'brand', 'position_priority', 'pics']
            values = []
            params = []
            for competitor in competitors[:5]:
                values.append('('+','.join(['%s']*len(fields))+')')
                params += [audit, competitor['brand'], competitor['position_priority'], _dumpImages(competitor.get('pics', []))]
            values_sql = ','.join(values)
            fields_sql = ','.join(fields)
            sql = 'Insert into endpoint_audit_compete_brand ('+fields_sql+') VALUES '+values_sql
            yield trans.execute(sql, tuple(params))
    pass


@gen.coroutine
def findByDistrict(city, district=None, type=1):
    """
    type: 1-售前，2-售后
    """
    fields = 'name, phone, address, city, lng, lat, blng, blat, is_pre_sale, is_after_sale'
    if type == 1:
        if district:
            cur = yield DB.execute('select ' + fields + ' from endpoint where city=%s and district=%s and status=1 and is_pre_sale=1',
                                   (city, district))
        else:
            cur = yield DB.execute('select '+fields+' from endpoint where city=%s and status=1 and is_pre_sale=1', city)
    elif type == 2:
        if district:
            cur = yield DB.execute('select '+fields+' from endpoint where city=%s and district=%s and status=1 and is_after_sale=1',
                                   (city, district))
        else:
            cur = yield DB.execute('select '+fields+' from endpoint where city=%s and status=1 and is_after_sale=1', city)
    else:
        if district:
            cur = yield DB.execute('select ' + fields + ' from endpoint where city=%s and district=%s and status=1',
                                   (city, district))
        else:
            cur = yield DB.execute('select '+fields+' from endpoint where city=%s and status=1', city)
    infos = cur.fetchall()
    raise gen.Return(infos)


@gen.coroutine
def findByLoc(lng, lat, type=1, count=10):
    """
    type: 1-售前，2-售后
    """
    R = 10 # 半径（km）
    r = 180 / math.pi * R / 6372.797    # 里面的 1 就代表搜索 1km 之内，单位km
    lngR = r / math.cos(lat * math.pi / 180.0)
    maxLat = lat + r
    minLat = lat - r
    maxLng = lng + lngR
    minLng = lng - lngR

    fields = 'name, phone, address, city, lng, lat, blng, blat, is_pre_sale, is_after_sale'
    if type == 1:
        cur = yield DB.execute('select '+fields+' from endpoint where ((lat BETWEEN %s AND %s) AND (lng BETWEEN %s AND %s)) and status=1 and is_pre_sale=1 limit %s', (minLat, maxLat, minLng, maxLng, count))
    elif type == 2:
        cur = yield DB.execute('select '+fields+' from endpoint where ((lat BETWEEN %s AND %s) AND (lng BETWEEN %s AND %s)) and status=1 and is_after_sale=1 limit %s', (minLat, maxLat, minLng, maxLng, count))
    else:
        cur = yield DB.execute('select '+fields+' from endpoint where ((lat BETWEEN %s AND %s) AND (lng BETWEEN %s AND %s)) and status=1 limit %s', (minLat, maxLat, minLng, maxLng, count))

    infos = cur.fetchall()
    for info in infos:
        info['distance'] = coords.distance(lat, lng, float(info['lat']), float(info['lng']))
    eps = sorted(infos, key=lambda i: i['distance'])
    raise gen.Return(eps)


@gen.coroutine
def notifications(ts=0, count=10):
    ts = 0 if ts <= 0 else ts
    start = datetime.datetime.fromtimestamp(ts).strftime('%Y-%m-%d %H:%M:%S')
    cur = yield DB.execute('SELECT id,content,title,author,created_at '+
                           'from endpoint_notice '+
                           'where created_at>%s '+
                           'order by created_at desc '+
                           'limit %s',
                           (start, count+1))
    cards = cur.fetchall()
    if not cards:
        raise gen.Return(None)
    l = len(cards)
    isEnd = l < count+1
    size = l if isEnd else count
    data = cards[0:size]
    for card in data:
        card['created_at'] = card['created_at'].strftime('%Y.%m.%d %H:%M:%S')
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)


@gen.coroutine
def get_endpoint_info(endpoint):
    sql = 'select e.`name`, e.top_agency, e.second_agency, e.is_direct_sales, a.channel as endpoint_channel from ' \
          'endpoint e left join agency a on e.top_agency = a.id where e.id = %s'
    cur = yield DB.execute(sql, endpoint)
    data = cur.fetchone()
    raise gen.Return(data)