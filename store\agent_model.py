# encoding=utf-8
# __author__ = 'qry'
import json
import random

from tornado import gen
from tornado.log import app_log
from cache import agent_cache
from store import DB_PR, repair_model, work_wechat_model
from conf import config
import datetime

def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')


def _changeTS(data, fields):
    """
    转换时间戳
    :param data: 数据
    :param fields: 需要转换时间戳的字段
    :return:
    """
    for i in fields:
        if data.get(i):
            data[i] = data[i].strftime('%Y-%m-%d %H:%M:%S')
    return data

def _unique_sn(sn=None, t=0):
    """
    生成唯一流水号字符串
    :param sn: 已有流水号生成关联号
    :param t: 关联类型，1寄来快递单，2寄去快递单
    :return:
    """
    if sn and t:
        s = sn + '%04d' % t
        return s
    else:
        ts = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        rand = random.randint(100000, 999999)
        s = ts + str(rand)
        return s


def _image_to_str(data, fields):
    """
    数据中的文件数组转换成字符串
    :param data: 数据
    :param fields: 要转换的字段
    :return:
    """
    for i in fields:
        if data.get(i) and isinstance(data[i], list):
            rets = list()
            for img in data[i]:
                if img.startswith(config.OSS_CNAME):
                    ret = img[len(config.OSS_CNAME):]
                    rets.append(ret)
            s = json.dumps(rets)
            data[i] = s
        else:
            data[i] = ''
    return data


def _image_to_json(data, fields):
    """
    数据中的文件字符串转换成json数组
    :param data: 数据
    :param fields: 要转换的字段
    :return:
    """
    if data:
        for i in fields:
            rets = []
            if data.get(i):
                imgs = json.loads(data[i])
                rets = [config.OSS_CNAME + img for img in imgs]
            data[i] = rets
    return data


@gen.coroutine
def get_pseudo_code(uid, model_id, model_name):
    """
    获取伪码
    :param uid: 用户id
    :param model_id: 机型id
    :param model_name: 机型名称
    :return:
    """
    number = agent_cache.get_pseudo_code()
    created_at = _now_date()
    if not number:
        number = 10000001
    number = int(number) + 1
    pseudo_code = 'PC' + str(number)
    ret = None
    try:
        param = tuple([uid, pseudo_code, model_id, model_name, created_at])
        yield DB_PR.execute('insert into pseudo_code (uid, barcode, model_id, model_name, created_at) values(%s,%s,%s,%s,%s)', param)
        agent_cache.set_pseudo_code(number)
        ret = pseudo_code
    except:
        app_log.error('get pseudo error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_order_cache(uid, barcode, agent_order_sn):
    """
    查看是否有保存的订单  即缓存
    :param agent_order_sn:
    :param uid:
    :param barcode: 条码
    :return:
    """
    sql = 'select count(*) as count from order_cache aoc right join agent_order_correlation_cache aocc ' \
          'on aoc.sn = aocc.order_sn where aoc.uid = %s and aoc.barcode = %s and aocc.agent_order_sn = %s ' \
          'and aoc.submit_status = 0 and aoc.type = 1'
    cur = yield DB_PR.execute(sql, (uid, barcode, agent_order_sn))
    data = cur.fetchone()
    # print data
    if data and data['count'] > 0:
        raise gen.Return(True)
    raise gen.Return(False)


@gen.coroutine
def modify_agent_repair(uid, sn, data):
    data = _image_to_str(data, ['period_file', 'upload_file', 'video_file'])
    sql = 'update order_cache set damage = %s, period_file = %s, upload_file = %s, video_file = %s, description = %s,' \
          ' attachment = %s, updated_at = %s '
    param = [data.get('damage'), data.get('period_file'), data.get('upload_file'), data.get('video_file'),
             data.get('description'), data.get('attachment'), _now_date()]
    if data.get('external_fault'):
        sql = sql + ',external_fault=%s '
        param.append(data.get('external_fault'))
    if data.get('internal_fault'):
        sql = sql + ',internal_fault=%s '
        param.append(data.get('internal_fault'))
    sql = sql + ' where uid = %s and sn = %s and type = 1'
    param = param + [uid, sn]
    ret = None
    try:
        yield DB_PR.execute(sql, param)
        ret = True
    except:
        app_log.error('update order_cache error')
    raise gen.Return(ret)


@gen.coroutine
def check_order_cache_by_sn(uid, sn):
    """
    查看是否有保存的订单  即缓存
    :param uid:
    :param sn: 单号
    :return:
    """
    sql = 'select count(*) as count from order_cache where uid = %s and sn = %s and submit_status = 0'
    cur = yield DB_PR.execute(sql, (uid, sn))
    data = cur.fetchone()
    if data and data['count'] > 0:
        raise gen.Return(True)
    raise gen.Return(False)


@gen.coroutine
def check_pseudo_code(barcode, uid):
    """
    获取伪码机型
    :param barcode: 条码
    :param uid: 用户id
    :return:
    """
    sql = 'select model_name, status from pseudo_code where barcode = %s and uid = %s'
    cur = yield DB_PR.execute(sql, (barcode, uid))
    data = cur.fetchone()
    raise gen.Return(data)

@gen.coroutine
def check_model_id_pseudo_code(barcode):
    """
    获取伪码机型
    :param barcode: 条码
    :return:
    """
    if barcode:
        param = [barcode]
        sql = 'select model_name, model_id, status from pseudo_code where barcode = %s'
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data:
            raise gen.Return(data)
    raise gen.Return(None)


@gen.coroutine
def save_repair(uid, data, agent_order_sn):
    """
    提交订单
    :param agent_order_sn:
    :param uid: 用户id
    :param data: 订单数据
    :return:
    """

    if not uid or not isinstance(data, dict):
        raise gen.Return(False)
    created_at = _now_date()
    # 将数据预处理
    data['uid'] = uid
    data['sn'] = _unique_sn()
    data['created_at'] = created_at
    data['status'] = 100
    data['come_exp_type'] = 2
    data['type'] = 1
    data = _image_to_str(data, ['period_file', 'upload_file', 'video_file'])
    key = [
        'barcode',
        'imei',
        'model_name',
        'color',
        'model_id',
        'serial',
        'in_period',
        'has_warranty',
        'in_si_period',
        'has_screen_insurance',
        'reason',
        'damage',
        'period_file',
        'upload_file',
        'video_file',
        'description',
        'repair_endpoint',
        'uid',
        'created_at',
        'sn',
        'status',
        'repeat_order',
        'attachment',
        'come_exp_type',
        'type',
        'is_pseudo_code'
    ]
    if data.get('external_fault'):
        key.append('external_fault')
    if data.get('internal_fault'):
        key.append('internal_fault')
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    param_insert = tuple([data.get(i) for i in key])
    sql = 'insert into `order_cache` (' + fields + ') values (' + values + ')'
    ret = None
    trans = yield DB_PR.begin()
    try:
        if not agent_order_sn:
            agent_order_sn = _unique_sn()
            param = tuple([uid, agent_order_sn, 1, created_at])
            # 大单处理
            yield trans.execute(
                'insert into agent_order (uid, sn, type, created_at) values(%s,%s,%s,%s)', param)
        # 插入大单-小单关联表
        yield trans.execute('insert into agent_order_correlation_cache (agent_order_sn, order_sn, created_at) '
                            'values(%s,%s,%s)', (agent_order_sn, data['sn'], created_at))
        # 小单缓存
        yield trans.execute(sql, param_insert)
        trans.commit()
        ret = {
            'agent_order_sn': agent_order_sn,
            'order_sn': data['sn']
        }
    except Exception as e:
        trans.rollback()
        app_log.info('add order_agent insert error:', exc_info=True)
        raise gen.Return(ret)
    raise gen.Return(ret)


@gen.coroutine
def get_order(uid, agent_order_sn):
    """
    获取缓存数据
    :param agent_order_sn:
    :param uid:
    :return:
    """
    sql = 'select aoc.uid,aoc.sn, aoc.`status`, aoc.barcode, aoc.imei, aoc.model_name, aoc.color, ' \
          'aoc.model_id, aoc.serial, aoc.in_period, aoc.has_warranty, aoc.in_si_period, aoc.has_screen_insurance, ' \
          'aoc.reason, aoc.damage, aoc.period_file, aoc.upload_file, aoc.video_file, aoc.description, ' \
          'aoc.repair_endpoint, aoc.repeat_order, aoc.created_at, aoc.external_fault, aoc.internal_fault, ' \
          'aoc.attachment, aoc.come_exp_type, aoc.is_pseudo_code from order_cache aoc ' \
          'RIGHT JOIN agent_order_correlation_cache ao ' \
          'ON aoc.sn = ao.order_sn WHERE ao.agent_order_sn = %s and aoc.uid = %s and aoc.submit_status = 0 and ' \
          'aoc.type = 1'
    cur = yield DB_PR.execute(sql, (agent_order_sn, uid))
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def add_agent_order(datas):
    """
    提交订单
    :param datas:
    :return:
    """
    trans = yield DB_PR.begin()
    # 大单流水号
    sn = datas['agent_order_sn']
    # datas['type'] = 2
    created_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    key = [
        'barcode',
        'imei',
        'model_name',
        'color',
        'model_id',
        'serial',
        'in_period',
        'has_warranty',
        'in_si_period',
        'has_screen_insurance',
        'reason',
        'damage',
        'period_file',
        'upload_file',
        'video_file',
        'description',
        'repair_endpoint',
        'uid',
        'created_at',
        'sn',
        'status',
        'repeat_order',
        'type',
        'attachment',
        'come_exp_type',
    ]
    fields = ','.join(key) + ', name, phone, province, city, district, address, agency'
    values = ','.join(['%s'] * len(key)) + ',%s,%s,%s,%s,%s,%s,%s'
    # print values
    sql = 'insert into `order` (' + fields + ') values (' + values + ')'
    # print sql
    ret = None
    try:
        param_agent_order = tuple([datas['province'], datas['city'], datas['district'], datas['address'],
                                   datas['agency'], datas['name'], datas['phone'], created_at, sn])
        yield trans.execute('update agent_order set status = 1, province = %s, city = %s, district = %s, address = %s, '
                            'agency = %s, name = %s, phone = %s, updated_at = %s where sn = %s and type = 1',
                            param_agent_order)
        for data in datas['order']:
            data['type'] = 2
            param = tuple([data.get(i) for i in key] +
                          [datas['name'], datas['phone'], datas['province'], datas['city'],
                           datas['district'], datas['address'], datas['agency']])
            # print param
            yield trans.execute('update pseudo_code set `status` = 1 where barcode = %s', data['sn'])
            # 提交小单
            yield trans.execute(sql, param)
            yield trans.execute('insert into order_extend (sn, is_pseudo_code, external_fault, '
                                'internal_fault, created_at , is_tell) values (%s, %s, %s, %s, %s , %s)',
                                (data['sn'], data['is_pseudo_code'], data['external_fault'], data['internal_fault'],
                                 data['created_at'],datas.get('is_tell' , 0) ))

            # 更新缓存状态
            yield trans.execute('update order_cache set submit_status = 1, updated_at = %s where sn = %s '
                                'and submit_status = 0 and type = 1 and uid = %s', (created_at, data.get('sn'), data.get('uid')))
            # 插入大单-小单关联表
            yield trans.execute('insert into agent_order_correlation (agent_order_sn, order_sn, created_at) '
                                'values(%s,%s,%s)', (sn, data.get('sn'), created_at))
            # 添加日志
            # param = tuple([data.get('sn'), 100, 100, 'web', '', 'create_order', data['uid'], 0,
            #                '寄修订单提交，寄修服务中心等待接收中', '', created_at])
            # yield trans.execute('insert into order_log (pr_sn, pr_status, log_status, log_from, relation_key, '
            #                     'operation, uid, admin, title, remark, date) values '
            #                     '(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)', param)
            log = {
                'pr_sn': data.get('sn', ''),
                'pr_status': data.get('status', 0),
                'log_status': data.get('status', 0),
                'log_from': data.get('log_from', 'web'),
                'relation_key': data.get('relation_key', ''),
                'operation': data.get('operation', 'create_order'),
                'uid': data.get('uid', 0),
                'admin': data.get('admin', 0),
                'title': data.get('title', '寄修订单提交，寄修服务中心等待接收中'),
                'remark': data.get('remark', ''),
                'date': data.get('date', _now_date()),
            }
            repair_model.add_log(log)
            ret = True
        trans.commit()
    except:
        ret = None
        app_log.error('add agent_order error', exc_info=True)
        trans.rollback()
    raise gen.Return(ret)


@gen.coroutine
def check_order(uid, sn):
    """
    检查用户是否有权限操作订单
    :param uid: 用户id
    :param sn: 订单流水号
    :return:
    """
    sql = 'SELECT sum(o.`status` = 200 or o.`status` = -300) as count, ' \
          'sum(o.`status` = 100) as check_count FROM agent_order_correlation aoc ' \
          'LEFT JOIN `order` o ON aoc.order_sn = o.sn ' \
          'LEFT JOIN agent_order ao ON aoc.agent_order_sn = ao.sn ' \
          ' WHERE ao.uid = %s and aoc.agent_order_sn = %s'
    cur = yield DB_PR.execute(sql, (uid, sn))
    data = cur.fetchone()
    # 有待审核订单
    if data.get('check_count') > 0:
        raise gen.Return(-1)

    if data.get('count') > 0:
        raise gen.Return(1)
    else:
        raise gen.Return(0)


@gen.coroutine
def get_order_sn(uid, sn):
    """
    获取子订单流水号
    :param uid: 用户id
    :param sn: 大单流水号
    :return:
    """
    sql = 'SELECT o.sn FROM `order` o LEFT JOIN agent_order_correlation aoc ON o.sn = aoc.order_sn ' \
          ' right join agent_order ao on aoc.agent_order_sn = ao.sn WHERE  ao.uid= %s and aoc.agent_order_sn ' \
          '= %s and (o.`status` = 200 or o.`status` = -300)'
    cur = yield DB_PR.execute(sql, (uid, sn))
    data = cur.fetchall()
    raise gen.Return(data)

@gen.coroutine
def change_order(sn, data, order_sn):
    """
    用户更新自己订单数据
    :param order_sn: 子订单流水号
    :param sn: 大订单流水号
    :param data: 修改的数据
    :return:
    """
    data['updated_at'] = _now_date()
    data['sn'] = sn
    data['status'] = 300
    created_at = _now_date()
    key_total = ['come_exp_type', 'come_exp_com', 'come_exp_sn', 'updated_at', 'sn']
    sql_total = 'update `agent_order` set come_exp_type=%s, come_exp_com=%s, come_exp_sn=%s, updated_at=%s where sn=%s'

    sql_order = 'update `order` set come_exp_type=%s, come_exp_com=%s, come_exp_sn=%s, updated_at=%s, status=%s ' \
                'where sn=%s'

    param = tuple([data.get(i) for i in key_total])
    trans = yield DB_PR.begin()
    try:
        # 大单处理
        cur = yield trans.execute(sql_total, param)
        # 小单处理
        for i in order_sn:
            # print i
            param = tuple([data.get('come_exp_type'), data.get('come_exp_com'), data.get('come_exp_sn'),
                           data.get('updated_at'), 300, i.get('sn')])
            # print param
            yield trans.execute(sql_order, param)
            # 添加日志
            # param = tuple([i.get('sn'), 300, 300, 'app', '', 'exp_come_sure', data.get('uid'), 0,
            #                '寄修产品发货成功，维修中心等待接收中', '', created_at])
            # yield trans.execute('insert into order_log (pr_sn, pr_status, log_status, log_from, relation_key, '
            #                     'operation, uid, admin, title, remark, date) values '
            #                     '(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)', param)
            log = {
                'pr_sn': i.get('sn', ''),
                'pr_status': 300,
                'log_status': 300,
                'log_from':  'app',
                'relation_key': '',
                'operation': 'exp_come_sure',
                'uid': data.get('uid', 0),
                'admin': data.get('admin', 0),
                'title': data.get('title', '寄修产品发货成功，维修中心等待接收中'),
                'remark': data.get('remark', ''),
                'date': data.get('date', _now_date()),
            }
            repair_model.add_log(log)
        trans.commit()
    except:
        trans.rollback()
        app_log.error('update order error', exc_info=True)
        raise gen.Return(False)
    raise gen.Return(True)


@gen.coroutine
def total_order(uid, order_sn, order_type, barcode, status, start, end):
    """
    大单获取
    :param uid: 用户id
    :param order_sn: 订单流水号
    :param order_type: 类型：1--大单  2--小单
    :param barcode: 条码
    :param status: 子订单状态
    :param start: 起始时间
    :param end: 结束时间
    :return:
    """
    param = [uid]
    sql = 'SELECT distinct ao.id, ao.sn as agent_order_sn, ao.created_at, COUNT(aoc.order_sn) as count' \
          ' FROM agent_order ao ' \
          'RIGHT JOIN agent_order_correlation aoc ON ao.sn = aoc.agent_order_sn ' \
          'RIGHT JOIN `order` o ON aoc.order_sn = o.sn ' \
          'WHERE ao.uid = %s and ao.status = 1 and ao.type = 1 '
    # sn或者status
    if barcode and status:
        sql = sql + ' and o.barcode = %s and o.status = %s'
        param.append(barcode)
        param.append(status)
    elif barcode:
        sql = sql + ' and o.barcode = %s'
        param.append(barcode)
    elif status:
        sql = sql + ' and o.status = %s'
        param.append(status)
    else:
        sql = 'SELECT distinct ao.id, ao.sn as agent_order_sn, ao.created_at, COUNT(aoc.order_sn) as count ' \
              'FROM agent_order ao ' \
              'RIGHT JOIN agent_order_correlation aoc ON ao.sn = aoc.agent_order_sn ' \
              'WHERE ao.type = 1 and ao.uid = %s '
    if order_sn and order_type == 1:
        sql = sql + ' and ao.sn = %s'
        param.append(order_sn)
    if order_sn and order_type == 2:
        sql = sql + ' and aoc.order_sn = %s'
        param.append(order_sn)

    if start and end:
        sql = sql + '  AND ao.created_at BETWEEN %s AND %s'
        param.append(start)
        param.append(end)

    sql = sql + ' GROUP BY ao.id order by ao.created_at desc'
    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchall()
    # print data
    if not data:
        raise gen.Return([])
    for i in data:
        # sql = 'select count(*) as count from agent_order_correlation aoc where agent_order_sn = %s'
        # cur2 = yield DB_PR.execute(sql, i['agent_order_sn'])
        # data2 = cur2.fetchone()
        # i['count'] = data2.get('count')
        i = _changeTS(i, ['created_at'])
    raise gen.Return(data)


@gen.coroutine
def sub_order(uid, order_sn, order_type, barcode, status, start, end):
    """

    :param end:
    :param start:
    :param uid: 用户id
    :param order_sn:    大单单号
    :param order_type:  类型：1--大单  2--小单
    :param barcode: 条码
    :param status: 状态
    :return:
    """
    param = [uid]
    sql = 'SELECT aoc.agent_order_sn, o.id, o.model_name, o.sn as order_sn, o.barcode, o.damage, o.in_period, ' \
          'o.`status`, o.pay_amount , IF(pc.id,1,0) as is_pseudo_code ' \
          'FROM `order` o ' \
          'RIGHT JOIN agent_order_correlation aoc ON o.sn = aoc.order_sn ' \
          'LEFT JOIN pseudo_code pc ON o.barcode = pc.barcode ' \
          'WHERE o.type = 2 and o.uid = %s'
    # sql = 'SELECT aoc.agent_order_sn, o.id, o.model_name, o.sn as order_sn, o.barcode, o.damage, o.in_period, ' \
    #       'o.`status`, o.pay_amount ' \
    #       'FROM `order` o ' \
    #       'RIGHT JOIN agent_order_correlation aoc ON o.sn = aoc.order_sn ' \
    #       'WHERE o.type = 2 and o.uid = %s'

    if barcode:
        sql = sql + ' and o.barcode = %s'
        param.append(barcode)
    if status:
        sql = sql + ' and o.`status` = %s'
        param.append(status)
    if order_sn and order_type == 1:
        sql = sql + ' and aoc.agent_order_sn = %s'
        param.append(order_sn)
    if order_sn and order_type == 2:
        sql = sql + ' and o.sn = %s'
        param.append(order_sn)

    if start and end:
        sql = sql + '  AND o.created_at BETWEEN %s AND %s'
        param.append(start)
        param.append(end)

    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def check_order_type(uid, order_sn):
    """
    判断order_sn订单类型
    :param uid: 用户id
    :param order_sn: 订单流水号
    :return: 1 -- 大单  0 -- 小单
    """
    sql = 'select count(*) as count from agent_order where sn = %s and uid = %s and type = %s'
    cur = yield DB_PR.execute(sql, (order_sn, uid, 1))
    data = cur.fetchone()
    if data.get('count') > 0:
        raise gen.Return(1)
    else:
        raise gen.Return(2)


@gen.coroutine
def statistics_order(uid, start, end):
    """
    月账单统计
    :param uid: 用户id
    :param start: 开始时间
    :param end: 结束时间
    :return:
    """
    sql = 'SELECT DATE_FORMAT(o.updated_at_last,\'%%Y-%%m\') date, count(DISTINCT ao.sn) as total_count, ' \
          'count(DISTINCT o.sn) as sub_count, SUM(o.pay_amount) as total_amount FROM agent_order ao LEFT JOIN' \
          ' agent_order_correlation aoc ON ao.sn = aoc.agent_order_sn LEFT JOIN `order` o ON aoc.order_sn = o.sn ' \
          'WHERE ao.uid = %s and (o.`status` = 800 or o.`status` = 900) and ao.type = 1 '
    param = [uid]
    if start and end:
        sql = sql + '  AND o.updated_at_last BETWEEN %s AND %s'
        param.append(start)
        param.append(end)
    sql = sql + ' GROUP BY date'
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def total_order_bill_detail(uid, start, end):
    """
    大单获取
    :param uid: 用户id
    :param start: 起始时间
    :param end: 结束时间
    :return:
    """
    param = [uid, start, end]
    sql = 'SELECT distinct ao.id, ao.sn as agent_order_sn, ao.created_at, SUM(o.`status` = 800 OR o.`status` = 900)' \
          ' as go_count FROM agent_order ao LEFT JOIN agent_order_correlation aoc ON ao.sn = aoc.agent_order_sn ' \
          'LEFT JOIN `order` o ON aoc.order_sn = o.sn WHERE ao.uid = %s AND o.updated_at_last ' \
          'BETWEEN %s AND %s  and ao.status = 1 and ao.type = 1  GROUP BY ao.sn'

    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchall()
    if not data:
        raise gen.Return([])
    for i in data:
        sql = 'select count(*) as count from agent_order_correlation aoc where agent_order_sn = %s'
        cur2 = yield DB_PR.execute(sql, i['agent_order_sn'])
        data2 = cur2.fetchone()
        i['count'] = data2.get('count')
        i = _changeTS(i, ['created_at'])
    raise gen.Return(data)


@gen.coroutine
def sub_order_bill_detail(uid, start, end):
    """

    :param end:
    :param start:
    :param uid: 用户id
    :return:
    """
    param = [uid, start, end]
    sql = 'SELECT aoc.agent_order_sn, o.id, o.model_name, o.sn as order_sn, o.barcode, o.damage, o.in_period, ' \
          'o.`status`, o.pay_amount, o.updated_at_last FROM `order` o RIGHT JOIN agent_order_correlation aoc ' \
          'ON o.sn = aoc.order_sn WHERE o.uid = %s  AND o.updated_at_last BETWEEN %s AND %s ' \
          'AND (o.`status` = 800 or o.`status` = 900)'

    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    for i in data:
        i = _changeTS(i, ['updated_at_last'])
    raise gen.Return(data)


@gen.coroutine
def confirm_repair(uid, order_sn):
    sql = 'update `order` set status = %s where uid = %s and sn = %s'
    ret = None
    try:
        yield DB_PR.execute(sql, (600, uid, order_sn))
        log = {
            'pr_sn': order_sn,
            'pr_status': 600,
            'log_status': 600,
            'log_from': 'app',
            'relation_key': '',
            'operation': 'confirm_repair',
            'uid': uid,
            'admin': 0,
            'title': '用户确认维修成功，正在维修中',
            'remark': '',
            'date': _now_date(),
        }
        repair_model.add_log(log)
        ret = True
    except:
        app_log.error('confirm repair error', exc_info=True)
    if ret:
        check_man = yield check_man_info(order_sn)
        if check_man:
            print check_man
            msg = '订单（' + order_sn + ')已经确认维修，请尽快处理。'
            user = [check_man.get('wechat_id')]
            # user = ['0431']
            work_wechat_model.send_work_wechat_msg(user, msg)

    raise gen.Return(ret)


@gen.coroutine
def check_man_info(sn):
    """
    获取维修人员的工号
    :param sn:
    :return:
    """
    sql = 'SELECT ps.wechat_id FROM pr_staff ps RIGHT JOIN `order` o ON ps.user_id = o.check_man WHERE o.sn = %s'
    cur = yield DB_PR.execute(sql, sn)
    data = cur.fetchone()
    raise gen.Return(data)

@gen.coroutine
def cancel_agent_repair(uid, sn):
    sql = 'delete from order_cache where uid = %s and sn = %s'
    sql2 = 'delete from agent_order_correlation_cache where order_sn = %s'
    ret = None
    trans = yield DB_PR.begin()
    try:
        yield trans.execute(sql, (uid, sn))
        yield trans.execute(sql2, sn)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        app_log.error('delete order_cache error')
    raise gen.Return(ret)


@gen.coroutine
def order_cache(uid):
    """
    大单获取
    :param uid: 用户id
    :return:
    """
    param = [uid]

    sql = 'SELECT ao.sn as agent_order_sn, oc.sn as order_sn, oc.barcode FROM agent_order ao LEFT JOIN ' \
          'agent_order_correlation_cache aocc ON ao.sn = aocc.agent_order_sn left join order_cache oc on' \
          ' aocc.order_sn = oc.sn WHERE ao.uid = %s AND ao.`status` = 0'
    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchall()
    ret = dict()
    datas = []
    for i in data:
        agent_order_sn = i.get('agent_order_sn')
        ls = ret.get(agent_order_sn, [])
        if i.get('order_sn'):
            ls.append({'order_sn': i['order_sn'], 'barcode': i['barcode']})
            ret[agent_order_sn] = ls
    for k, v in ret.items():
        datas.append({'agent_order_sn': k, 'sub_order': v})
    raise gen.Return(datas)


@gen.coroutine
def order_cache_detail(uid, order_sn):
    """
    大单获取
    :param uid:
    :param order_sn: 流水号
    :return:
    """
    param = [uid, order_sn]

    sql = 'SELECT barcode, serial, model_id, model_name, in_period, has_warranty, reason, damage, period_file, ' \
          'upload_file, video_file, description, repair_endpoint, attachment FROM order_cache ' \
          'WHERE uid = %s and sn = %s'
    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchone()
    if data:
        _image_to_json(data, ['period_file', 'upload_file', 'video_file'])
    raise gen.Return(data)


@gen.coroutine
def order_cache_delete(uid, sn):
    """
    删除大单对应的全部寄修缓存单
    :param uid:
    :param sn:   大单单号
    :return:
    """
    sql = 'delete from agent_order where uid = %s and sn = %s and status = 0'
    sql2 = ' delete oc from order_cache oc, agent_order_correlation_cache aocc where oc.sn = aocc.order_sn ' \
           'AND aocc.agent_order_sn = %s'
    sql3 = 'delete from agent_order_correlation_cache where agent_order_sn = %s'
    ret = None
    trans = yield DB_PR.begin()
    try:
        yield trans.execute(sql, (uid, sn))
        yield trans.execute(sql2, sn)
        yield trans.execute(sql3, sn)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        app_log.error('delete order_cache error', exc_info=True)
    raise gen.Return(ret)