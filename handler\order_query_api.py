# encoding=utf-8
# __author__ = 'lch'

import tornado
from tornado import gen
from tornado.log import app_log
import re
import time
import datetime
import traceback

import util.com as com
from handler import ApiHandler, tokenauth
import store.repair_model as repair_model

def _isKeyword(keyword):
    """
    检查关键字格式是否正确
    :param keyword: 关键字，6位或11位
    :return: True/False
    """
    if not keyword:
        return False
    # 6位：条码后6位（字母数字）
    if len(keyword) == 6:
        return re.match(r'^[A-Za-z0-9]{6}$', keyword) is not None
    # 11位：手机号
    elif len(keyword) == 11:
        return re.match(r'^1[0-9]{10}$', keyword) is not None
    else:
        return False


class OrderQuery(ApiHandler):
    """根据关键字查询订单最新日志"""
    
    def data_received(self, chunk):
        pass

    @gen.coroutine
    def get(self):
        keyword = self.get_argument("keyword", "")
        if not keyword:
            raise gen.Return(self.error(u"关键字不能为空", com.ERROR_PARAM))
        
        if not _isKeyword(keyword):
            raise gen.Return(self.error(u'关键字格式错误，应为6位条码后缀或11位手机号', com.ERROR_PARAM))
        
        try:
            # 根据关键字查询订单号
            order_sns = yield repair_model.get_order_sn_by_keyword(keyword)
            
            if not order_sns:
                raise gen.Return(self.error(u'未找到相关订单', com.ERROR_EMPTY_DATA))
            
            # 获取每个订单的最新日志
            result_list = []
            for sn in order_sns:
                latest_log = yield repair_model.get_latest_order_log(sn)
                if latest_log:
                    # 添加订单号到日志记录中
                    latest_log['order_sn'] = sn
                    result_list.append(latest_log)
            
            if not result_list:
                raise gen.Return(self.error(u'未找到订单日志', com.ERROR_EMPTY_DATA))
            
            ret = dict()
            ret['keyword'] = keyword
            ret['total_orders'] = len(order_sns)
            ret['logs'] = result_list
            
            raise gen.Return(self.success(ret, "success"))
            
        except Exception as e:
            app_log.error('OrderQuery error: %s' % str(e), exc_info=True)
            raise gen.Return(self.error(u'查询失败', com.ERROR_SYSTEM))
