#coding=utf-8
#encoding=utf-8
import datetime
import json
import time

import export_format
from export import connect, cursor, connect_yx, cursor_yx, save_path

from openpyxl import Workbook

export_type = 'broken_screen_insurance'
sheet_title = 'export'
freeze_coordinate = 'D2'

key_title_dicts = ( # 表格中显示的列
    {'key': 'sn', 'title': '保单编号'},
    {'key': 'barcode', 'title': '条码'},
    {'key': 'model_name', 'title': '机型'},
    {'key': 'standard_name', 'title': '投保类型'},
    {'key': 'top_agency', 'title': '一级代理'},
    {'key': 'second_agency', 'title': '二级代理'},
    {'key': 'endpoint_name', 'title': '购买终端'},
    {'key': 'buy_type', 'title': '购买类型'},
    {'key': 'pay_amount', 'title': '保险价格'},
    {'key': 'created_at', 'title': '投保时间'},
    {'key': 'end_time', 'title': '过期日期'},
    {'key': 'audited_at', 'title': '审核时间'},
    {'key': 'usage_state', 'title': '使用状态'},
    {'key': 'status', 'title': '保单状态'},
    {'key': 'trade_plat', 'title': '支付方式'},
    {'key': 'pay_id', 'title': '支付单号'},
    {'key': 'paid_at', 'title': '支付时间'},
    {'key': 'refund_id', 'title': '退款单号'},
    {'key': 'refund_at', 'title': '退款时间'},
)
key_list = map(lambda x: x.get('key'), key_title_dicts)
title_list = map(lambda x: x.get('title'), key_title_dicts)
key_fn_dict = { # 需要另外通过函数获取值的列
    'top_agency': export_format.f_agency_name,
    'second_agency': export_format.f_agency_name,
    'buy_type': export_format.f_bsi_buy_type,
    'end_time': export_format.f_bsi_end_time,
    'usage_state': export_format.f_bsi_usage_state,
    'status': export_format.f_bsi_status,
    'trade_plat': export_format.f_bsi_trade_plat,
}
key_column_width_dict = { # 需要设置列宽的列
    'sn': 25.38,
    'barcode': 14.25,
    'created_at': 20.88,
    'end_time': 10.88,
    'audited_at': 20.88,
    'pay_id': 33.13,
    'paid_at': 20.88,
    'refund_id': 33.13,
    'refund_at': 20.88,
}

def export(data):
    ret = 0
    # 读取参数
    params = None
    if data and isinstance(data, dict) and data.get('params'):
        params = json.loads(data.get('params'))
    paid_at_start = None
    paid_at_end = None
    created_at_start = None
    created_at_end = None
    audited_at_start = None
    audited_at_end = None
    refund_at_start = None
    refund_at_end = None
    if params and isinstance(params, dict):
        if params.get('paid_at') \
        and params['paid_at'].get('start') and params['paid_at'].get('end'):
            paid_at_start = params['paid_at'].get('start')
            paid_at_end = params['paid_at'].get('end')
        if params.get('created_at') \
        and params['created_at'].get('start') and params['created_at'].get('end'):
            created_at_start = params['created_at'].get('start')
            created_at_end = params['created_at'].get('end')
        if params.get('audited_at') \
        and params['audited_at'].get('start') and params['audited_at'].get('end'):
            audited_at_start = params['audited_at'].get('start')
            audited_at_end = params['audited_at'].get('end')
        if params.get('refund_at') \
        and params['refund_at'].get('start') and params['refund_at'].get('end'):
            refund_at_start = params['refund_at'].get('start')
            refund_at_end = params['refund_at'].get('end')
    # 检查参数
    now = datetime.datetime.now()
    start = datetime.datetime(now.year, now.month, now.day, 0, 0, 0).strftime('%Y-%m-%d %H:%M:%S')
    end = datetime.datetime(now.year, now.month, now.day, 23, 59, 59).strftime('%Y-%m-%d %H:%M:%S')
    if not paid_at_start and not paid_at_end and \
    not created_at_start and not created_at_end and \
    not audited_at_start and not audited_at_end and \
    not refund_at_start and not refund_at_end:
        created_at_start = start
        created_at_end = end
    print '%s start:%s, end:%s,' % (export_type, start, end)
    print '%s paid_at_start:%s, paid_at_end:%s,' % (export_type, paid_at_start, paid_at_end)
    print '%s created_at_start:%s, created_at_end:%s,' % (export_type, created_at_start, created_at_end)
    print '%s audited_at_start:%s, audited_at_end:%s,' % (export_type, audited_at_start, audited_at_end)
    print '%s refund_at_start:%s, refund_at_end:%s,' % (export_type, refund_at_start, refund_at_end)
    # 构建查询语句
    sql_1_base = ' select bsi.sn,' \
                 '        bsi.barcode,' \
                 '        bsi.model_name,' \
                 '        bsis.name as standard_name,' \
                 '        bsi.top_agency,' \
                 '        bsi.second_agency,' \
                 '        bsi.endpoint_name,' \
                 '        bsi.type as buy_type,' \
                 '        bsi.pay_amount,' \
                 '        bsi.created_at,' \
                 '        bsi.month,' \
                 '        bsi.audited_at,' \
                 '        bsi.usage_state,' \
                 '        bsi.status,' \
                 '        bsi.trade_plat,' \
                 '        bsi.pay_id,' \
                 '        bsi.paid_at,' \
                 '        bsi.refund_id,' \
                 '        bsi.refund_at' \
                 ' from broken_screen_insurance bsi' \
                 '          left join broken_screen_insurance_standard bsis on bsi.standard = bsis.id' \
                 ' where 1=1'
    sql_1_where = ''
    param_1 = []
    if paid_at_start and paid_at_end:
        sql_1_where += ' and bsi.paid_at between %s and %s'
        param_1.extend([paid_at_start, paid_at_end])
    if created_at_start and created_at_end:
        sql_1_where += ' and bsi.created_at between %s and %s'
        param_1.extend([created_at_start, created_at_end])
    if audited_at_start and audited_at_end:
        sql_1_where += ' and bsi.audited_at between %s and %s'
        param_1.extend([audited_at_start, audited_at_end])
    if refund_at_start and refund_at_end:
        sql_1_where += ' and bsi.refund_at between %s and %s'
        param_1.extend([refund_at_start, refund_at_end])
    sql_1 = sql_1_base + sql_1_where
    param_1 = tuple(param_1)
    ## print sql_1
    ## print param_1
    # 读取数据库
    sql_1_begin = time.clock()
    cursor.execute(sql_1, param_1)
    data_1 = cursor.fetchall()
    sql_1_finish = time.clock()
    print export_format.sql_log_str.format(type=export_type, no=1,
        count=len(data_1), dura=(sql_1_finish - sql_1_begin)*1000)
    if data_1 and len(data_1) > 0:
        # 取 agency_id 列表
        top_agency_id_dict = {di.get('top_agency'): 0 for di in data_1}
        top_agency_id_list = top_agency_id_dict.keys()
        second_agency_id_dict = {di.get('second_agency'): 0 for di in data_1}
        second_agency_id_list = second_agency_id_dict.keys()
        agency_id_list = top_agency_id_list.extend(second_agency_id_list)
        # 读取其它数据
        agency_list = get_agency_list()
        agency_dict = {ai.get('id'): ai.get('name') for ai in agency_list}
        sql_2_finish = time.clock()
        print export_format.sql_log_str.format(type=export_type, no=2,
            count=len(agency_dict), dura=(sql_2_finish - sql_1_finish)*1000)
        other_data = {
                'agency_dict': agency_dict,
            }
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = sheet_title
        ws.append(title_list)
        # 处理数据
        for di in data_1:
            ri = get_one_row(di, other_data)
            ws.append(ri)
        # 工作表处理
        column_width_dict = export_format.to_column_width_dict(key_column_width_dict, key_list)
        export_format.worksheet_setup(ws, freeze_coordinate=freeze_coordinate, table_title=sheet_title,
            column_width_dict=column_width_dict)
        # 保存文件
        wb.save(save_path + data['title'].decode('utf-8') + ".xlsx")
        wb.close()
        w_finish = time.clock()
        print export_format.sql_log_str.format(type=export_type, no='w',
            count='---', dura=(w_finish - sql_2_finish)*1000)
    else:
        ret = -1
    return ret

def get_agency_list():
    sql_base = 'select * from agency '
    # sql_where = 'where id in (%s) ' % ','.join(['%s'] * len(agency_id_list))
    sql = sql_base # + sql_where
    cursor_yx.execute(sql)
    data = cursor_yx.fetchall()
    return data

def get_one_row(di, other_data):
    row = []
    for k in key_list:
        c = di.get(k)
        if key_fn_dict.has_key(k):
            c = key_fn_dict.get(k)(c, di, other_data)
        row.append(c)
    return row
