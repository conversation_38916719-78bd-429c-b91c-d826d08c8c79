# coding=utf-8
# encoding=utf-8
# __author__="QZL"

import sys
import hashlib
import time
import random
import json
import urllib, urllib2
import pymysql
import datetime

reload(sys)
sys.setdefaultencoding('utf-8')

DEBUG = False
#
# connect = pymysql.connect(
#     host='**************',
#     user='root',
#     passwd='root',
#     # db='rbcare',
#     db='post_repair',
#     charset='utf8',
#     # host='localhost',
#     # user='root',
#     # passwd='q',
#     # db='rbcare',
#     # # db='readboydata',
#     # charset='utf8',
# ) if DEBUG else pymysql.connect(
#     host='rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',
#     user='rbcare',
#     passwd='9ZS0sebU2IfMTp3U',
#     db='post_repair',
#     # db='readboydata',
#     charset='utf8',
# )
# cursor = connect.cursor(cursor=pymysql.cursors.DictCursor)


def send_work_weichat_msg(user, msg):
    """
    发送企业微信消息
    :param user: 用户数组
    :param msg: 消息文本
    :return:
    """
    URL = 'https://oa.readboy.com/index.php?s=/Weixin/Api/send.html&auth_key='
    KEY = 'WeChatReadboyApi2018'

    ### sn
    t = str(int(time.time()))
    rd = str(random.randint(100000, 999999))
    key = hashlib.md5(KEY).hexdigest()
    s = '-'.join([t, rd, key])
    md5 = hashlib.md5(s).hexdigest()
    sn = '-'.join([t, rd, md5])

    data = {
        'tousername': user,
        'content': '寄修中心提醒你：\n' + msg
    }
    data = json.dumps(data)
    data = {'data': data}
    data = urllib.urlencode(data)
    url = URL + sn
    try:
        req = urllib2.Request(url, data)
        resp = urllib2.urlopen(req)
    except:
        print "work weichat api failed！"


def weekly_report():
    today = datetime.datetime.now()
    week = today.weekday()

    last_start = today - datetime.timedelta(days=today.weekday() + 7)
    last_end = today - datetime.timedelta(days=today.weekday() + 1)
    last_start_str = last_start.strftime('%Y%m%d')
    last_end_str = last_end.strftime('%Y%m%d')
    last_start_m = last_start.strftime('%m')
    last_start_d = last_start.strftime('%d')
    last_end_m = last_end.strftime('%m')
    last_end_d = last_end.strftime('%d')

    user = ['0431', '0362'] if DEBUG else ['0431', '0319', '0506', '0362', '0288', '0504', '0204', '0542']
    week_report = 'http://h5-yx.readboy.com/repair_report/#/detail/' + last_start_str + '/' + last_end_str
    field = '<a href = \'' + week_report + '\'>' + last_start_m + '月' + last_start_d + '日-' + \
            last_end_m + '月' + last_end_d + '日 寄修周报</a>'
    s = field
    print s
    if week == 0:
        send_work_weichat_msg(user, s)


def monthly_report():
    today = datetime.datetime.now()
    ret = first_day_of_month(today)
    lsat_first = datetime.date(today.year, today.month - 1, 1)
    last_end = datetime.date(today.year, today.month, 1) - datetime.timedelta(1)
    # 上月一号和最后一天  格式  20200601
    last_start_str = lsat_first.strftime('%Y%m%d')
    last_end_str = last_end.strftime('%Y%m%d')

    # 上月月份和日期 格式  06 01
    last_start_m = lsat_first.strftime('%m')
    last_start_d = lsat_first.strftime('%d')
    last_end_m = last_end.strftime('%m')
    last_end_d = last_end.strftime('%d')

    user = ['0431', '0362'] if DEBUG \
        else ['0431', '0319', '0506', '0362', '0288', '0504', '0204', '0542', '0508', '0631',
              '0549', '0502', '0850', '0501', '0315', '0036', '1020']
    # 覃荣业-未知-刘超华-刘绮绮-姚怡-李洪奎-颜建新-朱坤-季英会-刘明高
    # 张少存-刘培勇-陈钢-钟富强-陈明-陈家峰-廖达力
    week_report = 'http://h5-yx.readboy.com/repair_report/#/detail/' + last_start_str + '/' + last_end_str
    field = '<a href = \'' + week_report + '\'>' + last_start_m + '月' + last_start_d + '日-' + \
            last_end_m + '月' + last_end_d + '日 寄修月报</a>'
    s = field
    print s
    if ret:
        send_work_weichat_msg(user, s)


def first_day_of_month(dt):
    """判断今天是不是这个月第一天"""

    now_day = (dt + datetime.timedelta(days=-dt.day + 1)).day
    return now_day == dt.day


def main():
    monthly_report()
    # weekly_report()


if __name__ == '__main__':
    main()
