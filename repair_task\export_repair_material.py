#coding=utf-8
#encoding=utf-8
import datetime
import json
import time

import export_format
from export import connect, cursor, connect_yx, cursor_yx, save_path

from openpyxl import Workbook

export_type = 'repair_material'
sheet_title = 'export'
freeze_coordinate = 'G2'

key_title_dicts = ( # 表格中显示的列
    {'key': 'pay_com', 'title': '支付方式'},
    {'key': 'id', 'title': 'id'},
    {'key': 'sn', 'title': '寄修单号'},
    {'key': 'barcode', 'title': 'SN码'},
    {'key': 'status', 'title': '订单状态'},
    {'key': 'connect', 'title': '联系状态'},
    {'key': 'in_period', 'title': '保修状态'},
    {'key': 'ar_repeat', 'title': '之前弃修次数'},
    {'key': 'has_screen_insurance', 'title': '有无碎屏保'},
    {'key': 'used_screen_insurance', 'title': '是否使用碎屏保'},
    {'key': 'is_direct_sales', 'title': '是否直营（碎屏保）'},
#   {'key': 'buy_date', 'title': '保卡时间'},
    {'key': 'pay_time', 'title': '付款时间'},
    {'key': 'updated_at', 'title': '配件添加时间'},
#   {'key': 'statistic_time', 'title': '统计时间'},
    {'key': 'description', 'title': '细节描述'},
    {'key': 'deal_remark', 'title': '维修备注'},
    {'key': 'pay_remark', 'title': '支付备注'},
    {'key': 'machine_malfunction', 'title': '配件故障', 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'name', 'title': '物料名称'},
    {'key': 'code', 'title': '新物料编码'},
    {'key': 'old_code', 'title': '旧物料编码', 'hide_in_staff': True},
    {'key': 'count', 'title': '数量'},
    {'key': 'price', 'title': '单价'},
    {'key': 'is_charge', 'title': '是否收费'},
    {'key': 'charge_type', 'title': '收费类型', 'hide_in_staff': True},
    {'key': 'discount_value', 'title': '配件折扣-按购机时间计算', 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'oe_discount', 'title': '订单是1否0打折', 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'mat_discount', 'title': '配件是1否0打折', 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'price_in', 'title': '配件实收价格'},
    {'key': 'amount', 'title': '总费用', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'accessory_in_ar', 'title': '弃修时配件费用', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'amount_in_ar', 'title': '弃修时应收费用', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'staff_cast', 'title': '快递费用'},
    {'key': 'accessory_cast', 'title': '维修配件应收费用', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'optional_accessory_cast', 'title': '自选配件应收费用', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'pay_amount', 'title': '应付费用', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'paid_price', 'title': '已收费用', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'refund_amount', 'title': '退款金额', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'paid_reduce_refund', 'title': '已收减退款', 'hide_in_second': True, 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'model_name', 'title': '机型'},
    {'key': 'type', 'title': '寄修渠道'},
    {'key': 'mark', 'title': '物料添加类型', 'hide_in_staff': True, 'hide_in_oa': True},
    {'key': 'export_status', 'title': '是否已导出', 'hide_in_staff': True, 'hide_in_oa': True},
)
key_list = map(lambda x: x.get('key'), key_title_dicts)
title_list = map(lambda x: x.get('title'), key_title_dicts)
key_hide_in_second_list = map(lambda x: x.get('key'), filter(lambda x: x.get('hide_in_second'), key_title_dicts))
key_hide_in_staff_list = map(lambda x: x.get('key'), filter(lambda x: x.get('hide_in_staff'), key_title_dicts))
key_hide_in_oa_list = map(lambda x: x.get('key'), filter(lambda x: x.get('hide_in_oa'), key_title_dicts))
key_fn_dict = { # 需要另外通过函数获取值的列
    'pay_com': export_format.f_pay_com,
    'status': export_format.f_order_status,
    'connect': export_format.f_connect,
    'in_period': export_format.f_in_period,
    'has_screen_insurance': export_format.f_has_screen_insurance,
    'used_screen_insurance': export_format.f_used_screen_insurance,
    'is_direct_sales': export_format.f_is_direct_sales,
    'buy_date': export_format.f_buy_date,
    'pay_time': export_format.f_pay_time,
    'description': export_format.f_description,
    'machine_malfunction': export_format.f_machine_malfunction,
    'price': export_format.f_price,
    'is_charge': export_format.f_is_charge,
    'charge_type': export_format.f_charge_type,
    'discount_value': export_format.f_discount_value,
    'paid_price': export_format.f_paid_price,
    'refund_amount': export_format.f_refund_amount,
    'type': export_format.f_post_repair_type,
    'mark': export_format.f_used_material_mark,
}
key_column_width_dict = { # 需要设置列宽的列
    'sn': 22,
    'barcode': 14.25,
    'status': 10.25,
    'buy_date': 20.88,
    'pay_time': 20.88,
    'updated_at': 20.88,
    'statistic_time': 20.88,
    'code': 12,
    'old_code': 10.88,
}

def export(data):
    ret = 0
    # 读取参数
    params = None
    if data and isinstance(data, dict) and data.get('params'):
        params = json.loads(data.get('params'))
    # 检查参数
    now = datetime.datetime.now()
    start = datetime.datetime(now.year, now.month, now.day, 0, 0, 0).strftime('%Y-%m-%d %H:%M:%S')
    end = datetime.datetime(now.year, now.month, now.day, 23, 59, 59).strftime('%Y-%m-%d %H:%M:%S')
    if params and isinstance(params, dict) and params.get('pay_time') \
    and params['pay_time'].get('start') and params['pay_time'].get('end'):
        start = params['pay_time'].get('start')
        end = params['pay_time'].get('end')
    print '%s start:%s, end:%s,' % (export_type, start, end)
    # 读取订单日志已支付的
    sql_1 = ' select o.pay_com,' \
            '        o.id,' \
            '        o.sn,' \
            '        o.barcode,' \
            '        o.status,' \
            '        o.connect,' \
            '        o.in_period,' \
            '        o.ar_repeat,' \
            '        bsi.is_direct_sales,' \
            '        bsi.status   as bsi_status,' \
            '        o.pay_time,' \
            '        pum.updated_at,' \
            '        pum.statistic_time,' \
            '        o.description,' \
            '        o.deal_remark,' \
            '        oe.pay_remark,' \
            '        oe.balance_amount,' \
            '        m.name,' \
            '        m.code,' \
            '        m.old_code,' \
            '        pum.count,' \
            '        m.price,' \
            '        m.price_first,' \
            '        pum.is_charge,' \
            '        pum.charge_type,' \
            '        pum.price_in,' \
            '        oe.discount  as oe_discount,' \
            '        mat.discount as mat_discount,' \
            '        o.amount,' \
            '        o.accessory_in_ar,' \
            '        o.amount_in_ar,' \
            '        o.staff_cast,' \
            '        o.accessory_cast,' \
            '        o.optional_accessory_cast,' \
            '        o.pay_amount,' \
            '        o.model_name,' \
            '        o.type,' \
            '        pum.mark,' \
            '        pum.export_status,' \
            '        pum.id       as pum_id,' \
            '        pum.created_at,' \
            '        pum.material_id' \
            ' from order_log ol' \
            '          left join `order` o on ol.pr_sn = o.sn' \
            '          left join order_extend oe on o.sn = oe.sn' \
            '          left join broken_screen_insurance bsi on o.barcode = bsi.barcode and bsi.status in (300, 400)' \
            '          left join pr_used_material pum on o.sn = pum.pr_sn' \
            '          left join machine_accessory_tree mat on pum.mat_id = mat.id' \
            '          left join material m on pum.material_id = m.id' \
            ' where ol.log_status = 600 and ol.date between %s and %s' \
            '   and o.id is not null' \
            ' order by o.id, pum.is_charge desc'
    sql_1_begin = time.clock()
    cursor.execute(sql_1, (start, end))
    data_1 = cursor.fetchall()
    sql_1_finish = time.clock()
    print export_format.sql_log_str.format(type=export_type, no=1,
        count=len(data_1), dura=(sql_1_finish - sql_1_begin)*1000)
    if data_1 and len(data_1) > 0:
        # 取pr_sn列表
        pr_sn_dict = {di.get('sn') : 0 for di in data_1}
        pr_sn_list = pr_sn_dict.keys()
        # 取barcode列表
        barcode_dict = {di.get('barcode') : 0 for di in data_1}
        barcode_list = barcode_dict.keys()
        # 读取其它数据
        material_case_list = get_material_case_list(pr_sn_list)
        material_case_dict = export_format.to_material_case_dict(material_case_list)
        sql_2_finish = time.clock()
        print export_format.sql_log_str.format(type=export_type, no=2,
            count=len(material_case_dict), dura=(sql_2_finish - sql_1_finish)*1000)
        pay_info_list = get_pay_info_list(pr_sn_list)
        pay_info_dict = export_format.to_pay_info_dict(pay_info_list, pr_sn_list)
        sql_3_finish = time.clock()
        print export_format.sql_log_str.format(type=export_type, no=3,
            count=len(pay_info_list), dura=(sql_3_finish - sql_2_finish)*1000)
        refund_info_list = get_refund_info_list(start, end)
        refund_info_dict = export_format.to_refund_info_dict(refund_info_list, pr_sn_list)
        sql_4_finish = time.clock()
        print export_format.sql_log_str.format(type=export_type, no=4,
            count=len(refund_info_list), dura=(sql_4_finish - sql_3_finish)*1000)
        buy_date_list = get_buy_date_list(barcode_list)
        buy_date_dict = export_format.to_buy_date_dict(buy_date_list, barcode_list)
        sql_5_finish = time.clock()
        print export_format.sql_log_str.format(type=export_type, no=5,
            count=len(buy_date_list), dura=(sql_5_finish - sql_4_finish)*1000)
        oa_list = get_oa_list(pr_sn_list)
        oa_dict = export_format.to_oa_dict(oa_list)
        sql_6_finish = time.clock()
        print export_format.sql_log_str.format(type=export_type, no=6,
            count=len(oa_list), dura=(sql_6_finish - sql_5_finish)*1000)
        other_data = {
                'material_case_dict': material_case_dict,
                'pay_info_dict': pay_info_dict,
                'refund_info_dict': refund_info_dict,
                'buy_date_dict': buy_date_dict,
                'oa_dict': oa_dict,
            }
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = sheet_title
        ws.append(title_list)
        # 处理数据
        rs = []
        id = -1
        for di in data_1:
            if not export_format.di_filter_pay_time(di, other_data, start, end):
                continue
            is_second = False
            if id != di.get('id'): # 新订单首行
                id = di.get('id')
            else: # 订单第二行
                is_second = True
            r = get_one_row(di, other_data, is_second)
            if not is_second: # 新订单首行操作
                # 复制一行转为快递费
                re = r[:]
                export_format.row_handle_to_staff_cast(re, key_list)
                export_format.row_handle_remove_key(re, key_list, key_hide_in_staff_list)
                rs.append(re)
            if not export_format.di_filter_only_repair_charge(di):
                continue
            export_format.row_handle_old_code(r, key_list)
            export_format.row_handle_price_in(r, key_list)
            export_format.row_handle_paid_reduce_refund(r, key_list)
            export_format.row_handle_balance_amount(r, key_list, di)
            rs.append(r)
        ## 自选配件插入到对应订单后面
        i_sn = key_list.index('sn')
        for sn, oa_l in oa_dict.items():
            for index in range(len(rs)-1, -1, -1):
                v_sn = rs[index][i_sn]
                if sn == v_sn:
                    # 复制一行转为自选配件
                    re = rs[index][:]
                    res = export_format.row_handle_to_oa(re, key_list, oa_l)
                    # 保持顺序插入
                    ri_offset = 1
                    for ri in res:
                        export_format.row_handle_old_code(ri, key_list)
                        export_format.row_handle_remove_key(ri, key_list, key_hide_in_oa_list)
                        rs.insert(index+ri_offset, ri)
                        ri_offset += 1
                    break
        for ri in rs:
            ws.append(ri)
        # 工作表处理
        column_width_dict = export_format.to_column_width_dict(key_column_width_dict, key_list)
        export_format.worksheet_setup(ws, freeze_coordinate=freeze_coordinate, table_title=sheet_title,
            column_width_dict=column_width_dict)
        # 保存文件
        wb.save(save_path + data['title'].decode('utf-8') + ".xlsx")
        wb.close()
        w_finish = time.clock()
        print export_format.sql_log_str.format(type=export_type, no='w',
            count='---', dura=(w_finish - sql_6_finish)*1000)
    else:
        ret = -1
    return ret

def get_material_case_list(pr_sn_list):
    sql_base = 'select pr_sn, material_id, title from pr_material as pm ' \
               'left join machine_malfunction as mm on pm.malfunction_id = mm.id '
    sql_where = 'where pm.pr_sn in (%s) ' % ','.join(['%s'] * len(pr_sn_list))
    sql = sql_base + sql_where
    cursor.execute(sql, pr_sn_list)
    data = cursor.fetchall()
    return data

def get_pay_info_list(pr_sn_list):
    sql_base = 'select * from pay_order '
    sql_where = 'where pr_sn in (%s) and is_paid = 1 ' % ','.join(['%s'] * len(pr_sn_list))
    sql = sql_base + sql_where
    cursor.execute(sql, pr_sn_list)
    data = cursor.fetchall()
    return data # 存在没有支付的情况

def get_refund_info_list(start, end):
    sql = 'select * from refund_order where created_at between %s and %s and is_refund = 1 '
    cursor.execute(sql, (start, end))
    data = cursor.fetchall()
    return data

def get_buy_date_list(barcode_list):
    sql_base = 'select barcode, buy_date from warranty '
    sql_where = 'where barcode in (%s) and status = 1 ' % ','.join(['%s'] * len(barcode_list))
    sql_order = 'order by buy_date desc '
    sql = sql_base + sql_where + sql_order
    cursor_yx.execute(sql, barcode_list)
    data = cursor_yx.fetchall()
    return data

def get_oa_list(pr_sn_list):
    sql_fields = 'pr_oa.pr_sn, oa.name as oa_name, oa.sell_price, pr_oa.count, pr_oa.price, pr_oa.created_at, m.name as m_name, m.code as m_code'
    sql_base = 'select {sql_fields} from pr_optional_accessory pr_oa '.format(sql_fields=sql_fields)
    sql_join_m = 'left join material m on pr_oa.material_id = m.id '
    sql_join_oa = 'left join optional_accessory oa on pr_oa.oa_id = oa.id '
    sql_where = 'where pr_oa.pr_sn in (%s) and pr_oa.status = 0 and cancel_at is null ' % ','.join(['%s'] * len(pr_sn_list))
    sql = sql_base + sql_join_m + sql_join_oa + sql_where
    cursor.execute(sql, pr_sn_list)
    data = cursor.fetchall()
    return data

def get_one_row(di, other_data, is_second):
    row = []
    for k in key_list:
        c = None
        if is_second and k in key_hide_in_second_list: # 非首行且需隐藏的列
            pass
        else:
            c = di.get(k, '')
            if key_fn_dict.has_key(k):
                c = key_fn_dict.get(k)(c, di, other_data)
        row.append(c)
    return row
