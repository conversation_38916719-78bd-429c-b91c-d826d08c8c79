# encoding=utf-8
# __author__ = 'lch'

from store import DB
# import util.com as com
from tornado import gen
import bcrypt
from hashlib import md5
import time

import cache.user_cache as auth_cache
from conf.config import FILE_HOST, FILE_HOST_HTTP

@gen.coroutine
def layer_sound():
    """
    音频列表
    :return:
    """
    sql = 'select * from layer_sound'
    cur = yield DB.execute(sql)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def sound_list(key, page, count):
    """
    音频列表
    :param key: 层级关键字
    :param page: 页码
    :param count: 每页条数
    :return:
    """
    if not key:
        raise gen.Return(None)
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    sql = 'select * from sound where dir like %s limit %s, %s'
    cur = yield DB.execute(sql, ('%'+key+'%', offset, count+1))
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    l = len(data)
    isEnd = l < count + 1
    size = l if isEnd else count
    data = data[0:size]
    for i in data:
        if i.get('path'):
            i['path'] = FILE_HOST_HTTP + i['path']
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)


@gen.coroutine
def sound_search(key, page, count):
    """
    音频搜索列表
    :param key: 关键字
    :param page: 页码
    :param count: 条数
    :return:
    """
    if not key:
        raise gen.Return(None)
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    sql = 'select * from sound where name like %s limit %s, %s'
    # print sql % ('%'+key+'%', offset, count+1)
    cur = yield DB.execute(sql, ('%'+key+'%', offset, count+1))
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    l = len(data)
    isEnd = l < count + 1
    size = l if isEnd else count
    data = data[0:size]
    for i in data:
        if i.get('path'):
            i['path'] = FILE_HOST_HTTP + i['path']
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)


@gen.coroutine
def sound_search_arr(key, page, count):
    """
    音频搜索列表,支持数组关键字,按数组顺序返回
    :param key: 关键字
    :param page: 页码
    :param count: 条数
    :return:
    """
    key_array = key.split(',')
    if not key_array:
        raise gen.Return(None)
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    where = 'select * from sound where name regexp "%s"' % ('|'.join(key_array))
    order = ' order by (case'
    for i, v in enumerate(key_array):
        str = ' when name like "%s" then %s ' % ('%'+v+'%', i)
        order += str
    order += 'end )'
    limit = ' limit %s, %s' % (offset, count+1)
    sql = where + order + limit
    # print sql
    # sql = 'select * from sound where name like %s'
    cur = yield DB.execute(sql)
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    l = len(data)
    isEnd = l < count + 1
    size = l if isEnd else count
    data = data[0:size]
    for i in data:
        if i.get('path'):
            i['path'] = FILE_HOST_HTTP + i['path']
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)


@gen.coroutine
def books(key, page, count):
    """
    书本列表
    :param key: 层级关键字
    :param page: 页码
    :param count: 条数
    :return:
    """
    if not key:
        raise gen.Return(None)
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    sql = 'select * from book where layer like %s limit %s, %s'
    cur = yield DB.execute(sql, ('%'+key+'%', offset, count+1))
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    l = len(data)
    isEnd = l < count + 1
    size = l if isEnd else count
    data = data[0:size]
    for i in data:
        if i.get('path'):
            i['path'] = FILE_HOST + i['path']
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)


@gen.coroutine
def video(vid):
    """
    视频
    :param vid: 视频id
    :return:
    """
    if not vid:
        raise gen.Return(None)
    sql = 'select * from video where vid=%s'
    cur = yield DB.execute(sql, vid)
    data = cur.fetchone()
    if data:
        if data.get('path'):
            data['path'] = FILE_HOST + data['path']
    raise gen.Return(data)


@gen.coroutine
def click_learn(cover, content):
    """
    获取点读数据
    :param cover: 封面码
    :param content: 内容码
    :return:
    """
    if not cover and not content:
        raise gen.Return(None)
    if cover:
        sql = 'select * from click_learn where cover=%s'
        cur = yield DB.execute(sql, cover)
        data = cur.fetchone()
    else:
        sql = 'select * from click_learn where content_start <= %s and content_end >= %s'
        cur = yield DB.execute(sql, (content, content))
        data = cur.fetchone()
    if data:
        if data.get('path'):
            data['path'] = FILE_HOST + data['path']
    raise gen.Return(data)
