# encoding=utf-8
import calendar
import datetime

from tornado import gen
from handler import ApiHandler
from store import statistics_model
from util import com
from cache import statistics_cache


class Report(ApiHandler):
    @gen.coroutine
    def get(self):
        machine_category_id = self.get_argument("machine_category_id", None)
        model_id = self.get_argument('model_id', None)
        tp = com.safeInt(self.get_argument('type', 1), 1)
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        # top_agency = self.get_argument('top_agency', '')
        if not start or not end:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        # now = datetime.datetime.now()
        # if tp == 1:
        #     start = (now - datetime.timedelta(days=now.weekday() + 7)).strftime('%Y-%m-%d')
        #     end = (now - datetime.timedelta(days=now.weekday() + 1)).strftime('%Y-%m-%d')
        # else:
        #     # 本月第一天和最后一天
        #     this_month_start = datetime.datetime(now.year, now.month, 1)
        #     # 上月第一天和最后一天
        #     end = (this_month_start - datetime.timedelta(days=1))
        #     start = datetime.datetime(end.year, end.month, 1).strftime('%Y-%m-%d')
        #     end = end.strftime('%Y-%m-%d')
        data = statistics_cache.getReport(tp, start, end, machine_category_id, model_id)
        # print data
        if data:
            raise gen.Return(self.success(data))
        # 年度寄修人数--台数
        year_people_number, year_machine_number = yield statistics_model.pr_year_number()
        # 当前所选时间新增寄修人数--台数
        new_people_number, new_machine_number = yield statistics_model.post_person_number_stat(
            start, end, machine_category_id, model_id)
        # 寄修人数--台数
        post_person_number = yield statistics_model.post_person_number(start, end, machine_category_id, model_id)
        # 已审核-已收货-已检测-已支付-已维修-已回寄数量统计
        pr_status_number = yield statistics_model.pr_status_number(start, end, machine_category_id, model_id)
        # 保内保外
        pr_in_period = yield statistics_model.pr_in_period(start, end, machine_category_id, model_id)
        # 损坏物料
        damage_accessory = yield statistics_model.damage_accessory(start, end, machine_category_id, model_id)
        # 途径
        channel = yield statistics_model.channel(start, end, machine_category_id, model_id)
        # 设备故障分布
        damage_distribute = yield statistics_model.damage_distribute(start, end, machine_category_id, model_id)
        # 耗时
        time_consuming = yield statistics_model.time_consuming(start, end, machine_category_id, model_id)
        # 平均耗时
        avg_time = yield statistics_model.avg_time(start, end, machine_category_id, model_id)
        # 客服知会耗时
        inform_time_consuming = yield statistics_model.inform_time_consuming(start, end, machine_category_id, model_id)
        # 客户支付耗时
        pay_time_consuming = yield statistics_model.pay_time_consuming(start, end, machine_category_id, model_id)
        ret = dict()
        time = dict()
        time['start'] = start
        time['end'] = end
        ret['time'] = time
        ret['channel'] = channel
        ret['post_person_number'] = post_person_number
        ret['pr_status_number'] = pr_status_number
        ret['pr_in_period'] = pr_in_period
        ret['damage_accessory'] = damage_accessory
        ret['year_people_number'] = year_people_number
        ret['year_machine_number'] = year_machine_number
        ret['new_people_number'] = new_people_number
        ret['new_machine_number'] = new_machine_number
        ret['damage_distribute'] = damage_distribute
        ret['time_consuming'] = time_consuming
        ret['avg_time'] = avg_time
        ret['inform_time_consuming'] = inform_time_consuming
        ret['pay_time_consuming'] = pay_time_consuming

        statistics_cache.setReport(tp, ret, start, end, machine_category_id, model_id)
        # print post_person_number
        raise gen.Return(self.success(ret))


class ReportRegionDistribute(ApiHandler):
    @gen.coroutine
    def get(self):
        machine_category_id = self.get_argument("machine_category_id", None)
        model_id = self.get_argument('model_id', None)
        tp = com.safeInt(self.get_argument('type', 1), 1)
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        region_type = com.safeInt(self.get_argument('region_type', 1), 1)  # 1 代理地区  2 行政地区
        if not start or not end:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        data = statistics_cache.getReportRegion(tp, start, end, machine_category_id, model_id, region_type)
        if data:
            raise gen.Return(self.success(data))
        if region_type == 1:
            # 代理分布
            data = yield statistics_model.agency_distribute(start, end, machine_category_id, model_id)
        else:
            # 行政分布
            data = yield statistics_model.administration_distribute(start, end, machine_category_id, model_id)
        statistics_cache.setReportRegion(tp, data, start, end, machine_category_id, model_id, region_type)
        raise gen.Return(self.success(data))


class ReportCategoryModelProportion(ApiHandler):
    @gen.coroutine
    def get(self):
        machine_category_id = self.get_argument("machine_category_id", None)
        model_id = self.get_argument('model_id', None)
        tp = com.safeInt(self.get_argument('type', 1), 1)
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        proportion_type = com.safeInt(self.get_argument('proportion_type', 1), 1)  # 1 品类分布  2 寄修分布
        if machine_category_id:
            proportion_type = 2
        if not start or not end:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        data = statistics_cache.getReportProportion(tp, start, end, machine_category_id, model_id, proportion_type)
        if data:
            raise gen.Return(self.success(data))
        if proportion_type == 1:
            # 品类分布
            data = yield statistics_model.category_proportion(start, end, machine_category_id, model_id)
        else:
            # 机型分布
            data = yield statistics_model.machine_proportion(start, end, machine_category_id, model_id)
        statistics_cache.setReportProportion(tp, data, start, end, machine_category_id, model_id, proportion_type)
        raise gen.Return(self.success(data))


class ReportRepeatOrder(ApiHandler):
    @gen.coroutine
    def get(self):
        machine_category_id = self.get_argument("machine_category_id", None)
        model_id = self.get_argument('model_id', None)
        tp = com.safeInt(self.get_argument('type', 1), 1)
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        proportion_type = com.safeInt(self.get_argument('proportion_type', 1), 1)  # 1 机型分布  2 维修人分布  3 故障分布
        if not start or not end:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        data = statistics_cache.getReportRepeatOrderProportion(tp, start, end, machine_category_id, model_id, proportion_type)
        if data:
            raise gen.Return(self.success(data))
        if proportion_type == 1:
            # 机型分布
            data = yield statistics_model.repeat_order_proportion_by_model(start, end, machine_category_id, model_id)
        elif proportion_type == 2:
            # 维修人分布
            data = yield statistics_model.repeat_order_proportion_by_repair_man(start, end, machine_category_id, model_id)
        else:
            # 故障分布
            data = yield statistics_model.repeat_order_proportion_by_damage(start, end, machine_category_id, model_id)
        statistics_cache.setReportRepeatOrderProportion(tp, data, start, end, machine_category_id, model_id, proportion_type)
        raise gen.Return(self.success(data))


class ReportDamageAccessoryDetail(ApiHandler):
    @gen.coroutine
    def get(self):
        model_id = self.get_argument('model_id', None)
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        mat_id = self.get_argument('mat_id', '')
        if not model_id or not mat_id:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        # 故障原因
        malfunction_reason = yield statistics_model.machine_malfunction(model_id, start, end, mat_id)
        accessory_damage_distribute = yield statistics_model.accessory_damage_distribute(model_id, start, end, mat_id)
        ret = dict()
        ret['malfunction_reason'] = malfunction_reason
        ret['accessory_damage_distribute'] = accessory_damage_distribute
        raise gen.Return(self.success(ret))


class BrokenScreenInsuranceStat(ApiHandler):
    @gen.coroutine
    def get(self):
        # machine_category_id = self.get_argument("machine_category_id", None)
        model_id = self.get_argument('model_id', None)
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        top_agency = self.get_argument('top_agency', '')
        second_agency = self.get_argument('second_agency', '')
        if (start and not end) or (not start and end):
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        if not top_agency and second_agency:
            raise gen.Return(self.error(u'请选择一级代理', com.ERROR_PARAM))
        # 获取总投保数 和已使用数量
        count, used_count = yield statistics_model.screen_insurance_total(start, end, model_id, top_agency,
                                                                          second_agency)
        agency_distribute = yield statistics_model.screen_insurance_agency_distribute(start, end, model_id, top_agency,
                                                                                      second_agency)
        model_distribute = yield statistics_model.screen_insurance_model_distribute(start, end, model_id, top_agency,
                                                                                    second_agency)
        used_model_distribute = yield statistics_model.screen_insurance_used_model_distribute(start, end, model_id,
                                                                                              top_agency, second_agency)
        ret = dict()
        ret['count'] = count
        ret['used_count'] = used_count
        ret['agency_distribute'] = agency_distribute
        ret['model_distribute'] = model_distribute
        ret['used_model_distribute'] = used_model_distribute
        raise gen.Return(self.success(ret))


class DailyStatistics(ApiHandler):
    @gen.coroutine
    def get(self):
        today = datetime.datetime.now()
        start_time = today.strftime('%Y-%m-%d 00:00:00')
        end_time = today.strftime('%Y-%m-%d %H:%M:%S')

        order_sign_stat = yield statistics_model.order_sign_stat(start_time, end_time)
        exp_go_stat = yield statistics_model.exp_go_stat(start_time, end_time)

        start_time = (datetime.datetime.now() - datetime.timedelta(7)).strftime('%Y-%m-%d 00:00:00')
        seven_day_repair_summary = yield statistics_model.seven_day_exp_go_stat(start_time, end_time)

        month_start_time = datetime.date(today.year, today.month, 1).strftime('%Y-%m-%d 00:00:00')
        damage_distribute = yield statistics_model.month_statistics_damage_distribute(
            month_start_time, end_time, 0, 10)
        damage_accessory = yield statistics_model.month_statistics_damage_accessory(month_start_time, end_time)
        daily_statistics_exp_go = yield statistics_model.daily_statistics_exp_go()
        repair_man_stat = yield statistics_model.repair_man_stat_connect(month_start_time, end_time)

        repeat_order_damage_distribute = yield statistics_model.month_statistics_damage_distribute(
            month_start_time, end_time, 1, 5)
        repeat_order_model_distribute = yield statistics_model.repeat_order_model_distribute(
            month_start_time, end_time)
        repeat_order_repair_man_distribute = yield statistics_model.repeat_order_repair_man_distribute(
            month_start_time, end_time)
        repair_users = yield statistics_model.repair_daily_users_record_list()
        print repair_users

        order_to_status_group_number = yield statistics_model.order_to_status_group_number()
        order_four_day_not_push_pay_number = yield statistics_model.order_four_day_not_push_pay_number()
        order_not_exp_go_count = yield statistics_model.order_not_exp_go_count()

        ret = dict()
        ret['order_sign_sum'] = order_sign_stat['order_sign']
        ret['exp_go_sum'] = exp_go_stat['exp_go_sum']
        ret['seven_day_repair_sum'] = seven_day_repair_summary
        ret['damage_distribute'] = damage_distribute
        ret['damage_accessory'] = damage_accessory
        ret['daily_statistics_exp_go'] = daily_statistics_exp_go
        ret['repair_man_stat'] = repair_man_stat
        ret['repeat_order_damage_distribute'] = repeat_order_damage_distribute
        ret['repeat_order_model_distribute'] = repeat_order_model_distribute
        ret['repeat_order_repair_man_distribute'] = repeat_order_repair_man_distribute
        # ret['repair_users'] = repair_users
        ret.update(repair_users)
        ret['to_customer_inform'] = order_to_status_group_number.get('to_customer_inform')
        ret['to_check_num'] = order_to_status_group_number.get('to_check_num')
        ret['to_pay_num'] = order_to_status_group_number.get('to_pay_num')
        ret['to_repair_num'] = order_to_status_group_number.get('to_repair_num')
        ret['to_exp_go_num'] = order_to_status_group_number.get('to_exp_go_num')
        ret['four_day_not_push_pay_num'] = order_four_day_not_push_pay_number
        ret['not_exp_go_count'] = order_not_exp_go_count
        raise gen.Return(self.success(ret))


class DailyStatisticsRepairMan(ApiHandler):
    @gen.coroutine
    def get(self):
        # 获取参数
        input_time = datetime.datetime.now()
        input_month = self.get_argument('month', '')
        if input_month and len(input_month) == 7:
            year_str = input_month[:4]
            month_str = input_month[5:]
            if year_str > '2017' and month_str >= '01' and month_str <= '12':
                year_int = int(year_str)
                month_int = int(month_str)
                if year_int > 2017 and month_int >= 1 and month_int <= 12:
                    month_day_count = calendar.monthrange(year_int, month_int)[1]
                    input_time = datetime.datetime(year_int, month_int, month_day_count, 23, 59, 59)
        month_start_time = datetime.date(input_time.year, input_time.month, 1).strftime('%Y-%m-%d 00:00:00')
        end_time = input_time.strftime('%Y-%m-%d %H:%M:%S')
        # 读取数据
        repeat_order_repair_man_distribute = yield statistics_model.repeat_order_repair_man_distribute(
            month_start_time, end_time)
        ret = repeat_order_repair_man_distribute
        # 筛选维修人员
        repair_man = self.get_argument('repair_man', '')
        if repair_man and len(repair_man) > 0 and len(repeat_order_repair_man_distribute) > 0:
            ret = filter(lambda x: x.get('name') == repair_man, repeat_order_repair_man_distribute)
        # ret = {
        #     'input_month': input_month,
        #     'input_time': input_time.strftime('%Y-%m-%d %H:%M:%S'),
        #   # 'year_int': year_int or None,
        #   # 'month_int': month_int or None,
        #     'month_start_time': month_start_time,
        #     'end_time': end_time,
        #     'data': repeat_order_repair_man_distribute
        # }
        raise gen.Return(self.success(ret))


class RepairDailyUsersRecord(ApiHandler):
    @gen.coroutine
    def post(self):
        director = self.get_argument('director', '')
        repair_man = self.get_argument('repair_man', '')
        quantity_man = self.get_argument('quantity_man', '')
        group_leader = self.get_argument('group_leader', '')
        material_man = self.get_argument('material_man', '')
        repair_man_num = self.get_argument('repair_man_num', '0')
        absentee_num = self.get_argument('absentee_num', '0')

        if not director and not repair_man and not quantity_man and not group_leader and not material_man and not \
                repair_man_num and not absentee_num:
            raise gen.Return(self.error('所有参数不能全为空', com.ERROR_PARAM))
        data = yield statistics_model.repair_daily_users_record(director, repair_man, quantity_man, group_leader,
                                                                material_man, repair_man_num, absentee_num)
        raise gen.Return(self.success(data) if data else self.error('系统错误', com.ERROR_SYSTEM))
