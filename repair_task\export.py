# encoding=utf-8
# __author__ = 'qry'
import datetime
import json
import pymysql
import sys
import time
import traceback

from openpyxl import Workbook
from tornado import gen
from tornado.log import app_log
reload(sys)
sys.setdefaultencoding('utf-8')


connect = pymysql.connect(
    host='rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',
    user='yxrepair',
    passwd='clC30J3DQLN3w7ma',
    db='post_repair',
    # db='readboydata',
    charset='utf8',
)
cursor = connect.cursor(cursor=pymysql.cursors.DictCursor)

connect_yx = pymysql.connect(
    host='rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',
    user='yxpy',
    passwd='83GhKv75FDn3PA2u',
    db='rbcare',
    charset='utf8mb4',
)
cursor_yx = connect_yx.cursor(cursor=pymysql.cursors.DictCursor)


save_path = '/data/www/post_repair_admin/public/excel/'


status = {100: '待审核', 200: '审核通过', -200: '审核不通过', 300: '用户已发货', -300: '用户发货失败',
          400: '已收货,未知会', 410: '已收货,已知会',480 :'未检测,已知会',490:'已检测,未知会',
          500: '已检测,已知会', 600: '用户已支付', 700: '已维修', -700: '已弃修',
          800: '已回寄', -800: '回寄失败', 900: '订单完成', -900: '已取消'}

in_period = {0: '无保修信息', 1: '保修期内', 2: '保修期外'}

connect_dict = {0: '未联系', 1: '联系成功', 2: '联系失败', 3: '已弃修', 4: '专柜与用户确认中', 5: '用户考虑中', 6: '已弃修，联系成功'}

has_warranty = {1: '有保卡', 0: '没有保卡'}

reason = {1: '人为损坏', 2: '元件损坏', 3: '全面检测，客户反应故障未复现', 4: '重新对机器进行系统升级', 5: '屏幕脱胶'}

come_exp_type = {0: '未选择邮寄方式', 1: '上门服务', 2: '自主寄件'}

pay_com = {0: '未知', 1: '微信', 2: '支付宝'}

is_agency = {0: '否', 1: '是'}

repeat_order = {0: "首次寄修", 1: "二次寄修", 2: "二次返修"}

post_repair_type = {1: '用户寄修', 2: '代理商寄修', 3: '终端代寄'}


def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')


def post_repair_manage_export():
    sql = 'select id, title, `sql` from export_record where type = \'\' and status = 0 and `lock` = 0 limit 1'
    cursor.execute(sql)
    data = cursor.fetchone()
    sql = 'select id, title, `sql` from export_record where type = \'\' and status = 0 and `lock` = 1 limit 1'
    cursor.execute(sql)
    data6 = cursor.fetchone()
    # 上一个导出结束才能到下一个
    if data6:
        data = None
    titles = [
        'id',
        '寄修订单号',
        '订单状态',
        '联系状态',
        '机器条码',
        '上传的机器号',
        '机型名称',
        '颜色',
        '保内保外',
        '有没有保卡',
        '损坏原因',
        '受损类型',
        '实际故障类型',
        '联系人',
        '电话号码',
        '省',
        '市',
        '区',
        '寄来快递类型',
        '公司内部寄来快递单号',
        '寄来快递单号',
        '寄来快递公司',
        '审核意见',
        '支付公司',
        '支付单号',
        '公司内部支付单号',
        '检查人',
        '维修处理',
        '配件费用',
        '快递费用',
        '总金额',
        '待付金额',
        '支付备注',
        '维修备注',
        '后台备注',
        '收到的配件',
        '寄去公司内部单号',
        '寄去快递单号',
        '寄去快递公司',
        '提交订单时间',
        '回寄下单时间',
        '是否终端寄修',
        '是否二次维修',
        '签收时间',
        '故障导致原因分析',
        "市场拆修备注",
        '寄修类型'
    ]
    sql = 'SELECT o.id, o.sn, o.status, o.connect, o.barcode, o.serial, o.model_name, o.color, o.in_period, ' \
          'o.has_warranty, o.reason, o.damage, o.name, o.phone, o.province, o.city, o.district, ' \
          'o.come_exp_type, o.rb_come_exp_sn, o.come_exp_sn, o.come_exp_com, o.audit_opinion, o.pay_com, ' \
          'o.pay_sn, o.rb_pay_sn, u.name as check_user_name, o.deal, o.accessory_cast, o.staff_cast, ' \
          'o.amount, o.pay_amount, o.deal_remark, o.receive_case, o.rb_go_exp_sn, o.go_exp_sn, ' \
          'o.go_exp_com, o.created_at, o.updated_at_last, o.is_agency, o.repeat_order, o.repeat_remark, ' \
          'o.type, o.receive_time, oe.pay_remark, oe.backstage_remark, oe.overhaul_remark ' \
          'FROM `order` o ' \
          'LEFT JOIN admin_users u ON o.check_man= u.id ' \
          'LEFT JOIN admin_users u2 ON o.auditor= u2.id ' \
          'LEFT JOIN order_extend oe ON o.sn = oe.sn '
    book = Workbook()
    sheet = book.active
    try:
        if data:
            print "开始" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 先锁住
            cursor.execute('update export_record set `lock` = 1 where id = %s', data['id'])
            connect.commit()
            col = 1
            row = 1
            for t in titles:
                sheet.cell(row=row, column=col).value = t
                col += 1
            row += 1

            wh = True
            offset = 0
            sql2 = sql + ' where ' + data['sql'] + ' and o.id > %s order by id '
            # print sql2
            while wh:
                cursor.execute(sql2 + " limit 500", offset)

                data2 = cursor.fetchall()
                # app_log.info(data2)
                # print data2
                for j in data2:
                    # print row
                    cursor.execute('SELECT GROUP_CONCAT(d.title) as damages FROM damage d '
                                              'RIGHT JOIN damage_order `do` ON d.id = `do`.damage_id '
                                              'WHERE `do`.order_id = %s', j['id'])
                    data3 = cursor.fetchone()

                    if data3:
                        j['damages'] = data3['damages']
                    else:
                        j['damages'] = ''
                    try:
                        sheet_add(j, row, sheet, 1)
                    except Exception as ee:
                        print 'row=', row, j
                        raise ee

                    row += 1

                    if j['repeat_order'] != 0:
                        cursor.execute(sql + ' where  o.id != %s AND o.status != -900 AND o.status != -200 '
                                             'AND o.barcode = %s AND o.created_at < %s',
                                                   [j['id'], j['barcode'], j['created_at']])
                        data4 = cursor.fetchall()
                        # print data4
                        if len(data4) > 0:
                            sheet_add(None, row, sheet, 2)
                            row += 1
                            for k in data4:

                                cursor.execute('SELECT GROUP_CONCAT(d.title) as damages FROM damage d '
                                                          'RIGHT JOIN damage_order `do` ON d.id = `do`.damage_id '
                                                          'WHERE `do`.order_id = %s', j['id'])
                                data5 = cursor.fetchone()
                                if data5:
                                    k['damages'] = data5['damages']
                                else:
                                    k['damages'] = ''
                                sheet_add(k, row, sheet, 1)
                                row += 1
                            row += 1
                if len(data2) < 500:
                    break
                offset = data2[499]['id'] 

            # book.save("repair_task\\" + data['title'].decode('utf-8') + ".xlsx")

            book.save("/data/www/post_repair_admin/public/excel/" + data['title'].decode('utf-8') + ".xlsx")
            book.close()
            url = "https://repair-hub.readboy.com/excel/" + data['title'].decode('utf-8') + ".xlsx"
            # url = "http://repair-test.readboy.com/excel/" + data['title'].decode('utf-8') + ".xlsx"
            cursor.execute('update export_record set `status` = 1, url = %s where id = %s', [url, data['id']])
            connect.commit()
        print "结束" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        traceback.print_exc()
        connect.rollback()
        cursor.execute('update export_record set `lock` = 0 where id = %s', data['id'])
        connect.commit()
        print "导出出错：%s" % e
        # book.save("repair_task\\" + i['title'].decode('utf-8') + ".xlsx")

def sheet_add(data, row, sheet, row_type=1):
    try:
        if row_type != 1:
            sheet.cell(row=row, column=1).value = "二次维修记录"
        else:
            col = 1
            columns = ['id', "sn", "status", "connect", "barcode", "serial", 'model_name', "color", "in_period", "has_warranty",
                       "reason", 'damage', "damages", "name", "phone", "province", 'city', "district", "come_exp_type",
                       "rb_come_exp_sn", "come_exp_sn", 'come_exp_com', "audit_opinion", "pay_com", "pay_sn", "rb_pay_sn",
                       'check_user_name', "deal", "accessory_cast", "staff_cast", "amount", 'pay_amount', "pay_remark",
                       "deal_remark", "backstage_remark", "receive_case", 'rb_go_exp_sn', "go_exp_sn", "go_exp_com",
                       "created_at", "updated_at_last", 'is_agency', "repeat_order", "receive_time", "repeat_remark",
                       "overhaul_remark", 'type']
            for i in columns:
                if i == "status":
                    sheet.cell(row=row, column=col).value = status[data[i]]
                elif i == "connect":
                    sheet.cell(row=row, column=col).value = connect_dict[data[i]]
                elif i == "in_period":
                    sheet.cell(row=row, column=col).value = in_period[data[i]]
                elif i == "has_warranty":
                    sheet.cell(row=row, column=col).value = has_warranty[data[i]]
                elif i == "reason":
                    sheet.cell(row=row, column=col).value = reason[data[i]]
                elif i == "come_exp_type":
                    sheet.cell(row=row, column=col).value = come_exp_type[data[i]]
                elif i == "pay_com":
                    sheet.cell(row=row, column=col).value = pay_com[data[i]]
                elif i == "is_agency":
                    sheet.cell(row=row, column=col).value = is_agency[data[i]]
                elif i == "repeat_order":
                    sheet.cell(row=row, column=col).value = repeat_order[data[i]]
                elif i == "type":
                    sheet.cell(row=row, column=col).value = post_repair_type[data[i]]
                else:
                    sheet.cell(row=row, column=col).value = data[i]
                col += 1
    except Exception as e:
        traceback.print_exc()
        raise e


def params_export(export_type, fn):
    ret = 0
    # 读取任务
    sql = 'select id, title, params from export_record where type = %s and status = 0 and `lock` = 0 limit 1'
    cursor.execute(sql, export_type)
    data = cursor.fetchone()
    sql = 'select id, title, params from export_record where type = %s and status = 0 and `lock` = 1 limit 1'
    cursor.execute(sql, export_type)
    data6 = cursor.fetchone()
    # 上一个导出结束才能到下一个
    if data6:
        data = None
    try:
        dura_begin = time.clock()
        if data:
            ret = 1
            print "%s 开始%s" % (export_type, _now_date())
            # 先锁住
            cursor.execute('update export_record set `lock` = 1 where id = %s', data['id'])
            connect.commit()
            # 读取
            print "{type} data:{data},".format(type=export_type, data=json.dumps(data))
            ret = fn(data)
            # 结束
            url = "/excel/" + data['title'].decode('utf-8') + ".xlsx"
            cursor.execute('update export_record set `status` = 1, url = %s, updated_at = %s where id = %s', [url, _now_date(), data['id']])
            connect.commit()
        dura_finish = time.clock()
        print "{type} 结束{time}, 耗时{dura:>8.1f}ms, 返回{ret},".format(
                type=export_type, time=_now_date(), ret=ret,
                dura=(dura_finish - dura_begin)*1000
            )
    except Exception as e:
        traceback.print_exc()
        connect.rollback()
        cursor.execute('update export_record set `lock` = 0 where id = %s', data['id'])
        connect.commit()
        ret = -1
        print "%s 导出出错：%s" % (export_type, e)
    return ret


import export_repair_material
import export_broken_screen_insurance


if __name__ == "__main__":
    post_repair_manage_export()

    params_fn_dict = {
        export_repair_material.export_type: export_repair_material.export,
        export_broken_screen_insurance.export_type: export_broken_screen_insurance.export
    }
    for t, f in params_fn_dict.items():
        ret = params_export(t, f)
        if ret != 0:
            break
    print
