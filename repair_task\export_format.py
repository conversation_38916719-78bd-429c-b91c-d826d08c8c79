#coding=utf-8
#encoding=utf-8
import datetime
from dateutil.relativedelta import relativedelta
from decimal import Decimal

from openpyxl.worksheet.table import Table
from openpyxl.utils.cell import get_column_letter

def worksheet_setup(ws, freeze_coordinate=None, table_title=None, column_width_dict=None):
    if freeze_coordinate and isinstance(freeze_coordinate, basestring):
        ws.freeze_panes = freeze_coordinate
    if table_title and isinstance(table_title, basestring):
        table_1_ref = ws.calculate_dimension()
        table_1 = Table(displayName=table_title, ref=table_1_ref)
        ws.add_table(table_1)
    if column_width_dict and isinstance(column_width_dict, dict):
        for c, w in column_width_dict.items():
            if isinstance(w, float) or isinstance(w, int):
                ws.column_dimensions[c].width = w + 0.62 # 偏差校准
            else:
                ws.column_dimensions[c].bestFit = True # 这个自适应列宽效果不行

def to_column_width_dict(key_column_width_dict, key_list):
    column_width_dict = {}
    if key_column_width_dict and isinstance(key_column_width_dict, dict) and key_list:
        for k, w in key_column_width_dict.items():
            i = 0
            try:
                i = key_list.index(k)
            except:
                continue
            column_width_dict[get_column_letter(i + 1)] = w
    return column_width_dict


def to_material_case_dict_key(pr_sn, material_id):
    pr_sn = pr_sn if isinstance(pr_sn, basestring) else ''
    return pr_sn + '_' + str(material_id)

def to_material_case_dict(material_case_list):
    return {to_material_case_dict_key(d.get('pr_sn'), d.get('material_id')) : d.get('title') for d in material_case_list}

def to_pay_info_dict(pay_info_list, pr_sn_list):
    dic = {}
    for sn in pr_sn_list:
        dic[sn] = filter(lambda x : x.get('pr_sn') == sn and x.get('is_paid') == 1, pay_info_list)
    return dic

def to_refund_info_dict(refund_info_list, pr_sn_list):
    dic = {}
    for sn in pr_sn_list:
        dic[sn] = filter(lambda x : x.get('pr_sn') == sn and x.get('is_refund') == 1, refund_info_list)
    return dic

def to_buy_date_dict(buy_date_list, barcode_list):
    dic = {}
    for barcode in barcode_list:
        for x in buy_date_list:
            if x.get('barcode') == barcode:
                dic[barcode] = x.get('buy_date')
                break
    return dic

def to_oa_dict(oa_list):
    dic = {}
    for di in oa_list:
        sn = di.get('pr_sn')
        if dic.get(sn):
            dic.get(sn).append(di)
        else:
            dic[sn] = [di]
    return dic


def di_filter_pay_time(di, other_data, start, end):
    flag = True
    if (not isinstance(start, datetime.datetime) and not isinstance(start, basestring)) \
    or (not isinstance(end, datetime.datetime) and not isinstance(end, basestring)):
        return flag
    pay_info_dict = other_data.get('pay_info_dict')
    if not pay_info_dict or not isinstance(pay_info_dict, dict):
        return flag
    sn = di.get('sn')
    pays = pay_info_dict.get(sn, [])
    pay_time = di.get('pay_time')
    if pays and len(pays) > 0: # 取最新支付记录的时间
        pay_time = pays[len(pays) - 1].get('updated_at')
    if (isinstance(start, datetime.datetime) and isinstance(end, datetime.datetime)) \
    and isinstance(pay_time, datetime.datetime) and (pay_time < start or pay_time > end):
        flag = False
    if (isinstance(start, basestring) and isinstance(end, basestring)) \
    and isinstance(pay_time, datetime.datetime) and (str(pay_time) < start or str(pay_time) > end):
        flag = False
    if (isinstance(start, basestring) and isinstance(end, basestring)) \
    and isinstance(pay_time, basestring) and (pay_time < start or pay_time > end):
        flag = False
    return flag

def di_filter_only_repair_charge(di):
    flag = True
    connect_v = di.get('connect')
    is_charge_v = di.get('is_charge')
    if is_charge_v == is_charge_only_repair and (not connect_v in connect_ar):
        flag = False
    return flag

def di_filter_repair_dismantle_fee(di):
    flag = True
    connect_v = di.get('connect')
    code = di.get('code')
    if code and code == '97004.00050' and (not connect_v in connect_ar):
        flag = False
    return flag

def row_handle_remove_key(list_of_row, key_list, key_remove_list):
    if isinstance(list_of_row, list) and len(list_of_row) > 0 \
    and isinstance(key_remove_list, list) and len(key_remove_list) > 0:
        for k in key_remove_list:
            ki = key_list.index(k)
            list_of_row[ki] = None

def row_handle_old_code(list_of_row, key_list):
    if isinstance(list_of_row, list) and len(list_of_row) > 0:
        # 获取索引
        i_name = key_list.index('name')
        i_code = key_list.index('code')
        i_old_code = key_list.index('old_code')
        # 取编码
        code = list_of_row[i_code]
        if isinstance(code, basestring):
            code = code.strip()
            if len(code) < 11: # 是旧编码要处理
                list_of_row[i_old_code] = list_of_row[i_code]
                list_of_row[i_code] = '97004.00040'
                list_of_row[i_name] = '维修服务费'

def row_handle_price_in(list_of_row, key_list):
    if isinstance(list_of_row, list) and len(list_of_row) > 0:
        # 获取索引
        i_price = key_list.index('price')
        i_discount_value = key_list.index('discount_value')
        i_oe_discount = key_list.index('oe_discount')
        i_mat_discount = key_list.index('mat_discount')
        i_price_in = key_list.index('price_in')
        # 取实收价列原始值
        price_in = list_of_row[i_price_in]
        if price_in == 0: # 只操作没记录实收价的旧数据
            price = list_of_row[i_price]
            discount_value = list_of_row[i_discount_value]
            oe_discount = list_of_row[i_oe_discount]
            mat_discount = list_of_row[i_mat_discount]
            if price > 0:
                price_in = price
            if oe_discount == 1 and mat_discount == 1 and discount_value > 0:
                price_in = price * discount_value
            list_of_row[i_price_in] = price_in

def row_handle_paid_reduce_refund(list_of_row, key_list):
    if isinstance(list_of_row, list) and len(list_of_row) > 0:
        # 获取索引
        i_paid_price = key_list.index('paid_price')
        i_refund_amount = key_list.index('refund_amount')
        i_paid_reduce_refund = key_list.index('paid_reduce_refund')
        # 取两个金额
        paid_price = list_of_row[i_paid_price]
        refund_amount = list_of_row[i_refund_amount]
        if isinstance(paid_price, Decimal) and isinstance(refund_amount, Decimal):
            list_of_row[i_paid_reduce_refund] = paid_price - refund_amount
        else:
            list_of_row[i_paid_reduce_refund] = None

def row_handle_to_staff_cast(list_of_row, key_list):
    if isinstance(list_of_row, list) and len(list_of_row) > 0:
        # 获取索引
        i_connect = key_list.index('connect')
        i_name = key_list.index('name')
        i_code = key_list.index('code')
        i_count = key_list.index('count')
        i_price = key_list.index('price')
        i_is_charge = key_list.index('is_charge')
        i_price_in = key_list.index('price_in')
        i_accessory_in_ar = key_list.index('accessory_in_ar')
        i_amount_in_ar = key_list.index('amount_in_ar')
        i_staff_cast = key_list.index('staff_cast')
        i_accessory_cast = key_list.index('accessory_cast')
        i_pay_amount = key_list.index('pay_amount')
        i_paid_price = key_list.index('paid_price')
        # 快递费计算
        connect_v = list_of_row[i_connect]
        accessory_in_ar = list_of_row[i_accessory_in_ar]
        amount_in_ar = list_of_row[i_amount_in_ar]
        staff_cast = list_of_row[i_staff_cast]
        staff_cast_calc = staff_cast
        accessory_cast = list_of_row[i_accessory_cast]
        pay_amount = list_of_row[i_pay_amount]
        paid_price = list_of_row[i_paid_price]
        ## 没收快递费的情况
        if paid_price == 0 or (accessory_cast == pay_amount and pay_amount == paid_price):
            staff_cast_calc = Decimal(0)
        ## 弃修时收的快递费
        if connect_v in [connect.get(x) for x in [3, 6]] and paid_price == amount_in_ar:
            staff_cast_calc = amount_in_ar - accessory_in_ar
        # 赋值
        list_of_row[i_name] = '快递费用'
        list_of_row[i_code] = '97004.00040'
        list_of_row[i_count] = 1
        list_of_row[i_is_charge] = '快递费用'
        list_of_row[i_price] = staff_cast
        list_of_row[i_price_in] = staff_cast_calc

def row_handle_balance_amount(list_of_row, key_list, di):
    if isinstance(list_of_row, list) and len(list_of_row) > 0:
        # 获取索引
        i_name = key_list.index('name')
        i_code = key_list.index('code')
        i_count = key_list.index('count')
        i_price = key_list.index('price')
        i_is_charge = key_list.index('is_charge')
        i_charge_type = key_list.index('charge_type')
        i_price_in = key_list.index('price_in')
        i_staff_cast = key_list.index('staff_cast')
        i_paid_price = key_list.index('paid_price')
        # 检查是否没配件又收了超过快递费的费用-以换代修
        balance_amount = di.get('balance_amount', Decimal(0))
        is_charge_v = list_of_row[i_is_charge]
        staff_cast = list_of_row[i_staff_cast]
        paid_price = list_of_row[i_paid_price]
        if balance_amount > 0 and not is_charge_v and paid_price > staff_cast:
            price = None
            if paid_price - staff_cast == balance_amount:
                price = balance_amount
            else:
                price = paid_price
            list_of_row[i_name] = '维修服务费'
            list_of_row[i_code] = '97004.00040'
            list_of_row[i_count] = 1
            list_of_row[i_price] = price
            list_of_row[i_is_charge] = is_charge[1]
            list_of_row[i_charge_type] = charge_type[1]
            list_of_row[i_price_in] = price

def row_handle_to_oa(list_of_row, key_list, oa_l):
    res = []
    if isinstance(list_of_row, list) and len(list_of_row) > 0:
        # 获取索引
        i_updated_at = key_list.index('updated_at')
        i_connect = key_list.index('connect')
        i_name = key_list.index('name')
        i_code = key_list.index('code')
        i_count = key_list.index('count')
        i_price = key_list.index('price')
        i_is_charge = key_list.index('is_charge')
        i_charge_type = key_list.index('charge_type')
        i_price_in = key_list.index('price_in')
        # 弃修的不导出
        connect_v = list_of_row[i_connect]
        if connect_v in [connect.get(x) for x in [3, 6]]:
            return res
        # 每个自选配件
        for oa in oa_l:
            # 复制一行再修改
            re = list_of_row[:]
            re[i_updated_at] = oa.get('created_at')
            re[i_name] = oa.get('m_name')
            re[i_code] = oa.get('m_code')
            re[i_count] = oa.get('count')
            re[i_price] = oa.get('sell_price')
            re[i_is_charge] = is_charge[is_charge_yes]
            re[i_charge_type] = '选购配件'
            re[i_price_in] = oa.get('price')
            res.append(re)
    return res


sql_log_str = "{type} sql:{no}, duration:{dura:>8.1f}ms, fetch_count:{count},"


y_or_n = {0: '否', 1: '是'}


bsi_status = {
    100: '待审核',
    200: '审核通过',
    -200: '审核不通过',
    300: '已支付和屏保已生效',
    -300: '支付超时',
    400: '已报修',
    -400: '订单关闭',
    500: '已过期',
    -500: '已作废',
    600: '已退保(已退款)',
    700: '超期退保',
}

bsi_usage_state_no = 0
bsi_usage_state_yes = 1
bsi_usage_state = {bsi_usage_state_no: '未使用', bsi_usage_state_yes: '已使用'}

bsi_buy_type_purchase = 1
bsi_buy_type_present = 2
bsi_buy_type = {
    bsi_buy_type_purchase: '购买',
    bsi_buy_type_present: '赠送',
}

bsi_trade_plat_no = 0
bsi_trade_plat_wx = 1
bsi_trade_plat_ali = 2
bsi_trade_plat = {
    bsi_trade_plat_no: '未支付',
    bsi_trade_plat_wx: '微信',
    bsi_trade_plat_ali: '支付宝',
}

def f_bsi_status(*args):
    if not args or len(args) == 0:
        return None
    return bsi_status.get(args[0], args[0])

def f_bsi_usage_state(*args):
    if not args or len(args) == 0:
        return None
    return bsi_usage_state.get(args[0], args[0])

def f_bsi_buy_type(*args):
    if not args or len(args) == 0:
        return None
    return bsi_buy_type.get(args[0], args[0])

def f_bsi_trade_plat(*args):
    if not args or len(args) == 0:
        return None
    return bsi_trade_plat.get(args[0], args[0])

def f_bsi_end_time(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 2 or not args[1] or not isinstance(args[1], dict):
        return None
    created_at = args[1].get('created_at')
    month = args[1].get('month')
    end_time = created_at + relativedelta(months=month)
    return end_time.date()

def f_agency_name(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 3 or not args[2] or not isinstance(args[2], dict):
        return None
    agency_dict = args[2].get('agency_dict')
    if not agency_dict or not isinstance(agency_dict, dict):
        return None
    return agency_dict.get(args[0])


import export

def f_order_status(*args):
    if not args or len(args) == 0:
        return None
    return export.status.get(args[0], args[0])

def f_in_period(*args):
    if not args or len(args) == 0:
        return None
    return export.in_period.get(args[0], args[0])

def f_has_warranty(*args):
    if not args or len(args) == 0:
        return None
    return export.has_warranty.get(args[0], args[0])

def f_reason(*args):
    if not args or len(args) == 0:
        return None
    return export.reason.get(args[0], args[0])

def f_come_exp_type(*args):
    if not args or len(args) == 0:
        return None
    return export.come_exp_type.get(args[0], args[0])

def f_pay_com(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 3 or not args[2] or not isinstance(args[2], dict):
        return None
    pay_info_dict = args[2].get('pay_info_dict')
    if not pay_info_dict or not isinstance(pay_info_dict, dict):
        return None
    sn = args[1].get('sn')
    pays = pay_info_dict.get(sn, [])
    pay_com = args[0]
    if pays and len(pays) > 0: # 取最新支付记录的支付公司
        pay_com = pays[len(pays) - 1].get('com', 0)
    return export.pay_com.get(pay_com, pay_com)

def f_is_agency(*args):
    if not args or len(args) == 0:
        return None
    return export.is_agency.get(args[0], args[0])

def f_repeat_order(*args):
    if not args or len(args) == 0:
        return None
    return export.repeat_order.get(args[0], args[0])

def f_post_repair_type(*args):
    if not args or len(args) == 0:
        return None
    return export.post_repair_type.get(args[0], args[0])


connect = export.connect_dict
connect_ar = [3, 6]

def f_connect(*args):
    if not args or len(args) == 0:
        return None
    return connect.get(args[0], args[0])

has_screen_insurance = {0: '没有碎屏保', 1: '有碎屏保'}

def f_has_screen_insurance(*args): # 第二个参数是sql查询出的字典
    if not args or len(args) == 0:
        return None
    if len(args) < 2 or not args[1] or not isinstance(args[1], dict):
        return None
    k = 1 if args[1].get('bsi_status') in [300, 400] else 0 # 碎屏保订单状态列的列名
    return has_screen_insurance.get(k)

used_material_mark = {0: '检测添加', 1: '维修添加'}

def f_used_material_mark(*args):
    if not args or len(args) == 0:
        return None
    return used_material_mark.get(args[0], args[0])

def f_used_screen_insurance(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 2 or not args[1] or not isinstance(args[1], dict):
        return None
    k = 1 if args[1].get('bsi_status') == 400 else 0
    return y_or_n.get(k)

def f_is_direct_sales(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 2 or not args[1] or not isinstance(args[1], dict):
        return None
    return y_or_n.get(args[0]) if args[1].get('bsi_status') in [300, 400] else '无碎屏保'

def f_pay_time(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 3 or not args[2] or not isinstance(args[2], dict):
        return None
    pay_info_dict = args[2].get('pay_info_dict')
    if not pay_info_dict or not isinstance(pay_info_dict, dict):
        return None
    sn = args[1].get('sn')
    pays = pay_info_dict.get(sn, [])
    pay_time = args[0]
    if pays and len(pays) > 0: # 取最新支付记录的时间
        pay_time = pays[len(pays) - 1].get('updated_at')
    return pay_time

def f_description(*args):
    if not args or len(args) == 0:
        return None
    if not isinstance(args[0], basestring):
        return args[0]
    return args[0].strip()

def f_machine_malfunction(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 3 or not args[2] or not isinstance(args[2], dict):
        return None
    material_case_dict = args[2].get('material_case_dict')
    if not material_case_dict or not isinstance(material_case_dict, dict):
        return None
    key = to_material_case_dict_key(args[1].get('sn', ''), args[1].get('material_id'))
    return material_case_dict.get(key, '') or ''

def f_price(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 2 or not args[1] or not isinstance(args[1], dict):
        return None
    charge_type = args[1].get('charge_type')
    price_first = args[1].get('price_first')
    return price_first if charge_type == 2 and price_first > 0 else args[0]

is_charge_no = 0
is_charge_yes = 1
is_charge_only_repair = 2
is_charge = {is_charge_no: '不收费', is_charge_yes: '收费', is_charge_only_repair: '仅弃修时收费'}

def f_is_charge(*args):
    if not args or len(args) == 0:
        return None
    return is_charge.get(args[0], args[0])

charge_type = {1: '客户价格', 2: '一代价格', 3: '特殊政策（免费）'}

def f_charge_type(*args):
    if not args or len(args) == 0:
        return None
    return charge_type.get(args[0], args[0])

def f_paid_price(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 3 or not args[2] or not isinstance(args[2], dict):
        return None
    pay_info_dict = args[2].get('pay_info_dict')
    if not pay_info_dict or not isinstance(pay_info_dict, dict):
        return None
    sn = args[1].get('sn')
    pays = pay_info_dict.get(sn, [])
    sum = reduce(lambda t,x : t + x.get('pay_amount', Decimal(0)), pays, Decimal(0))
    return sum

def f_refund_amount(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 3 or not args[2] or not isinstance(args[2], dict):
        return None
    refund_info_dict = args[2].get('refund_info_dict')
    if not refund_info_dict or not isinstance(refund_info_dict, dict):
        return None
    sn = args[1].get('sn')
    refunds = refund_info_dict.get(sn, [])
    sum = reduce(lambda t,x : t + x.get('refund_amount', Decimal(0)), refunds, Decimal(0))
    return sum

def f_buy_date(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 3 or not args[2] or not isinstance(args[2], dict):
        return None
    buy_date_dict = args[2].get('buy_date_dict')
    if not buy_date_dict or not isinstance(buy_date_dict, dict):
        return None
    barcode = args[1].get('barcode')
    buy_date = buy_date_dict.get(barcode)
    return buy_date

def f_discount_value(*args):
    if not args or len(args) == 0:
        return None
    if len(args) < 3 or not args[2] or not isinstance(args[2], dict):
        return None
    buy_date_dict = args[2].get('buy_date_dict')
    if not buy_date_dict or not isinstance(buy_date_dict, dict):
        return None
    barcode = args[1].get('barcode')
    # 按时间计算折扣
    buy_date = buy_date_dict.get(barcode)
    updated_at = args[1].get('updated_at')
    if not buy_date or not updated_at:
        return Decimal('1')
    buy_1 = buy_date + datetime.timedelta(days=395) # 过保一个月
    buy_2 = buy_date + datetime.timedelta(days=426) # 过保两个月
    if updated_at < buy_1:
        return Decimal('0.8')
    if updated_at < buy_2:
        return Decimal('0.85')
    return Decimal('1')

