# coding=utf-8

import pymysql.cursors
import util.coords as coords

from conf.config import MYSQL_DB
import cache.user_cache as region_cache

conn = pymysql.connect(host=MYSQL_DB['host'],
                       port=MYSQL_DB['port'],
                       user=MYSQL_DB['user'],
                       password=MYSQL_DB['passwd'],
                       database=MYSQL_DB['db'],
                       charset='utf8mb4',
                       cursorclass=pymysql.cursors.DictCursor,
                       autocommit=True)

rel_map = {}
region_map = {}

# filled: 0-not filled, 1-filled, 2-all
def _getRegionByType(tp, filled=2, pid=0):
    with conn.cursor() as cursor:
        sql = 'select region_id, parent_id, shortname from weather_region WHERE region_type=(%s)'
        if filled==0:
            sql += ' and area_id=0'
        elif filled==1:
            sql += ' and area_id<>0'
        if pid!=0:
            sql += ' and parent_id='+str(pid)
        cursor.execute(sql, tp)
        rets = cursor.fetchall()
        return rets


def _getAreaByRegionName(tp, name):
    name_fileds = ['prov_cn', 'city_cn', 'name_cn', 'name_cn']
    with conn.cursor() as cursor:
        name_filed = name_fileds[tp]
        sql  = 'select region_id, area_id from weather_areaid WHERE '+name_filed+' like %s order by `area_id` LIMIT 0, 1'
        # print sql
        cursor.execute(sql, name)
        ret = cursor.fetchone()
        return ret


def _getRels():
    with conn.cursor() as cursor:
        sql = 'select area_id, region_id from weather_areaid where region_id<>0'
        cursor.execute(sql)
        rels = cursor.fetchall()
        global rel_map
        rel_map = {rel['area_id']: rel['region_id'] for rel in rels}
        print rel_map


def _fillRegion(level, filled=2):
    regions = _getRegionByType(level, filled)
    with conn.cursor() as cursor:
        for region in regions:
            region_id = region['region_id']
            parent_id = region['parent_id']
            short_name = region['shortname']
            area_id = rel_map.get(region_id)
            if not area_id:
                area = _getAreaByRegionName(level, short_name)
                print short_name, area
                if area:
                    area_id = area['area_id']
                elif parent_id:
                    area_id = region_map.get(parent_id)
            if area_id:
                region_map[region_id] = area_id
                sql = 'update weather_region set area_id=(%s) where region_id=(%s)'
                cursor.execute(sql, (area_id, region_id))
            else:
                print 'level', level, 'no area id: region_id=', region_id
    pass


def _cacheRegion():
    with conn.cursor() as cursor:
        sql = 'select region_id, lng, lat, area_id from weather_region'
        cursor.execute(sql)
        regions = cursor.fetchall()
        # regions = cursor.fetchmany(10)
        print len(regions), regions[:10]
        region_cache.delRegionGeo()
        region_cache.delRegionRel()
        region_cache.setRegionGeoBatch(regions)
        region_cache.setRegionRelBatch(regions)


def endpoint_add_bdcoord():
    with conn.cursor() as cursor:
        cursor.execute('select id, lng, lat from endpoint')
        eps = cursor.fetchall()
        for ep in eps:
            lng, lat = float(ep['lng']), float(ep['lat'])
            blng, blat = coords.gcj02tobd09(lng, lat) if lng > 0 and lat > 0 else (0, 0)
            # ep['blng'] = blng
            # ep['blat'] = blat
            cursor.execute('update endpoint set blng=%s, blat=%s where id=%s', (round(blng, 6), round(blat, 6), ep['id']))
            print 'endpoint %d add %s' % (ep['id'], cursor.rowcount>0)

if __name__ == '__main__':
    try:
        # _getRels()
        # _fillRegion(0, filled=2)
        # _fillRegion(1, filled=2)
        # _fillRegion(2, filled=2)
        # _fillRegion(3, filled=2)

        # _cacheRegion()
        endpoint_add_bdcoord()
    finally:
        conn.close()