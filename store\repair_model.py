# encoding=utf-8
# __author__ = 'lch'
import hashlib
import time
import urllib
import uuid

import tornado

from store import DB2, DB_PR
# import util.com as com
from tornado import gen, httpclient
import datetime
import random
from store import log_model, warranty_model, wxpay_model, alipay_model
from tornado.log import app_log, access_log
import copy
from conf import config
import json
from log import db_log, wxpay_log, alipay_log
from util import com
from urllib import quote
from hashlib import md5, sha1
from dateutil.relativedelta import relativedelta
import urlparse

MAX_CLIENTS = 1000
httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)
HTTP_TIMEOUT = 30


def _changeTS(data, fields):
    """
    转换时间戳
    :param data: 数据
    :param fields: 需要转换时间戳的字段
    :return:
    """
    for i in fields:
        if data.get(i):
            data[i] = data[i].strftime('%Y-%m-%d %H:%M:%S')
    return data


def _image_to_str(data, fields):
    """
    数据中的文件数组转换成字符串
    :param data: 数据
    :param fields: 要转换的字段
    :return:
    """
    for i in fields:
        if data.get(i) and isinstance(data[i], list):
            rets = list()
            for img in data[i]:
                if img.startswith(config.OSS_CNAME):
                    ret = img[len(config.OSS_CNAME):]
                    rets.append(ret)
            s = json.dumps(rets)
            data[i] = s
        else:
            data[i] = ''
    return data


def _image_to_json(data, fields):
    """
    数据中的文件字符串转换成json数组
    :param data: 数据
    :param fields: 要转换的字段
    :return:
    """
    if data:
        for i in fields:
            rets = []
            if data.get(i):
                imgs = json.loads(data[i])
                rets = [config.OSS_CNAME + img for img in imgs]
            data[i] = rets
    return data


def in_period(warranty):
    """
    检查是否保修期内
    :param warranty:
    :return:
    """
    ret = 0
    if warranty and warranty.get('status') == 1 and warranty.get('buy_date'):
        buy_day = datetime.datetime.strptime(warranty['buy_date'], '%Y.%m.%d')
        now = datetime.datetime.now().date()
        # print (buy_day + datetime.timedelta(days=cond)).strftime('%Y%m%d')
        period = (buy_day + relativedelta(years=1) - relativedelta(days=1)).date()
        # 到期时间 购买时间
        start = datetime.datetime.strptime('2020.01.20', '%Y.%m.%d').date()
        end = datetime.datetime.strptime('2020.02.29', '%Y.%m.%d').date()
        # 购买时间在1月20到2月29之间 延保3个月
        if start <= buy_day.date() <= end:
            period = (buy_day + relativedelta(years=1, months=3) - relativedelta(days=1)).date()
        if period >= now:
            ret = 1
        else:
            ret = 2
        in_period_date = datetime.datetime.strptime('2020.03.31', '%Y.%m.%d').date()
        # 到期时间在2020年1月20到2月29之间 延保到3月31日
        if start <= period <= end and now <= in_period_date:
            ret = 1
    return ret


def period(warranty):
    """
    检查是否保修期内
    :param warranty:
    :return:
    """
    ret = ''
    if warranty and warranty['buy_date']:
        time = datetime.datetime.strptime(warranty['buy_date'], '%Y.%m.%d').date() + datetime.timedelta(days=365)
        ret = time.strftime('%Y/%m/%d')
    return ret

@gen.coroutine
def check_black_list(barcode):
    """
    检查是否在黑名单中
    :param barcode: 条码
    :param phone: 手机号码
    :return:
    """
    ret = 0
    sql = 'select * from `repair_black_list` where barcode=%s '
    param = tuple([barcode])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    if data:
        ret = 1
    else:
        sql_2 = 'select * from `locked_device` where barcode=%s and status=1 '
        cur = yield DB2.execute(sql_2, param)
        data = cur.fetchone()
        if data:
            ret = 1
    raise gen.Return(ret)

@gen.coroutine
def check_black_phone(phone):
    """
    检查是否在黑名单中
    :param phone: 手机号码
    :return:
    """
    ret = 0
    sql = 'select * from `repair_black_list` where phone=%s and type=1'
    param = tuple([phone])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    if data:
        ret = 1
    raise gen.Return(ret)

@gen.coroutine
def check_special(barcode):
    """
    检查是是特批出库，如果是的话，默认保外
    :param barcode: 条码
    :return:
    """
    ret = 0
    sql = 'select * from `special_sales` where sn=%s '
    param = tuple([barcode])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    if data:
        ret = 1
    raise gen.Return(ret)

@gen.coroutine
def barcode_repeat(barcode):
    """
    检查该机器30天内有没有申请寄修
    之前是30天  现在改成90天了    还分3个月内寄修的为二次返修   三个月外重复寄修的为二次寄修   不然为首次
    wei xiu bu de zhu guan zhen bu kao pu
    :param barcode: 机器条码
    :return:
    """
    ret = 0
    if barcode:
        sql = 'select updated_at_last from `order` where status=900 and barcode=%s order by updated_at_last desc'
        param = tuple([barcode])
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data and data.get('updated_at_last') and data.get('updated_at_last') != '0000-00-00 00:00:00':
            create_time = data['updated_at_last'].date()
            now = datetime.datetime.now().date()
            if create_time + datetime.timedelta(days=90) > now:
                ret = 2
            else:
                ret = 1
            yield DB_PR.execute("insert into order_bug_log (barcode, repeat_order, updated_at_last, created_at) "
                                "values (%s,%s,%s,%s)", (barcode, ret, data.get("updated_at_last"), now))
    raise gen.Return(ret)

@gen.coroutine
def ar_repeat(barcode):
    """
    获取该机器在此之前的弃修次数
    :param barcode: 机器条码
    :return: 该机器弃修订单数量
    """
    ret = 0
    if barcode:
        sql = 'select connect from `order` where barcode=%s order by id desc'
        param = tuple([barcode])
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchall()
        if data and len(data) > 0:
            mat = filter(lambda x: 'connect' in x and x['connect'] in (3, 6), data)
            ret = len(mat)
    raise gen.Return(ret)

@gen.coroutine
def damage(category_id, count=0):
    """
    获取受损类型
    :param category_id: 机型品类id
    :param count: 最多获取多少个
    :return:
    """
    if not count:
        count = 8
    sql = 'select id, title, image_front, image_back from damage where machine_category_id=%s and visible=1 limit %s'
    param = tuple([category_id, count])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def repair_history(uid, barcode, subSn, date=None, number=10):
    """
    个人寄修历史列表
    :param uid: 用户id
    :param barcode: 机器条码
    :param subSn: 寄修单号后六位
    :param date: 小于日期时间
    :param number: 多少条
    :return:
    """
    if not date:
        date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    param = [uid, date]
    sql = '''SELECT
                model_name,
                sn,
                created_at,
                status,
                appraise
            FROM
                `order`
            WHERE
                uid = %s
                AND created_at < %s
            '''
    if barcode and subSn:
        sql += ''' AND (barcode = %s 
                        OR SUBSTRING(sn, -6) = %s) '''
        param.extend([barcode, subSn])
    elif barcode:
        sql += ' AND barcode = %s '
        param.append(barcode)
    elif subSn:
        sql += ' AND SUBSTRING(sn, -6) = %s '
        param.append(subSn)
    sql += ''' ORDER BY
                    created_at DESC
                LIMIT %s '''
    param.append(number + 1)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    l = len(data)
    is_end = l < number + 1
    size = l if is_end else number
    data = data[0:size]
    for i in data:
        i = _changeTS(i, ['created_at'])
        if i.get('status') in [410, 480, 490]:
            i['status'] = 400
    ret = {
        'data': data,
        'is_end': is_end,
        'size': size
    }
    raise gen.Return(ret)


def _unique_sn(sn=None, t=0):
    """
    生成唯一流水号字符串
    :param sn: 已有流水号生成关联号
    :param t: 关联类型，1寄来快递单，2寄去快递单
    :return:
    """
    if sn and t:
        s = sn + '%04d' % t
        return s
    else:
        ts = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        rand = random.randint(100000, 999999)
        s = ts + str(rand)
        return s


@gen.coroutine
def _unique_pay_sn(sn=None):
    """
    生成唯一支付流水号字符串
    :param sn: 已有流水号生成关联号
    :return:
    """
    while True:
        if sn:
            rand = random.randint(1000, 9999)
            s = sn + 'jxzf' + str(rand)
        else:
            ts = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            rand = random.randint(100000, 999999)
            s = ts + str(rand)
        sql = 'select count(*) as count from pay_order where readboy_sn=%s'
        param = tuple([s])
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if not data.get('count'):
            break
    raise gen.Return(s)


@gen.coroutine
def _unique_refund_sn(sn=None):
    """
    生成唯一支付流水号字符串
    :param sn: 已有流水号生成关联号
    :return:
    """
    while True:
        if sn:
            rand = random.randint(1000, 9999)
            s = sn + 'jxtk' + str(rand)
        else:
            ts = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            rand = random.randint(100000, 999999)
            s = ts + str(rand)
        sql = 'select count(*) as count from refund_order where readboy_sn=%s'
        param = tuple([s])
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if not data.get('count'):
            break
    raise gen.Return(s)


def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')


@gen.coroutine
def add_log(data):
    key = ['pr_sn', 'pr_status', 'log_status', 'log_from', 'relation_key', 'operation', 'uid', 'admin', 'title',
           'remark', 'date']
    default = {
        'pr_sn': data.get('pr_sn', ''),
        'pr_status': data.get('pr_status', 0),
        'log_status': data.get('log_status', 0),
        'log_from': data.get('log_from', ''),
        'relation_key': data.get('relation_key', ''),
        'operation': data.get('operation', ''),
        'uid': data.get('uid', 0),
        'admin': data.get('admin', 0),
        'title': data.get('title', ''),
        'remark': data.get('remark', ''),
        'date': data.get('date', _now_date()),
    }
    insert = {i: default[i] for i in key}
    yield log_model.store_order_log(insert)
    raise gen.Return(True)


@gen.coroutine
def add_repair(uid, data):
    """
    提交订单
    :param uid: 用户id
    :param data: 订单数据
    :return:
    """
    if not uid or not isinstance(data, dict):
        raise gen.Return(False)
    ### 将数据预处理
    data['uid'] = uid
    data['sn'] = _unique_sn()
    data['created_at'] = _now_date()
    data['status'] = 100
    data = _image_to_str(data, ['period_file', 'upload_file', 'video_file'])
    key = [
        'barcode',
        'model_name',
        'imei',
        'color',
        'model_id',
        'serial',
        'in_period',
        'has_warranty',
        'in_si_period',
        'has_screen_insurance',
        'reason',
        'damage',
        'period_file',
        'upload_file',
        'video_file',
        'description',
        'name',
        'phone',
        'province',
        'city',
        'district',
        'address',
        'come_exp_type',
        'repair_endpoint',
        'uid',
        'created_at',
        'sn',
        'status',
        'repeat_order',
        'ar_repeat',
        'need_invoice',
        'invoice_type',
        'invoice_title',
        'invoice_tax_id',
        'invoice_email',
    ]
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    param = tuple([data.get(i) for i in key])
    sql = 'insert into `order` (' + fields + ') values (' + values + ')'
    app_log.info(sql % param)
    try:
        cur = yield DB_PR.execute(sql, param)
        yield DB_PR.execute('insert into order_extend (sn, external_fault, internal_fault, created_at , is_tell) '
                            'values (%s, %s, %s, %s , %s)',
                            (data['sn'], data['created_at'], data.get('external_fault', 0),
                             data.get('internal_fault', 0) , data.get('is_tell', 0)))
        log = {
            'pr_sn': data.get('sn', ''),
            'pr_status': data.get('status', 0),
            'log_status': data.get('status', 0),
            'log_from': data.get('log_from', 'app'),
            'relation_key': data.get('relation_key', ''),
            'operation': data.get('operation', 'create_order'),
            'uid': data.get('uid', 0),
            'admin': data.get('admin', 0),
            'title': data.get('title', '寄修订单提交，寄修服务中心等待接收中'),
            'remark': data.get('remark', ''),
            'date': data.get('date', _now_date()),
        }
        add_log(log)
    except Exception as e:
        app_log.info('add order insert error:', exc_info=True)
        raise gen.Return(False)
    raise gen.Return(data)


@gen.coroutine
def check_order(uid, sn):
    """
    检查用户是否有权限操作订单
    :param uid: 用户id
    :param sn: 订单流水号
    :return:
    """
    ret = 0
    sql = 'select  uid, status from `order` where sn=%s'
    try:
        cur = yield DB_PR.execute(sql, sn)
        data = cur.fetchone()
        if data.get('status'):
            # 需要加终端代寄账户寄修检测
            if uid == data.get('uid'):
                ret = data['status']

            # 终端代寄 用户未绑定订单的情况下  终端帮用户确认收货  查此用户是否有此小单
            elif data.get('uid') == 0:
                sql = 'SELECT ao.uid from agent_order ao ' \
                      'RIGHT JOIN agent_order_correlation aoc ON ao.sn = aoc.agent_order_sn ' \
                      'WHERE aoc.order_sn = %s AND ao.type = 2 and ao.uid = %s '
                cur2 = yield DB_PR.execute(sql, (sn, uid))
                data2 = cur2.fetchone()
                if data2:
                    ret = data['status']
    except:
        ret = 0
    raise gen.Return(ret)


@gen.coroutine
def change_order(sn, data):
    """
    用户更新自己订单数据
    :param sn: 订单流水号
    :param data: 修改的数据
    :return:
    """
    data['updated_at'] = _now_date()
    data['sn'] = sn
    data['status'] = 300
    key = ['come_exp_type', 'come_exp_com', 'come_exp_sn', 'updated_at', 'status', 'sn']
    sql = 'update `order` set come_exp_type=%s, come_exp_com=%s, come_exp_sn=%s, updated_at=%s, status=%s where sn=%s'
    param = tuple([data.get(i) for i in key])
    try:
        cur = yield DB_PR.execute(sql, param)
        log = {
            'pr_sn': data.get('sn', ''),
            'pr_status': data.get('status', 0),
            'log_status': data.get('status', 0),
            'log_from': data.get('log_from', 'app'),
            'relation_key': data.get('relation_key', ''),
            'operation': data.get('operation', 'exp_come_sure'),
            'uid': data.get('uid', 0),
            'admin': data.get('admin', 0),
            'title': data.get('title', '寄修产品发货成功，维修中心等待接收中'),
            'remark': data.get('remark', ''),
            'date': data.get('date', _now_date()),
        }
        add_log(log)
    except:
        app_log.error('update order error', exc_info=True)
        raise gen.Return(False)
    raise gen.Return(True)


@gen.coroutine
def finish_order(sn):
    """
    确认收货后完成订单
    :param sn: 订单流水号
    :return:
    """
    data = dict()
    data['updated_at'] = _now_date()
    data['sn'] = sn
    data['status'] = config.ORDER_FINISH
    key = ['updated_at', 'status', 'sn']
    sql = 'update `order` set go_received=1, updated_at=%s, status=%s where sn=%s'
    param = tuple([data.get(i) for i in key])
    try:
        cur = yield DB_PR.execute(sql, param)
        log = {
            'pr_sn': data.get('sn', ''),
            'pr_status': data.get('status', 0),
            'log_status': data.get('status', 0),
            'log_from': data.get('log_from', 'app'),
            'relation_key': data.get('relation_key', ''),
            'operation': data.get('operation', 'finish_order'),
            'uid': data.get('uid', 0),
            'admin': data.get('admin', 0),
            'title': data.get('title', '回寄产品已被接收，寄修服务结束'),
            'remark': data.get('remark', ''),
            'date': data.get('date', _now_date()),
        }
        add_log(log)
    except:
        app_log.error('finish_order error', exc_info=True)
        raise gen.Return(False)
    raise gen.Return(True)


@gen.coroutine
def appraise_order(sn, data):
    """
    完成订单后评价订单
    :param sn: 订单流水号
    :param data: 评价数据
    :return:
    """
    data['updated_at'] = _now_date()
    data['sn'] = sn
    data['pr_sn'] = sn
    key = ['updated_at', 'sn']
    sql = 'update `order` set appraise=1, updated_at=%s where sn=%s'
    param = tuple([data.get(i) for i in key])
    trans = yield DB_PR.begin()
    try:
        yield trans.execute(sql, param)
        key = ['pr_sn', 'uid', 'star', 'description', 'updated_at']
        update = '=%s,'.join(key) + '=%s'
        fields = ','.join(key)
        values = ','.join(['%s'] * len(key))
        sql = 'insert into pr_appraise (' + fields + ') values (' + values + ') ON DUPLICATE KEY UPDATE ' + update
        param = tuple([data.get(i) for i in key] * 2)
        yield trans.execute(sql, param)
        trans.commit()
    except:
        app_log.error('appraise_order error', exc_info=True)
        trans.rollback()
        raise gen.Return(False)
    raise gen.Return(True)


@gen.coroutine
def order_log(sn):
    """
    获取订单日志记录
    :param sn: 订单流水号
    :return:
    """
    sql_log = 'select * from order_log where pr_sn=%s order by date DESC, id DESC'
    param_log = tuple([sn])
    ret = []
    try:
        cur = yield DB_PR.execute(sql_log, param_log)
        data = cur.fetchall()
        had_inform = any([x.get('pr_status') in [410, 480, 490, 500, 600, 700, -700, 800] for x in data])
        for di in data:
            if not di.get('pr_status'):
                continue
            if di.get('pr_status') == 400:
                if had_inform:
                    di['title'] = '维修终端已收货，客服已知会'
                sign_in_info = yield order_sign_in_info(sn)
                for k, v in sign_in_info.items():
                    di[k] = v
                di = _image_to_json(di, ['sign_in_pictures', 'sign_in_videos'])
            di = _changeTS(di, ['date'])
            if di.get('pr_status') not in [410, 480, 490]:
                ret.append(di)
    except:
        app_log.error('order log error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(ret)


@gen.coroutine
def order_sign_in_info(sn):
    fields = ['sign_in_pictures', 'sign_in_videos', 'sign_in_status']
    sql = 'select {fields} from `order_extend` where sn = %s '.format(fields=','.join(fields))
    param = tuple([sn])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def order_info(sn):
    """
    订单详情
    :param sn: 订单流水号
    :return:
    """
    key = ['o.*', 'oe.ar_rb_pay_sn', 'oe.ar_pay_com', 'oe.ar_pay_sn', 'oe.ar_is_paid', 'oe.ar_pay_time']
    fields = ','.join(key)
    param = tuple([sn])
    sql = 'select ' + fields + ' from `order` o left join order_extend oe on o.sn = oe.sn where o.sn=%s'
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data:
            if data['status'] in [410, 480, 490]:
                data['status'] = 400
            data = _changeTS(data, ['created_at', 'updated_at', 'updated_at_last', 'receive_time', 'pay_time', 'ar_pay_time'])
            data = _image_to_json(data, ['period_file', 'upload_file', 'repair_image', 'video_file'])
        # if data and data.get('repair_endpoint'):
        #     data['endpoint'] = yield endpoint(data['repair_endpoint'])
    except:
        app_log.error('order info error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(data)


@gen.coroutine
def endpoints(city=0, district=0):
    """
    获取终端列表
    :param city: 城市
    :param district: 镇区
    :return:
    """
    data = dict()
    data['city'] = city
    data['district'] = district
    key = ['city', 'district']
    key_c = copy.copy(key)
    for i in key_c:
        if not data.get(i):
            key.remove(i)
    where = '=%s, and '.join(key) + '=%s and' if len(key) else ''
    field_key = [
        'id',
        'name',
        'contact',
        'phone',
        'province',
        'city',
        'district',
        'p.region_name as province_name',
        'c.region_name as city_name',
        'd.region_name as district_name',
        'address',
    ]
    field = ', '.join(field_key)
    sql = 'select ' + field + ' from pr_endpoint left join region_view as p on pr_endpoint.province=p.region_id left join region_view as c on pr_endpoint.city=c.region_id left join region_view as d on pr_endpoint.district=d.region_id where ' + \
          where + ' status=1'
    param = tuple([data.get(i) for i in key])
    try:
        cur = yield DB_PR.execute(sql, param)
        ret = cur.fetchall()
    except:
        app_log.error('endpoints get error', exc_info=True)
        ret = None
    raise gen.Return(ret)


@gen.coroutine
def endpoint(id):
    """
    获取终端列表
    :param id: 寄修终端id
    :return:
    """
    if not id:
        raise gen.Return(None)
    field_key = [
        'id',
        'name',
        'contact',
        'phone',
        'province',
        'city',
        'district',
        'p.region_name as province_name',
        'c.region_name as city_name',
        'd.region_name as district_name',
        'address',
    ]
    where = 'id=%s and'
    field = ', '.join(field_key)
    sql = 'select ' + field + ' from pr_endpoint left join region_view as p on pr_endpoint.province=p.region_id left join region_view as c on pr_endpoint.city=c.region_id left join region_view as d on pr_endpoint.district=d.region_id where ' + \
          where + ' status=1'
    param = tuple([id])
    try:
        cur = yield DB_PR.execute(sql, param)
        ret = cur.fetchone()
    except:
        app_log.error('endpoint get error', exc_info=True)
        ret = None
    raise gen.Return(ret)


@gen.coroutine
def malfunction(sn):
    """
    获取订单故障
    :param sn: 订单流水号
    :return:
    """
    sql = 'select m_m.title from pr_malfunction as pr_m left join machine_malfunction as m_m on pr_m.malfunction_id=m_m.id where pr_m.pr_sn=%s'
    param = tuple([sn])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchall()
    except:
        app_log.error('malfunction get error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(data)


@gen.coroutine
def accessory(sn):
    """
    获取订单配件
    :param sn:
    :return:
    """
    sql = 'select m.price, mat.title, pr_m.count, is_charge from pr_material as pr_m ' \
          'left join material as m on pr_m.material_id=m.id ' \
          'left join machine_accessory_tree as mat on pr_m.mat_id=mat.id where pr_m.pr_sn=%s'
    param = tuple([sn])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchall()
    except:
        app_log.error('accessory get error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(data)


@gen.coroutine
def contacts(uid, endpoint_id=0):
    """
    获取用户的联系人列表
    :param endpoint_id:
    :param uid: 用户id
    :return:
    """
    key = [
        'ct.id',
        'name',
        'phone',
        'province',
        'city',
        'district',
        'p.region_name as province_name',
        'c.region_name as city_name',
        'd.region_name as district_name',
        'address',
    ]
    fields = ','.join(key)
    sql = 'select ' + fields + ' from contact as ct ' \
                               'left join region_view as p on ct.province=p.region_id ' \
                               'left join region_view as c on ct.city=c.region_id ' \
                               'left join region_view as d on ct.district=d.region_id '
    if endpoint_id:
        sql = sql + 'where ct.endpoint_id= %s order by ct.id desc'
        param = [endpoint_id]
    else:
        sql = sql + 'where ct.uid=%s and ct.endpoint_id = 0 order by ct.id desc'
        param = tuple([uid])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchall()
    except:
        app_log.erro('contacts get error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(data)


@gen.coroutine
def contact_one(uid):
    """
    无默认联系人获取用户的联系人列表中第一条
    :param uid: 用户id
    :return:
    """
    key = [
        'ct.id',
        'name',
        'phone',
        'province',
        'city',
        'district',
        'p.region_name as province_name',
        'c.region_name as city_name',
        'd.region_name as district_name',
        'address',
    ]
    fields = ','.join(key)
    sql = 'select ' + fields + ' from contact as ct ' \
                               'left join region_view as p on ct.province=p.region_id ' \
                               'left join region_view as c on ct.city=c.region_id ' \
                               'left join region_view as d on ct.district=d.region_id where uid=%s order by ct.id desc'
    param = tuple([uid])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
    except:
        app_log.error('contacts get error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(data)


@gen.coroutine
def contact(id):
    """
    获取联系人信息
    :param id: 联系人id
    :return:
    """
    key = [
        'ct.id',
        'name',
        'phone',
        'province',
        'city',
        'district',
        'p.region_name as province_name',
        'c.region_name as city_name',
        'd.region_name as district_name',
        'address',
    ]
    fields = ','.join(key)
    sql = 'select ' + fields + ' from contact as ct ' \
                               'left join region_view as p on ct.province=p.region_id ' \
                               'left join region_view as c on ct.city=c.region_id ' \
                               'left join region_view as d on ct.district=d.region_id where id=%s'
    param = tuple([id])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
    except:
        app_log.error('contact get error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(data)


@gen.coroutine
def get_default_contact(uid, endpoint_id=0):
    """
    获取用户默认联系人id
    :param endpoint_id:
    :param uid: 用户id
    :return:
    """
    key = ['contact_id']
    fields = ','.join(key)
    sql = 'select ' + fields + ' from admin_users where id=%s'
    param = tuple([uid])
    if endpoint_id:
        sql = 'select au.contact_id from admin_users au right join contact c on au.contact_id = c.id ' \
              'where c.endpoint_id = %s and au.id = %s'
        param = tuple([endpoint_id, uid])
    ret = 0
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data and data.get('contact_id'):
            ret = data['contact_id']
    except:
        app_log.error('get default contact error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def set_default_contact(uid, contact_id):
    """
    设置用户默认联系人
    :param uid: 用户id
    :param contact_id: 联系人id
    :return:
    """
    sql = 'update admin_users set contact_id=%s where id=%s'
    param = tuple([contact_id, uid])
    ret = False
    try:
        cur = yield DB_PR.execute(sql, param)
        ret = True
    except:
        app_log.error('get default contact error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def drop_contact(uid, contact_id, endpoint_id=0):
    """
    删除联系人
    :param endpoint_id:
    :param uid: 用户id
    :param contact_id: 联系人id
    :return:
    """
    if endpoint_id:
        sql = 'delete from contact where id=%s and endpoint_id = %s'
        param = tuple([contact_id, endpoint_id])
    else:
        sql = 'delete from contact where id=%s and uid=%s'
        param = tuple([contact_id, uid])
    ret = False
    try:
        cur = yield DB_PR.execute(sql, param)
        ret = True
    except:
        app_log.error('drop contact error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def add_contact(uid, endpoint_id, data, set_default):
    """
    新增一个用户联系人
    :param endpoint_id:
    :param uid: 用户id
    :param data: 联系人信息
    :param set_default: 是否设置为默认联系人
    :return:
    """
    data['created_at'] = _now_date()
    data['uid'] = uid
    data['endpoint_id'] = endpoint_id
    key = ['uid', 'name', 'phone', 'province', 'city', 'district', 'address', 'created_at']
    for i in key:
        if not data.get(i, None):
            raise gen.Return(False)
    key.append('endpoint_id')
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    sql = 'insert into contact (' + fields + ') values (' + values + ')'
    param = tuple([data.get(i) for i in key])
    trans = yield DB_PR.begin()
    ret = False
    try:
        cur = yield trans.execute(sql, param)
        if set_default and cur.lastrowid > 0:
            contact_id = cur.lastrowid
            yield set_default_contact(uid, contact_id)
        yield trans.commit()
        ret = True
    except:
        yield trans.rollback()
        app_log.error('add contact error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def update_contact(contact_id, data):
    """
    更新联系人
    :param contact_id: 联系人id
    :param data: 联系人数据
    :return:
    """
    data['updated_at'] = _now_date()
    data['id'] = contact_id
    key = ['name', 'phone', 'province', 'city', 'district', 'address', 'updated_at']
    key_c = copy.copy(key)
    for i in key_c:
        if not data.get(i):
            key.remove(i)
    fields = '=%s,'.join(key) + '=%s'
    where_key = ['id']
    where_fields = '=%s'.join(where_key) + '=%s'
    sql = 'update contact set ' + fields + ' where ' + where_fields
    param = tuple([data.get(i) for i in key] + [data.get(i) for i in where_key])
    ret = False
    try:
        cur = yield DB_PR.execute(sql, param)
        if cur.rowcount > 0:
            ret = True
    except:
        app_log.error('update contact error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def explain(key):
    """
    获取预设配置说明
    :param key: 说明标识，如客服电话：service_phone
    :return:
    """
    sql = 'select `value` from `explain` where `key`=%s'
    param = tuple([key])
    ret = None
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        ret = data.get('value', None)
    except:
        app_log.error('get explain error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def machine(name=None, category_id=None):
    """
    获取机型列表
    :return:
    """
    if name and category_id:
        sql = 'select model_id, name from machine_type where visibility=%s and name like %s and category_id = %s'
        param = tuple([1, '%' + name.replace('%', '') + '%', category_id])
    elif name:
        sql = 'select model_id, name from machine_type where visibility=%s and name like %s'
        param = tuple([1, '%' + name.replace('%', '') + '%'])
    elif category_id:
        sql = 'select model_id, name from machine_type where visibility=%s and category_id = %s'
        param = tuple([1, category_id])
    else:
        sql = 'select model_id, name from machine_type where visibility=%s'
        param = tuple([1])
    data = None
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchall()
    except:
        app_log.error('get machine error', exc_info=True)
    raise gen.Return(data)


@gen.coroutine
def machine_accessory(model_id):
    """
    获取机型配件价格列表
    :param model_id: 机型id
    :return:
    """
    sql = 'select title, price from accessory_price_offer where model_id=%s'
    param = tuple([model_id])
    data = None
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchall()
    except:
        app_log.error('get machine accessory error', exc_info=True)
    raise gen.Return(data)


@gen.coroutine
def exp_route(sn):
    """
    获取快递信息
    :param sn: 快递内部单号
    :return:
    """
    key = ['opcode', 'accept_time', 'accept_address', 'remark']
    fields = ','.join(key)
    sql = 'select ' + fields + ' from express_route where readboy_sn=%s or exp_sn=%s order by accept_time desc'
    param = tuple([sn, sn])
    data = None
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchall()
        if data:
            for i in data:
                i = _changeTS(i, ['accept_time'])
    except:
        app_log.error('get machine accessory error', exc_info=True)
    raise gen.Return(data)


@gen.coroutine
def store_exp_route(data):
    """
    保存快递路由信息
    :param data:
    :return:
    """
    data['created_at'] = _now_date()
    key = [
        'com',
        'readboy_sn',
        'status',
        'exp_sn',
        'opcode',
        'accept_time',
        'accept_address',
        'remark',
        'created_at',
    ]
    key_copy = copy.copy(key)
    for i in key_copy:
        if not data.get(i, None):
            key.remove(i)
    fields = '=%s,'.join(key) + '=%s'
    sql = 'replace into express_route set ' + fields
    param = tuple([data.get(i) for i in key])
    ret = False
    try:
        cur = yield DB_PR.execute(sql, param)
        ret = True
    except:
        app_log.error('store express route error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_model(modelId):
    """
    根据机器id获取机器
    :param model: 机器id
    :return:
    """
    ret = None
    if not modelId:
        raise gen.Return(ret)
    # sql = 'select model_id from machine_type where name=%s'
    sql = 'select model_id, prompt_enable, prompt_info from machine_type where model_id=%s and visibility=1'
    param = tuple([modelId])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data and data.get('model_id'):
            ret = data
    except:
        app_log.error('check model_id error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_model_id(model):
    """
    根据机器名称获取机器id
    :param model: 机器名称
    :return:
    """
    ret = None
    if not model:
        raise gen.Return(ret)
    # sql = 'select model_id from machine_type where name=%s'
    sql = 'select model_id, prompt_enable, prompt_info from machine_type where name=%s and visibility=1'
    param = tuple([model])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data and data.get('model_id'):
            ret = data
    except:
        app_log.error('check model_id error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_category_id(model_id):
    """
    根据机器id获取机器品类id
    :param model_id: 机器id
    :return:
    """
    ret = 0
    if not model_id:
        raise gen.Return(ret)
    # sql = 'select model_id from machine_type where name=%s'
    sql = 'select category_id from machine_type where model_id=%s and visibility=1'
    param = tuple([model_id])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data and data.get('category_id'):
            ret = data['category_id']
    except:
        app_log.error('check category_id error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_barcode_order(barcode, uid=None):
    """
    查询条码有没有正在寄修中的订单
    :param barcode: 条码
    :param uid: 用户id
    :return:
    """
    ret = None
    if uid:
        sql = 'select model_name, sn, created_at, status from `order` where (status BETWEEN 100 and 800 or status BETWEEN -800 and -300) AND barcode=%s and uid=%s'
        param = tuple([barcode, uid])
    else:
        sql = 'select model_name, sn, created_at, status from `order` where (status BETWEEN 100 and 800 or status BETWEEN -800 and -300) AND barcode=%s'
        param = tuple([barcode])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data:
            data = _changeTS(data, ['created_at'])
            ret = data
    except:
        app_log.error('check barcode order error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_imei_order(imei, uid=None):
    """
    查询imei有没有正在寄修中的订单
    :param uid: 用户id
    :return:
    """
    ret = None
    if uid:
        sql = 'select model_name, sn, barcode, created_at, status from `order` where (status BETWEEN 100 and 800 or status BETWEEN -800 and -300) AND imei=%s and uid=%s'
        param = tuple([imei, uid])
    else:
        sql = 'select model_name, sn, barcode, created_at, status from `order` where (status BETWEEN 100 and 800 or status BETWEEN -800 and -300) AND imei=%s'
        param = tuple([imei])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data:
            data = _changeTS(data, ['created_at'])
            ret = data
    except:
        app_log.error('check imei order error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def get_exp_order_sn(exp_sn):
    """
    获取某个内部快递单号的寄修流水号
    :param exp_sn: 内部快递单号
    :return:
    """
    ret = None
    sql = 'select pr_sn from pr_express where readboy_sn=%s or exp_sn=%s'
    param = tuple([exp_sn, exp_sn])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data and data.get('pr_sn'):
            ret = data['pr_sn']
    except:
        app_log.error('get exp order sn error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def wxpay(order, trade_type, openid):
    """
    微信支付
    :param order: 订单信息
    :return:
    """
    trans = yield DB_PR.begin()
    try:
        ### 寄修单中没有支付单号，需要创建一个新的支付单号
        readboy_sn = yield _unique_pay_sn(order.get('sn'))
        if order.get('rb_pay_sn'):
            # 查询支付记录
            pay_order = yield pay_order_data(order.get('rb_pay_sn'), order.get('pay_com'))
            if pay_order:
                # 查询是否已支付
                query_pay_order = yield wxpay_model.query_pay(pay_order.get('appid'), order.get('rb_pay_sn'))
                if query_pay_order.get('return_code') == 'SUCCESS' and \
                        query_pay_order.get('result_code') == 'SUCCESS' and \
                        query_pay_order.get('trade_state') == 'SUCCESS' and \
                        query_pay_order.get('total_fee') == str(long(order.get('pay_amount') * 100)):
                    # 如果已经支付
                    readboy_sn = order['rb_pay_sn']
                else:
                    # 如果没有支付   直接关闭此订单生成新的
                    yield close_pay_order(order['rb_pay_sn'])
            # # 寄修单中有支付单号，需要找对应的应用有没有该支付单号未支付的支付订单，如果有，则复用单号
            # sql = 'select count(*) as count from pay_order where readboy_sn=%s and pay_appid=%s and com=1 and abandon = 0'
            # param = tuple([order['rb_pay_sn'], config.WXPAY_APPID.get(order.get('appid'))])
            # cur = yield trans.execute(sql, param)
            # check = cur.fetchone()
            # if check.get('count') > 0:
            #     readboy_sn = order['rb_pay_sn']
            else:
                ### 没有可复用的支付单号，关闭寄修单中记录的旧支付单号
                yield close_pay_order(order['rb_pay_sn'])
        ### 写入或更新支付订单表
        save = dict()
        save['pr_sn'] = order.get('sn')
        save['com'] = 1
        save['readboy_sn'] = readboy_sn
        save['pay_amount'] = order.get('pay_amount')
        save['appid'] = order.get('appid')
        save['pay_appid'] = config.WXPAY_APPID.get(order.get('appid'))
        save['updated_at'] = _now_date()
        key = [
            'pr_sn',  # 寄修单号
            'com',
            'readboy_sn',  # 内部支付单号
            'pay_amount',
            'appid',
            'pay_appid',
            'updated_at',
        ]
        fields = ','.join(key)
        values = ','.join(['%s'] * len(key))
        updates = ','.join([i + '=%s' for i in key])
        sql = 'insert into pay_order (' + fields + ') values (' + values + ') ON DUPLICATE KEY UPDATE ' + updates
        param = tuple([save.get(i) for i in key] * 2)
        yield trans.execute(sql, param)
        # 更新寄修单中支付信息   内部支付单号
        sql = 'update `order` set rb_pay_sn=%s, pay_com=1 where sn=%s'
        param = tuple([readboy_sn, order.get('sn')])
        yield trans.execute(sql, param)
        trans.commit()
    except:
        trans.rollback()
        db_log.error('wxpay error', exc_info=True)
        raise gen.Return(None)
    # print order.get('appid')
    ret = yield wxpay_model.create_pay(order.get('appid', ''), readboy_sn, order.get('pay_amount'), trade_type,
                                       openid, 0)
    raise gen.Return(ret)


@gen.coroutine
def AR_wxpay(order, trade_type, openid):
    """
    弃修微信支付
    :param order: 订单信息
    :return:
    """
    trans = yield DB_PR.begin()
    try:
        # 寄修单中没有支付单号，需要创建一个新的支付单号
        readboy_sn = yield _unique_pay_sn(order.get('sn'))
        if order.get('ar_rb_pay_sn'):
            # 查询支付记录
            pay_order = yield pay_order_data(order.get('ar_rb_pay_sn'), order.get('ar_pay_com'))
            if pay_order:
                # 查询是否已支付
                query_pay_order = yield wxpay_model.query_pay(pay_order.get('appid'), order.get('ar_rb_pay_sn'))
                if query_pay_order.get('return_code') == 'SUCCESS' and \
                        query_pay_order.get('result_code') == 'SUCCESS' and \
                        query_pay_order.get('trade_state') == 'SUCCESS' and \
                        query_pay_order.get('total_fee') == str(long(order.get('amount_in_ar') * 100)):
                    # 如果已经支付
                    readboy_sn = order['ar_rb_pay_sn']
                else:
                    # 如果没有支付   直接关闭此订单生成新的
                    yield close_pay_order(order['ar_rb_pay_sn'])
            else:
                # 没有可复用的支付单号，关闭寄修单中记录的旧支付单号
                yield close_pay_order(order['ar_rb_pay_sn'])
        # 写入或更新支付订单表
        save = dict()
        save['pr_sn'] = order.get('sn')
        save['com'] = 1
        save['readboy_sn'] = readboy_sn
        save['pay_amount'] = order.get('amount_in_ar')
        save['appid'] = order.get('appid')
        save['pay_appid'] = config.WXPAY_APPID.get(order.get('appid'))
        save['updated_at'] = _now_date()
        save['type'] = 1
        key = [
            'pr_sn',  # 寄修单号
            'com',
            'readboy_sn',  # 内部支付单号
            'pay_amount',
            'appid',
            'pay_appid',
            'type',
            'updated_at',
        ]
        fields = ','.join(key)
        values = ','.join(['%s'] * len(key))
        updates = ','.join([i + '=%s' for i in key])
        sql = 'insert into pay_order (' + fields + ') values (' + values + ') ON DUPLICATE KEY UPDATE ' + updates
        param = tuple([save.get(i) for i in key] * 2)
        yield trans.execute(sql, param)
        # 更新寄修单中支付信息   内部支付单号
        sql = 'update order_extend set ar_rb_pay_sn=%s, ar_pay_com=1 where sn=%s'
        param = tuple([readboy_sn, order.get('sn')])
        yield trans.execute(sql, param)
        trans.commit()
    except:
        trans.rollback()
        db_log.error('wxpay error', exc_info=True)
        raise gen.Return(None)
    # print order.get('appid')
    ret = yield wxpay_model.create_pay(order.get('appid', ''), readboy_sn, order.get('amount_in_ar'), trade_type,
                                       openid, 1)
    raise gen.Return(ret)


@gen.coroutine
def wxrefund(order, refund_fee=0):
    """
    微信退款
    :param refund_fee:
    :param order: 订单信息
    :return:
    """
    trans = yield DB_PR.begin()
    try:
        # 寄修单中没有退款单号，需要创建一个新的退款单号   readboy_sn  商户内部退款单号
        readboy_sn = yield _unique_refund_sn(order.get('sn'))
        sql = 'select count(*) as count from refund_order where readboy_sn=%s and refund_appid=%s and com=1 and is_refund=0'
        param = tuple([order['rb_refund_sn'], config.WXPAY_APPID[order['appid']]])
        cur = yield trans.execute(sql, param)
        data = cur.fetchone()
        if data.get('count') > 0:
            readboy_sn = order['rb_refund_sn']
        # 写入或更新退款订单表
        save = dict()
        save['pr_sn'] = order.get('sn')
        save['com'] = 1
        save['readboy_sn'] = readboy_sn
        save['refund_amount'] = refund_fee
        save['appid'] = order.get('appid')
        save['refund_appid'] = config.WXPAY_APPID.get(order.get('appid'))
        save['created_at'] = _now_date()
        save['updated_at'] = _now_date()
        key1 = [
            'pr_sn',
            'com',
            'readboy_sn',  # 商户内部退款单号
            'refund_amount',
            'appid',
            'refund_appid',
            'created_at',
        ]
        key2 = [
            'pr_sn',
            'com',
            'readboy_sn',  # 商户内部退款单号
            'refund_amount',
            'appid',
            'refund_appid',
            'updated_at',
        ]
        fields = ','.join(key1)
        values = ','.join(['%s'] * len(key1))
        updates = ','.join([i + '=%s' for i in key2])
        if data.get('count') > 0:
            # print updates
            sql = 'update  refund_order set ' + updates + ' where readboy_sn=%s'
            # print sql
            param = tuple([save.get(i) for i in key2] + [readboy_sn])
            yield trans.execute(sql, param)
        else:
            sql = 'insert into refund_order (' + fields + ') values (' + values + ') '
            param = tuple([save.get(i) for i in key1])
            yield trans.execute(sql, param)
        # 更新订单表
        sql = 'update `order` set rb_refund_sn=%s, refund_com=1 where sn=%s'
        param = tuple([readboy_sn, order.get('sn')])
        yield trans.execute(sql, param)
        trans.commit()
    except:
        trans.rollback()
        db_log.error('wxpay error', exc_info=True)
        raise gen.Return(None)
    # 调用退款接口
    ret = yield wxpay_model.create_refund(order.get('appid', ''), order['rb_pay_sn'], readboy_sn,
                                          order.get('pay_amount'), refund_fee)
    if ret:
        yield DB_PR.execute('update refund_order set refund_sn = %s where readboy_sn = %s', (ret, readboy_sn))
        raise gen.Return(readboy_sn)
    raise gen.Return(ret)


@gen.coroutine
def alirefund(order, refund_fee=0):
    """
    支付宝退款
    :param refund_fee:
    :param order: 订单信息
    :return:
    """
    trans = yield DB_PR.begin()
    try:
        # 寄修单中没有退款单号，需要创建一个新的退款单号   readboy_sn  商户内部退款单号
        readboy_sn = yield _unique_refund_sn(order.get('sn'))
        sql = 'select count(*) as count from refund_order where readboy_sn=%s and refund_appid=%s and com=2 and is_refund=0'
        param = tuple([order['rb_refund_sn'], config.WXPAY_APPID[order['appid']]])
        cur = yield trans.execute(sql, param)
        data = cur.fetchone()
        if data.get('count') > 0:
            readboy_sn = order['rb_refund_sn']
        # 写入或更新退款订单表
        save = dict()
        save['pr_sn'] = order.get('sn')
        save['com'] = 2
        save['readboy_sn'] = readboy_sn
        save['refund_amount'] = refund_fee
        save['appid'] = order.get('appid')
        save['refund_appid'] = config.WXPAY_APPID.get(order.get('appid'))
        save['created_at'] = _now_date()
        save['updated_at'] = _now_date()
        key1 = [
            'pr_sn',
            'com',
            'readboy_sn',  # 商户内部退款单号
            'refund_amount',
            'appid',
            'refund_appid',
            'created_at',
        ]
        key2 = [
            'pr_sn',
            'com',
            'readboy_sn',  # 商户内部退款单号
            'refund_amount',
            'appid',
            'refund_appid',
            'updated_at',
        ]
        fields = ','.join(key1)
        values = ','.join(['%s'] * len(key1))
        updates = ','.join([i + '=%s' for i in key2])
        if data.get('count') > 0:
            # print updates
            sql = 'update  refund_order set ' + updates + ' where readboy_sn=%s'
            # print sql
            param = tuple([save.get(i) for i in key2] + [readboy_sn])
            yield trans.execute(sql, param)
        else:
            sql = 'insert into refund_order (' + fields + ') values (' + values + ') '
            param = tuple([save.get(i) for i in key1])
            yield trans.execute(sql, param)
        # 更新订单表
        sql = 'update `order` set rb_refund_sn=%s, refund_com=2 where sn=%s'
        param = tuple([readboy_sn, order.get('sn')])
        yield trans.execute(sql, param)
        trans.commit()
    except:
        trans.rollback()
        db_log.error('alipay error', exc_info=True)
        raise gen.Return(None)
    # 调用退款接口
    ret = alipay_model.create_refund(order.get('appid', ''), order['rb_pay_sn'], refund_fee, readboy_sn)
    if ret:
        change_data = {
            'pr_sn': order.get('sn'),
            'refund_com': order.get('com'),
            'rb_refund_sn': readboy_sn,
            'is_refund': 1,
            'refund_state': 'SUCCESS',
            'refund_sn': ret,  # 退款单号
            'pay_amount': order.get('pay_amount'),
            'refund_fee': refund_fee,
        }
        yield order_refund_sure(change_data)
        # yield DB_PR.execute('update `order` set ')
        # yield DB_PR.execute('update refund_order set refund_sn = %s, is_refund = 1, refund_state = %s '
        #                     'where readboy_sn = %s', (ret, 'SUCCESS', readboy_sn))
        raise gen.Return(readboy_sn)
    raise gen.Return(ret)


@gen.coroutine
def alipay(order):
    """
    支付宝支付
    :param order: 订单信息
    :return:
    """
    trans = yield DB_PR.begin()
    try:
        ### 寄修单中没有支付单号，需要创建一个新的支付单号
        readboy_sn = yield _unique_pay_sn(order.get('sn'))
        if order.get('rb_pay_sn'):
            ### 寄修单中有支付单号，需要找对应的应用有没有支付宝的未支付的支付订单，如果有，则复用单号
            sql = 'select readboy_sn from pay_order where pr_sn=%s and pay_appid=%s and com=2'
            param = tuple([order['sn'], config.ALIPAY_APPID.get(order.get('appid'))])
            cur = yield trans.execute(sql, param)
            check = cur.fetchone()
            if check and check.get('readboy_sn'):
                readboy_sn = check['readboy_sn']
            else:
                ### 没有找到复用支付订单号，关闭寄修单中旧的支付单号
                yield close_pay_order(order['rb_pay_sn'])
        ### 写入或更新支付订单表
        save = dict()
        save['pr_sn'] = order.get('sn')
        save['com'] = 2
        save['readboy_sn'] = readboy_sn
        save['pay_amount'] = order.get('pay_amount')
        save['appid'] = order.get('appid')
        save['pay_appid'] = config.ALIPAY_APPID.get(order.get('appid'))
        save['updated_at'] = _now_date()
        key = [
            'pr_sn',
            'com',
            'readboy_sn',
            'pay_amount',
            'appid',
            'pay_appid',
            'updated_at',
        ]
        fields = ','.join(key)
        values = ','.join(['%s'] * len(key))
        updates = ','.join([i + '=%s' for i in key])
        sql = 'insert into pay_order (' + fields + ') values (' + values + ') ON DUPLICATE KEY UPDATE ' + updates
        param = tuple([save.get(i) for i in key] * 2)
        yield trans.execute(sql, param)
        ### 更新寄修单中支付信息
        sql = 'update `order` set rb_pay_sn=%s, pay_com=2 where sn=%s'
        param = tuple([readboy_sn, order.get('sn')])
        yield trans.execute(sql, param)
        trans.commit()
    except:
        trans.rollback()
        db_log.error('alipay error', exc_info=True)
        raise gen.Return(None)
    ret = alipay_model.create_pay(order.get('appid', ''), readboy_sn, order.get('pay_amount'), 0)
    raise gen.Return(ret)


@gen.coroutine
def AR_alipay(order):
    """
    支付宝支付
    :param order: 订单信息
    :return:
    """
    trans = yield DB_PR.begin()
    try:
        # 寄修单中没有支付单号，需要创建一个新的支付单号
        readboy_sn = yield _unique_pay_sn(order.get('sn'))
        if order.get('ar_rb_pay_sn'):
            # 寄修单中有支付单号，需要找对应的应用有没有支付宝的未支付的支付订单，如果有，则复用单号
            sql = 'select readboy_sn from pay_order where pr_sn=%s and pay_appid=%s and com=2 and type = 1'
            param = tuple([order['sn'], config.ALIPAY_APPID.get(order.get('appid'))])
            cur = yield trans.execute(sql, param)
            check = cur.fetchone()
            if check and check.get('readboy_sn'):
                readboy_sn = check['readboy_sn']
            else:
                # 没有找到复用支付订单号，关闭寄修单中旧的支付单号
                yield close_pay_order(order['ar_rb_pay_sn'])
        ### 写入或更新支付订单表
        save = dict()
        save['pr_sn'] = order.get('sn')
        save['com'] = 2
        save['readboy_sn'] = readboy_sn
        save['pay_amount'] = order.get('amount_in_ar')
        save['appid'] = order.get('appid')
        save['pay_appid'] = config.ALIPAY_APPID.get(order.get('appid'))
        save['updated_at'] = _now_date()
        save['type'] = 1
        key = [
            'pr_sn',
            'com',
            'readboy_sn',
            'pay_amount',
            'appid',
            'pay_appid',
            'updated_at',
            'type'
        ]
        fields = ','.join(key)
        values = ','.join(['%s'] * len(key))
        updates = ','.join([i + '=%s' for i in key])
        sql = 'insert into pay_order (' + fields + ') values (' + values + ') ON DUPLICATE KEY UPDATE ' + updates
        param = tuple([save.get(i) for i in key] * 2)
        yield trans.execute(sql, param)
        ### 更新寄修单中支付信息
        sql = 'update order_extend set ar_rb_pay_sn=%s, ar_pay_com=2 where sn=%s'
        param = tuple([readboy_sn, order.get('sn')])
        yield trans.execute(sql, param)
        trans.commit()
    except:
        trans.rollback()
        db_log.error('alipay error', exc_info=True)
        raise gen.Return(None)
    ret = alipay_model.create_pay(order.get('appid', ''), readboy_sn, order.get('amount_in_ar'), 1)
    raise gen.Return(ret)


@gen.coroutine
def close_pay_order(readboy_sn):
    """
    关闭寄修单中支付单号
    :param readboy_sn: 内部支付单号
    :return:
    """
    sql = 'select com, appid from pay_order where readboy_sn=%s and abandon=0'
    param = tuple([readboy_sn])
    cur = yield DB_PR.execute(sql, param)
    pay_order = cur.fetchone()
    ret = False
    if pay_order and pay_order.get('com') and pay_order.get('appid'):
        if pay_order['com'] == 1:
            ### 微信关闭订单
            close_result = yield wxpay_model.close_pay(pay_order['appid'], readboy_sn)
            if close_result.get('result_code') == 'SUCCESS':
                wxpay_log.info('close pay order success: readboy_sn=%s' % readboy_sn)
                sql = 'update pay_order set abandon=1 where readboy_sn=%s and appid=%s and com=1'
                param = tuple([readboy_sn, pay_order['appid']])
                yield DB_PR.execute(sql, param)
                ret = True
            else:
                wxpay_log.info('close pay order error: readboy_sn=%s, err_code=%s, err_code_des=%s' % (
                readboy_sn, close_result.get('err_code'), close_result.get('err_code_des')))
        elif pay_order['com'] == 2:
            pass
    raise gen.Return(ret)


@gen.coroutine
def pay_order_data(readboy_sn, com):
    """
    获取支付订单信息
    :param readboy_sn: 读书郎支付订单号
    :param com: 支付公司
    :return:
    """
    ret = None
    sql = 'select * from pay_order where readboy_sn=%s and com=%s'
    param = tuple([readboy_sn, com])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data:
            ret = _changeTS(data, ['created_at', 'updated_at'])
    except:
        db_log.error('pay_order_data error:readboy=%s, com=%s' % (readboy_sn, com))
    raise gen.Return(ret)


@gen.coroutine
def refund_order_data(readboy_sn, com):
    """
    获取退款订单信息
    :param readboy_sn: 读书郎支付订单号
    :param com: 支付公司
    :return:
    """
    ret = None
    sql = 'select * from refund_order where readboy_sn=%s and com=%s'
    param = tuple([readboy_sn, com])
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data:
            ret = _changeTS(data, ['created_at', 'updated_at'])
    except:
        db_log.error('refund_order_data error:readboy=%s, com=%s' % (readboy_sn, com))
    raise gen.Return(ret)


@gen.coroutine
def order_data(sn, need_fields):
    """
    订单详情
    :param sn: 订单流水号
    :param need_fields: 需要返回的字段
    :return:
    """
    key = need_fields
    fields = ','.join(key)
    param = tuple([sn])
    sql = 'select ' + fields + ' from `order` where sn=%s'
    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data:
            data = _changeTS(data, ['created_at', 'updated_at', 'updated_at_last'])
            data = _image_to_json(data, ['period_file', 'upload_file'])
        # if data and data.get('repair_endpoint'):
        #     data['endpoint'] = yield endpoint(data['repair_endpoint'])
    except:
        app_log.error('order_data error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(data)


@gen.coroutine
def get_order_sn_by_keyword(keyword):
    """
    根据关键字查询订单号
    :param keyword: 关键字，可能是6位或11位
    :return: 订单号列表
    """
    ret = []
    if not keyword:
        raise gen.Return(ret)

    try:
        if len(keyword) == 6:
            # 6位关键字，查询条码后6位
            sql = 'select sn from `order` where RIGHT(barcode, 6) = %s order by created_at desc'
            param = tuple([keyword])
        elif len(keyword) == 11:
            # 11位关键字，查询手机号
            sql = 'select sn from `order` where phone = %s order by created_at desc'
            param = tuple([keyword])
        else:
            # 其他长度，直接返回空
            raise gen.Return(ret)

        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchall()
        if data:
            ret = [item['sn'] for item in data]
    except:
        app_log.error('get_order_sn_by_keyword error', exc_info=True)

    raise gen.Return(ret)


@gen.coroutine
def get_latest_order_log(sn):
    """
    获取订单最新日志记录
    :param sn: 订单流水号
    :return: 最新日志记录
    """
    ret = None
    if not sn:
        raise gen.Return(ret)

    try:
        sql_log = 'select * from order_log where pr_sn=%s order by date DESC, id DESC'
        param_log = tuple([sn])
        cur = yield DB_PR.execute(sql_log, param_log)
        data = cur.fetchall()

        if not data:
            raise gen.Return(ret)

        had_inform = any([x.get('pr_status') in [410, 480, 490, 500, 600, 700, -700, 800] for x in data])

        # 处理每条日志记录，找到第一条符合条件的作为最新记录
        for di in data:
            if not di.get('pr_status'):
                continue
            if di.get('pr_status') == 400:
                if had_inform:
                    di['title'] = '维修终端已收货，客服已知会'
                sign_in_info = yield order_sign_in_info(sn)
                for k, v in sign_in_info.items():
                    di[k] = v
                di = _image_to_json(di, ['sign_in_pictures', 'sign_in_videos'])
            di = _changeTS(di, ['date'])
            if di.get('pr_status') not in [410, 480, 490]:
                ret = di  # 返回第一条符合条件的记录（即最新的）
                break

    except:
        app_log.error('get_latest_order_log error', exc_info=True)

    raise gen.Return(ret)


@gen.coroutine
def order_pay_sure(data):
    """
    订单确认支付
    :param data: 订单数据
    :return:
    """
    key = [
        'rb_pay_sn',
        'pay_com',
        'pay_sn',
        'is_paid',
        'status',
        'pay_time'
    ]
    data['pay_time'] = _now_date()
    if any(data.get(i) is None for i in key):
        raise gen.Return(None)
    trans = yield DB_PR.begin()
    try:
        update = ','.join([i + '=%s' for i in key])
        sql = 'update `order` set ' + update + ' where sn=%s'
        param = tuple([data.get(i) for i in key] + [data.get('pr_sn')])
        cur = yield trans.execute(sql, param)
        yield trans.execute('update pr_used_material set statistic_time = %s where pr_sn = %s and mark = 0',
                            [data['pay_time'], data['pr_sn']])
        sql = 'update pay_order set is_paid=%s, trade_state=%s, pay_sn=%s where readboy_sn=%s and com=%s'
        param = tuple([1, 'SUCCESS', data.get('pay_sn'), data.get('rb_pay_sn'), data.get('pay_com')])
        cur = yield trans.execute(sql, param)
        log = {
            'pr_sn': data.get('pr_sn', ''),
            'pr_status': data.get('status', 0),
            'log_status': data.get('status', 0),
            'log_from': data.get('log_from', 'app'),
            'relation_key': data.get('relation_key', ''),
            'operation': data.get('operation', 'pay_order'),
            'uid': data.get('uid', 0),
            'admin': data.get('admin', 0),
            'title': data.get('title', '维修费用支付成功，正在维修中'),
            'remark': data.get('remark', ''),
            'date': data.get('date', _now_date()),
        }
        add_log(log)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        db_log.error('order_pay_sure error:data=' % data, exc_info=True)
        ret = False
    raise gen.Return(ret)


@gen.coroutine
def ar_order_pay_sure(data):
    """
    订单确认支付
    :param data: 订单数据
    :return:
    """
    key = [
        'ar_rb_pay_sn',
        'ar_pay_com',
        'ar_pay_sn',
        'ar_is_paid',
        'ar_pay_time',
        'ar_time'
    ]
    now = _now_date()
    data['ar_pay_time'] = now
    data['ar_time'] = now
    if any(data.get(i) is None for i in key):
        raise gen.Return(None)
    trans = yield DB_PR.begin()
    try:
        update = ','.join([i + '=%s' for i in key])
        sql = 'update order_extend set ' + update + ' where sn=%s'
        param = tuple([data.get(i) for i in key] + [data.get('pr_sn')])
        cur = yield trans.execute(sql, param)
        # cur = yield trans.execute('update `order` set connect = %s, accessory_amount = %s, accessory_cast = %s, '
        #                           'amount = %s, pay_amount = %s, status = %s, updated_at = %s where sn = %s',
        #                           [data.get('connect'), data.get('accessory_amount'), data.get('accessory_cast'),
        #                            data.get('amount'), data.get('pay_amount'), data.get('status'), now, data.get('pr_sn')])
        cur = yield trans.execute('update `order` set connect = %s, pay_amount = %s, status = %s, updated_at = %s where sn = %s',
                                  [data.get('connect'), data.get('pay_amount'), data.get('status'), now, data.get('pr_sn')])
        # 弃修支付后需要把物料清除
        # yield trans.execute('delete from pr_used_material where pr_sn = %s', [data['pr_sn']])
        # yield trans.execute('delete from pr_material where pr_sn = %s', [data['pr_sn']])
        sql = 'update pay_order set is_paid=%s, trade_state=%s, pay_sn=%s where readboy_sn=%s and com=%s'
        param = tuple([1, 'SUCCESS', data.get('ar_pay_sn'), data.get('ar_rb_pay_sn'), data.get('ar_pay_com')])
        cur = yield trans.execute(sql, param)
        log = {
            'pr_sn': data.get('pr_sn', ''),
            'pr_status': data.get('status', 0),
            'log_status': data.get('status', 0),
            'log_from': data.get('log_from', 'h5'),
            'relation_key': data.get('relation_key', ''),
            'operation': data.get('operation', 'pay_order'),
            'uid': data.get('uid', 0),
            'admin': data.get('admin', 0),
            'title': data.get('title', '弃修费用支付成功，正在回寄机器中'),
            'remark': data.get('remark', ''),
            'date': data.get('date', now),
        }
        add_log(log)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        db_log.error('order_ar_pay_sure error:data=' % data, exc_info=True)
        ret = False
    raise gen.Return(ret)


@gen.coroutine
def order_refund_sure(data):
    """
    订单确认退款
    :param data: 订单数据
    :return:
    """
    key = [
        'rb_refund_sn',  # readboy_sn  内部退款单号
        'refund_com',  # 微信退款--1
        'refund_sn',  # 退款单号
        'is_refund',  # 是否已退款
        'pay_amount',  # 总金额
        'refund_fee',  # 退款金额
        'refund_state',  # 退款状态
    ]
    if any(data.get(i) is None for i in key):
        raise gen.Return(None)
    trans = yield DB_PR.begin()
    try:
        param = tuple([1, 'SUCCESS', data.get('rb_refund_sn'), data.get('refund_com')])
        yield trans.execute(
            'update refund_order set is_refund = %s, refund_state=%s where readboy_sn = %s and com = %s', param)
        cur = yield DB_PR.execute('select refund_amount from `order` where sn = %s ', data.get('pr_sn'))
        data_amount = cur.fetchone()
        sql = 'update `order` set refund_amount = refund_amount + %s, is_refund = %s, refund_com=%s,rb_refund_sn=%s where sn = %s'
        if data_amount and data_amount.get('refund_amount') == 0:
            if data.get('pay_amount') == data.get('refund_fee'):  # 全部退款
                param_order = tuple(
                    [data.get('refund_fee'), 1, data.get('refund_com'), data.get('rb_refund_sn'), data.get('pr_sn')])
            else:  # 部分退款
                param_order = tuple(
                    [data.get('refund_fee'), 2, data.get('refund_com'), data.get('rb_refund_sn'), data.get('pr_sn')])
        else:
            # 全部退款
            if (data_amount.get('refund_amount') + data.get('refund_fee')) == data.get('pay_amount'):
                param_order = tuple(
                    [data.get('refund_fee'), 1, data.get('refund_com'), data.get('rb_refund_sn'), data.get('pr_sn')])
            else:  # 部分退款
                param_order = tuple(
                    [data.get('refund_fee'), 2, data.get('refund_com'), data.get('rb_refund_sn'), data.get('pr_sn')])
        yield trans.execute(sql, param_order)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        db_log.error('order_refund_sure error:data=' % data, exc_info=True)
        ret = False
    raise gen.Return(ret)


@gen.coroutine
def save_pay_log(data):
    """
    保存支付推送记录
    :param data:
    :return:
    """
    data['created_at'] = _now_date()
    key = [
        'readboy_sn',
        'pay_com',
        'pay_sn',
        'detail',
        'created_at',
    ]
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    sql = 'insert into pay_notify_log (' + fields + ') values (' + values + ')'
    param = tuple([data.get(i) for i in key])
    ret = False
    try:
        cur = yield DB_PR.execute(sql, param)
        ret = True
    except:
        db_log.error('save_pay_log error:', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def save_refund_log(data):
    """
    保存退款推送记录
    :param data:
    :return:
    """
    data['created_at'] = _now_date()
    key = [
        'readboy_sn',
        'refund_com',
        'refund_sn',
        'detail',
        'created_at',
    ]
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    sql = 'insert into refund_notify_log (' + fields + ') values (' + values + ')'
    param = tuple([data.get(i) for i in key])
    ret = False
    try:
        cur = yield DB_PR.execute(sql, param)
        ret = True
    except:
        db_log.error('save_refund_log error:', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def order_pay_check(sn):
    """
    检查订单是否已经支付成功
    :param sn: 寄修订单号
    :return:
    """
    ### 查询寄修单数据库信息
    order = yield order_data(sn, ['status', 'rb_pay_sn', 'pay_com', 'pay_amount'])
    if order and order.get('status') == config.ORDER_CHECK and order.get('rb_pay_sn') and order.get('pay_com'):
        pay_order = yield pay_order_data(order.get('rb_pay_sn'), order.get('pay_com'))
        if pay_order and pay_order.get('appid'):
            ### 核对订单信息
            if order['pay_com'] == 1:
                query_pay_order = yield wxpay_model.query_pay(pay_order.get('appid'), order.get('rb_pay_sn'))
                if query_pay_order.get('return_code') == 'SUCCESS' and query_pay_order.get(
                        'result_code') == 'SUCCESS' and query_pay_order.get(
                    'trade_state') == 'SUCCESS' and query_pay_order.get('total_fee') == str(
                    long(order.get('pay_amount') * 100)):
                    ### 修改寄修单和支付订单状态
                    change_data = {
                        'pr_sn': sn,
                        'pay_com': order.get('pay_com'),
                        'rb_pay_sn': order.get('rb_pay_sn'),
                        'is_paid': 1,
                        'status': config.ORDER_PAY,
                        'pay_sn': query_pay_order.get('transaction_id')
                    }
                    wxpay_log.info('change_data:', json.dumps(change_data))
                    yield order_pay_sure(change_data)
            elif order['pay_com'] == 2:
                query_pay_order = yield alipay_model.query_pay(pay_order.get('appid'), order.get('rb_pay_sn'))
                if query_pay_order and query_pay_order.get('code') == '10000' and query_pay_order.get(
                        'msg') == 'Success' and query_pay_order.get('trade_status') in ['TRADE_SUCCESS',
                                                                                        'TRADE_FINISHED'] and query_pay_order.get(
                    'total_amount') == str(order.get('pay_amount')):
                    ### 修改寄修单和支付订单状态
                    change_data = {
                        'pr_sn': sn,
                        'pay_com': order.get('pay_com'),
                        'rb_pay_sn': order.get('rb_pay_sn'),
                        'is_paid': 1,
                        'status': config.ORDER_PAY,
                        'pay_sn': query_pay_order.get('trade_no')
                    }
                    alipay_log.info('change_data:', json.dumps(change_data))
                    yield order_pay_sure(change_data)
            else:
                pass
    raise gen.Return()


@gen.coroutine
def get_machine_category():
    """
    获取所有可见的机器品类
    :return:
    """
    sql = 'select id, name, image from machine_category where visible=1'
    cur = yield DB_PR.execute(sql)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def self_test_damage(machine_category_id):
    """
    获取机型品类下的故障
    :param machine_category_id:
    :return:
    """
    # sql = 'select id, title from damage where self_test=1 and machine_category_id=%s'
    sql = 'select id, title from faq_category where visibility=1 and machine_category_id=%s'
    param = tuple([machine_category_id])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def get_self_test(machine_category_id, damage_id, page, count):
    """
    根据品类和故障id获取自检问题
    :param machine_category_id: 品类id
    :param damage_id: 故障id
    :param page: 第几页
    :param count: 每页条数
    :return:
    """
    if count > 100:
        count = 100
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    if machine_category_id and damage_id:
        sql = 'select id, title from faq_question where machine_category_id=%s and faq_category=%s and enable=1 order by top DESC, id  limit %s, %s'
        param = tuple([machine_category_id, damage_id, offset, count + 1])
    elif machine_category_id:
        sql = 'select id, title from faq_question where machine_category_id=%s and enable=1 order by top DESC, id  limit %s, %s'
        param = tuple([machine_category_id, offset, count + 1])
    elif damage_id:
        sql = 'select id, title from faq_question where faq_category=%s and enable=1 order by top DESC, id  limit %s, %s'
        param = tuple([damage_id, offset, count + 1])
    else:
        raise gen.Return(None)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    l = len(data)
    isEnd = l < count + 1
    size = l if isEnd else count
    data = data[0:size]
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)


@gen.coroutine
def get_self_test_content(st_id):
    """
    获取自检问题详情
    :param st_id: 自检问题id
    :return:
    """
    sql = 'select id, title, content from faq_question where id=%s'
    param = tuple([st_id])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def check_self_test_appraise(st_id, uid):
    """
    获取自检问题评论详情
    :param st_id: 自检问题id
    :param uid: 用户id
    :return:
    """
    # sql = 'select count(*) as count from self_test_appraise where st_id=%s and uid=%s'
    sql = 'select count(*) as count from faq_appraise where question_id=%s and uid=%s'
    param = tuple([st_id, uid])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    raise gen.Return(data['count'])


@gen.coroutine
def self_test_appraise(uid, st_id, helpful, appraise):
    """
    用户给自检问题评论
    :param uid: 用户id
    :param st_id: 问题id
    :param helpful: 是否有用
    :param appraise: 评论文字
    :return:
    """
    question_id = st_id
    key = [
        'uid',
        'question_id',
        'helpful',
        'appraise',
        'created_at',
    ]
    update = '=%s,'.join(key) + '=%s'
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    sql = 'insert into faq_appraise (' + fields + ') values (' + values + ') ON DUPLICATE KEY UPDATE ' + update
    param = tuple([uid, question_id, helpful, appraise, _now_date()] * 2)
    ret = False
    try:
        cur = yield DB_PR.execute(sql, param)
        ret = True
    except:
        db_log.info('faq_appraise error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def ban_repair():
    """
    判断当前时间范围是否允许寄修
    :return:
    """
    cur = yield DB_PR.execute('select start, end, status, info from ban_repair')
    data = cur.fetchone()
    ret = None
    if data:
        if data.get('status') == 0:
            raise gen.Return(ret)
        start = data.get('start')
        end = data.get('end')
        now = datetime.datetime.now()
        if start < now < end:
            ret = data.get('info')
    raise gen.Return(ret)


@gen.coroutine
def cancel_repair(uid, sn):
    sql = 'UPDATE `order` SET `status` = -900 where sn = %s '
    sql_log = 'replace INTO order_log (pr_sn, pr_status, log_status, log_from, operation, uid, admin, title, date) ' \
              'VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)'
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    params = tuple([sn, -900, -900, 'app', 'cancel order', uid, 0, '寄修服务已取消', now])
    trans = yield DB_PR.begin()
    ret = False
    try:
        yield trans.execute(sql, sn)
        yield trans.execute(sql_log, params)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        app_log.error('cancel repair error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def change_address(sn, data):
    sql = 'SELECT `name`, phone, province, city, district, address FROM `order` WHERE sn = %s'
    cur = yield DB_PR.execute(sql, sn)
    addr_data = cur.fetchone()
    if not addr_data:
        raise gen.Return(None)
    created_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    sql_insert = 'insert into order_old_address (sn, name, phone, province, city, district, address, created_at) ' \
                 'values(%s,%s,%s,%s,%s,%s,%s,%s)'
    param_insert = tuple([sn, addr_data['name'], addr_data['phone'], addr_data['province'], addr_data['city'],
                          addr_data['district'], addr_data['address'], created_at])
    data['updated_at'] = _now_date()
    key = ['name', 'phone', 'province', 'city', 'district', 'address', 'updated_at']
    for i in key:
        if not data.get(i, None):
            raise gen.Return(False)
    fields = '=%s,'.join(key) + '=%s'
    where_key = ['sn']
    where_fields = '=%s'.join(where_key) + '=%s'
    sql = 'update `order` set ' + fields + ' where ' + where_fields
    param = tuple([data.get(i) for i in key] + [sn])
    ret = False
    trans = yield DB_PR.begin()
    try:
        yield trans.execute(sql_insert, param_insert)
        yield trans.execute(sql, param)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        app_log.error('update contact error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_order_v1(uid, sn):
    """
    检查用户是否有权限操作订单
    :param uid: 用户id
    :param sn: 订单流水号
    :return:
    """
    ret1 = 0
    ret2 = 0
    sql = 'select uid, status,come_exp_type from `order` where sn=%s'
    try:
        cur = yield DB_PR.execute(sql, sn)
        data = cur.fetchone()
        if data.get('status'):
            ret1 = data['status']
            ret2 = data.get('come_exp_type')
            if data.get('uid') != 0 and data.get('uid') != uid:
                ret1 = -1
                ret2 = -1
    except:
        ret1 = 0
        ret2 = 0
    raise gen.Return([ret1, ret2])


@gen.coroutine
def get_machine_category_name(id):
    """
    获取所有可见的机器品类
    :return:
    """
    sql = 'select name from machine_category where visible=1 and id = %s'
    cur = yield DB_PR.execute(sql, id)
    data = cur.fetchone()
    if data:
        raise gen.Return(data['name'])
    raise gen.Return(None)


@gen.coroutine
def wx_ticket():
    timestamp = int(time.time())
    token = 'df6b88a899'
    url = 'http://wxadmin.readboy.com/weiphp/index.php?s=/home/<USER>/get_classone_jssdk&tokenval=gh_223743faa015'
    sn = str(timestamp) + md5(str(timestamp) + token).hexdigest() + token
    url = url + '&sn=' + sn
    # print url
    request = httpclient.HTTPRequest('%s' % url, method='GET', connect_timeout=HTTP_TIMEOUT,
                                     request_timeout=HTTP_TIMEOUT, validate_cert=False)
    client = httpclient.AsyncHTTPClient()
    ret = None
    try:
        response = yield client.fetch(request)
        resp = tornado.escape.json_decode(response.body)

        if resp:
            # print resp
            ret = resp
    except:
        app_log.error('request error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def wx_scan(url, jsapi_ticket):
    noncestr = createNoncestr()
    timestamp = int(time.time())
    token = 'df6b88a899'
    param = {
        'noncestr': noncestr,
        'timestamp': timestamp,
        'jsapi_ticket': jsapi_ticket,
        'url': url,
    }
    sign = getSign(param)
    data = {
        'sign': sign,
        'noncestr': noncestr,
        'timestamp': timestamp,
    }
    raise gen.Return(data)


def createNoncestr(length=32):
    """产生随机字符串，不长于32位"""
    chars = "abcdefghijklmnopqrstuvwxyz0123456789"
    strs = []
    for x in range(length):
        strs.append(chars[random.randrange(0, len(chars))])
    return "".join(strs)


def getSign(obj):
    """生成签名"""
    # 签名步骤一：按字典序排序参数,formatBizQueryParaMap已做
    String = formatBizQueryParaMap(obj, False)
    # 签名步骤三：sha加密
    String = hashlib.sha1(String).hexdigest()
    return String


def formatBizQueryParaMap(paraMap, urlencode):
    """格式化参数，签名过程需要使用"""
    slist = sorted(paraMap)
    buff = []
    for k in slist:
        v = quote(paraMap[k]) if urlencode else paraMap[k]
        buff.append("{0}={1}".format(k, v))
    return "&".join(buff)


@gen.coroutine
def get_broken_screen_insurance(barcode):
    """
    获取碎屏保信息
    :param barcode: 条码
    :return: dict or none
    """
    sql = 'SELECT bsi.`status`, bsi.created_at, bsis.month FROM broken_screen_insurance bsi left join ' \
          'broken_screen_insurance_standard bsis on bsi.standard = bsis.id WHERE bsi.barcode = %s ' \
          'AND bsi.`status` BETWEEN 300 AND 500'
    cur = yield DB_PR.execute(sql, barcode)
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def in_si_period(screen_insurance):
    """
    碎屏保是否在保修期内
    :param screen_insurance:
    :return:
    """
    ret = 0
    used_screen_insurance = 0
    if screen_insurance and screen_insurance.get('created_at') and screen_insurance.get('month'):
        now = datetime.datetime.now()
        buy_date = (screen_insurance['created_at'] + relativedelta(months=screen_insurance['month'])).date()
        if now.date() < buy_date:
            ret = 1
        else:
            ret = 2
        if screen_insurance['status'] == 400:
            used_screen_insurance = 1
    raise gen.Return([ret, used_screen_insurance])


@gen.coroutine
def check_order_by_phone(uid, phone):
    sql = 'select count(*) as count from `order` where phone = %s and uid = 0'
    cur = yield DB_PR.execute(sql, phone)
    data = cur.fetchone()
    ret = None
    app_log.info(data)
    if data['count'] > 0:
        try:
            yield DB_PR.execute('update `order` set uid = %s where phone = %s and uid = 0', (uid, phone))
            ret = True
        except:
            app_log.error('update error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_bind_equip(uid, barcode):
    sql = 'select count(*) as count from bind_equipment where status = 1 and barcode = %s and uid = %s'
    cur = yield DB_PR.execute(sql, (barcode, uid))
    data = cur.fetchone()
    if data['count'] > 0:
        raise gen.Return(True)
    raise gen.Return(False)


@gen.coroutine
def bind_equip(uid, data):
    data['uid'] = uid
    data['status'] = 1
    data['created_at'] = _now_date()
    key = ['uid', 'barcode', 'number', 'model_id', 'model', 'status', 'created_at']
    field = ','.join(key)
    sql = 'insert into bind_equipment (' + field + ') values(%s,%s,%s,%s,%s,%s,%s)'
    param = tuple([data.get(i) for i in key])
    ret = None
    try:
        yield DB_PR.execute(sql, param)
        ret = True
    except:
        app_log.error('bind equip error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def unbind_equip(uid, barcode):
    sql = 'delete from bind_equipment where uid = %s and barcode = %s and status = 1'
    ret = None
    try:
        yield DB_PR.execute(sql, (uid, barcode))
        ret = True
    except:
        app_log.error('bind equip error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def bind_equip_list(uid):
    sql = 'select barcode, model, number from bind_equipment where uid = %s and status = 1 order by created_at'
    cur = yield DB_PR.execute(sql, uid)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def bind_equip_wear_list(uidStr):
    ts = str(int(time.time()))
    ua = '1/ECS_yx/aliyun/yx.readboy.com/1/' + str(uuid.uuid1())
    appSecret = '08d9543a320bbf64fe49e9acdf64a04f'
    sn = hashlib.md5(ua + appSecret + ts).hexdigest()
    host = 'http://api-wear.readboy.com'
    url = '{host}/api/account/bind-device?t={ts}&ua={ua}&sn={sn}&uid={uid}'
    url = url.format(host=host, ts=ts, ua=ua, sn=sn, uid=uidStr)
    request = httpclient.HTTPRequest('%s' % url, method='GET', connect_timeout=HTTP_TIMEOUT,
                                     request_timeout=HTTP_TIMEOUT, validate_cert=False)
    client = httpclient.AsyncHTTPClient()
    ret = None
    try:
        response = yield client.fetch(request)
        resp = tornado.escape.json_decode(response.body)
        if resp:
            ret = resp.get('data')
    except:
        app_log.error('request error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def order_abandon(uid, sn):
    """
    订单快递费不需要支付时弃修
    :param uid:
    :param sn:
    :return:
    """
    trans = yield DB_PR.begin()
    now = _now_date()
    try:

        # sql = 'update `order` set connect = %s, accessory_amount = %s, accessory_cast = %s, ' \
        #       'amount = %s, pay_amount = %s, status = %s, updated_at = %s where sn = %s'
        sql = 'update `order` set connect = %s, pay_amount = %s, status = %s, updated_at = %s where sn = %s'
        sql2 = 'update order_extend set ar_time = %s where sn = %s'
        # yield trans.execute(sql, [3, 0, 0, 0, 0, config.ORDER_PAY, now, sn])
        yield trans.execute(sql, [3, 0, config.ORDER_PAY, now, sn])
        yield trans.execute(sql2, [now, sn])
        # yield trans.execute('delete from pr_used_material where pr_sn = %s', sn)
        # yield trans.execute('delete from pr_material where pr_sn = %s', sn)
        trans.commit()
        ret = True
        log = {
            'pr_sn': sn,
            'pr_status': config.ORDER_REPAIR_ABANDON,
            'log_status': config.ORDER_REPAIR_ABANDON,
            'log_from': 'app',
            'relation_key': '',
            'operation': 'abandon_order',
            'uid': uid,
            'admin': 0,
            'title': '寄修订单弃修，等待回寄',
            'remark': '',
            'date': now,
        }
        add_log(log)
    except Exception as e:
        app_log.error('order abandon error: %s' % e, exc_info=True)
        trans.rollback()
        ret = False
    raise gen.Return(ret)


@gen.coroutine
def post_repair_estimate(damages_id, model_id):
    sql = 'select distinct d.type from damage d ' \
          'right join machine_type m on d.machine_category_id = m.category_id ' \
          'where d.type != 0 and d.id in (' + damages_id + ') and m.model_id = %s'
    cur = yield DB_PR.execute(sql, model_id)
    damages = cur.fetchall()
    if not damages:
        raise gen.Return([])
    damage_type = []
    for i in damages:
        if i['type'] == 1:
            damage_type.append(1)
        elif i['type'] == 2:
            damage_type.append(2)
    if 1 not in damage_type and 2 not in damage_type:
        raise gen.Return([])
    if 1 in damage_type and 2 in damage_type:
        sql = 'SELECT a.id, a.title, a.price FROM accessory_price_offer a ' \
              'WHERE a.model_id = %s AND (a.title = %s OR a.title = %s OR a.title = %s OR a.title = %s)'
        param = [model_id, "主板", "主板组件", "屏幕组件", "屏幕"]
    elif 1 in damage_type:
        sql = 'SELECT a.id, a.title, a.price FROM accessory_price_offer a ' \
              'WHERE a.model_id = %s AND (a.title = %s OR a.title = %s)'
        param = [model_id, "主板", "主板组件"]
    else:
        sql = 'SELECT a.id, a.title, a.price FROM accessory_price_offer a ' \
              'WHERE a.model_id = %s AND (a.title = %s OR a.title = %s)'
        param = [model_id, "屏幕组件", "屏幕"]
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)




