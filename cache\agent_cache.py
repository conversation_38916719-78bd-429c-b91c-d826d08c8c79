# encoding=utf-8
import json
import zlib

from datetime import datetime
from cache import Cache
import decimal


class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return int(o)
        super(DecimalEncoder, self).default(o)


def week_report_key(time):
    return 'week_report:%s' % time


def month_report_key(time):
    return 'month_report:%s' % time


def set_pseudo_code(pseudo_code):
    k = 'pseudo_code'
    v = pseudo_code
    if not v:
        v = 10000001
    Cache.set(k, v)
    # setWeatherTTL(area_id)


def get_pseudo_code():
    k = 'pseudo_code'
    v = Cache.get(k)
    print type(v)
    return v