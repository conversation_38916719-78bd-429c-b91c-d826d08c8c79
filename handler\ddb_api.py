# encoding=utf-8
# __author__ = 'lch'

from tornado import gen
from tornado.log import app_log

import util.com as com
from handler import ApiHandler
import cache.ddb_cache as ddb_cache
import store.ddb_model as ddb_model
import store.region_model as region_model


class HelloWorld(ApiHandler):
    def get(self):
        self.success(None, msg=u'欢迎使用读书郎点读笔服务')


class DDB_LayerSound(ApiHandler):
    @gen.coroutine
    def get(self):
        """音频目录树"""
        data = ddb_cache.get_layer_sound()
        if not data:
            data = yield ddb_model.layer_sound()
            ddb_cache.set_layer_sound(data)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class DDB_SoundList(ApiHandler):
    @gen.coroutine
    def get(self):
        key = self.get_argument('layer', '')
        page = com.safeInt(self.get_argument('page', 1))
        count = com.safeInt(self.get_argument('count', 20))
        if not key:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        data = ddb_cache.get_sound_list(key, page, count)
        if not data:
            data = yield ddb_model.sound_list(key, page, count)
            ddb_cache.set_sound_list(data, key, page, count)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class DDB_SoundSearch(ApiHandler):
    @gen.coroutine
    def get(self):
        key = self.get_argument('key', '')
        key = key.replace('%', '').replace('\\', '')
        page = com.safeInt(self.get_argument('page', 1))
        count = com.safeInt(self.get_argument('count', 20))
        if not key:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        data = yield ddb_model.sound_search(key, page, count)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class DDB_SoundSearchs(ApiHandler):
    @gen.coroutine
    def get(self):
        key = self.get_argument('key', '')
        page = com.safeInt(self.get_argument('page', 1))
        count = com.safeInt(self.get_argument('count', 20))
        if not key:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        data = yield ddb_model.sound_search_arr(key, page, count)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class DDB_Video(ApiHandler):
    @gen.coroutine
    def get(self):
        vid = self.get_argument('id', '')
        if not vid:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        data = yield ddb_model.video(vid)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class DDB_Books(ApiHandler):
    @gen.coroutine
    def get(self):
        key = self.get_argument('layer', '')
        page = com.safeInt(self.get_argument('page', 1))
        count = com.safeInt(self.get_argument('count', 20))
        if not key:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        data = ddb_cache.get_books(key, page, count)
        if not data:
            data = yield ddb_model.books(key, page, count)
            ddb_cache.set_books(data, key, page, count)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class DDB_ClickLearn(ApiHandler):
    @gen.coroutine
    def get(self):
        cover = com.safeInt(self.get_argument('cover', 0))
        content = com.safeInt(self.get_argument('content', 0))
        if not cover and not content:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        data = yield ddb_model.click_learn(cover, content)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))