# coding=utf-8
# encoding=utf-8
#__author__="QZL"

import datetime
import hashlib
import json
import pymysql
import random
import time
import urllib, urllib2
import sys
reload(sys)
sys.setdefaultencoding('utf-8')

sys.path.append('/data/www/post_repair')

from store import repair_model, wxpay_model

from tornado import gen
from tornado.ioloop import IOLoop


print_format = '{time}, {method}, {data}'

def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

DEBUG = False


connect = pymysql.connect(
    host='**************',
    user='root',
    passwd='root',
    # db='rbcare',
    db='post_repair',
    charset='utf8',
    # host='localhost',
    # user='root',
    # passwd='q',
    # db='rbcare',
    # # db='readboydata',
    # charset='utf8',
) if DEBUG else pymysql.connect(
    host='rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',
    user='yxrepair',
    passwd='clC30J3DQLN3w7ma',
    db='post_repair',
    # db='readboydata',
    charset='utf8',
)
cursor = connect.cursor(cursor=pymysql.cursors.DictCursor)


def send_work_weichat_msg(user,msg):
    """
    发送企业微信消息
    :param user: 用户数组
    :param msg: 消息文本
    :return:
    """
    URL = 'https://oa.readboy.com/index.php?s=/Weixin/Api/send.html&auth_key='
    KEY = 'WeChatReadboyApi2018'

    ### sn
    t = str(int(time.time()))
    rd = str(random.randint(100000, 999999))
    key = hashlib.md5(KEY).hexdigest()
    s = '-'.join([t, rd, key])
    md5 = hashlib.md5(s).hexdigest()
    sn = '-'.join([t, rd, md5])

    data = {
        'tousername': user,
        'content': '寄修中心提醒你：\n' + msg
    }
    data = json.dumps(data)
    data = {'data':data}
    data = urllib.urlencode(data)
    url = URL + sn
    try:
        req = urllib2.Request(url, data)
        resp = urllib2.urlopen(req)
    except:
        print print_format.format(time=_now_date(), method='send_work_weichat_msg', data="work wechat api failed！")


def order_check():
    """
    十分钟内查询是否有需要审核的订单  有就通知相关人员
    :return:
    """
    ten_min = datetime.datetime.now() - datetime.timedelta(minutes=10)
    str_time = ten_min.strftime('%Y-%m-%d %H:%M:%S')
    sql = 'select count(*) as count from `order` where status=100 and created_at > %s'
    param = tuple([str_time])
    cursor.execute(sql, param)
    result = cursor.fetchone()
    print print_format.format(time=_now_date(), method='order_check', data='str_time:%s, result:%s' % (str_time, result))
    if result and result.get('count'):
        user = ['0431'] if DEBUG else ['0134']
        s = '你有{}个订单等待审核!'.format(result.get('count'))
        send_work_weichat_msg(user, s)


def order_paied():
    """
    查询十分钟内是否有新支付  有就通知相关人员--检测这个订单的人员
    :return:
    """
    ten_min = datetime.datetime.now() - datetime.timedelta(minutes=10)
    str_time = ten_min.strftime('%Y-%m-%d %H:%M:%S')
    # print str_time
    # str_time = '2020-04-10 12:00:07'
    sql_wechat_id = 'SELECT DISTINCT ps.wechat_id FROM `order` o left JOIN pay_order po ON o.sn = po.pr_sn ' \
                    'left JOIN pr_staff ps ON o.check_man = ps.user_id WHERE o.status = 600 and po.updated_at > %s'
    # print sql_wechat_id
    param_wechat_id = tuple([str_time])
    cursor.execute(sql_wechat_id, param_wechat_id)
    wechat_id = cursor.fetchall()
    if wechat_id:
        for i in wechat_id:
            sql = 'SELECT o.sn FROM `order` o left JOIN pay_order po ON o.sn = po.pr_sn left JOIN ' \
                  'pr_staff ps ON o.check_man = ps.user_id WHERE o.status = 600 ' \
                  'AND ps.wechat_id = %s AND po.updated_at > %s'
            param = tuple([i['wechat_id'], str_time])
            cursor.execute(sql, param)
            result = cursor.fetchall()

            if result:
                param = []
                for j in result:
                    param.append(j['sn'])
                field = '【' + '】、【'.join(param) + '】'
                user = ['0431', '0362'] if DEBUG else [i['wechat_id']]
                s = '订单编号为：{}\n的寄修订单已支付,请及时维修。'.format(field)
                send_work_weichat_msg(user, s)


def order_oa_sum():
    """
    查询十分钟内是否有新完成的订单，检查其是否有没取消的自选配件，统计对应配件已售数量
    :return:
    """
    ten_min = datetime.datetime.now() - datetime.timedelta(minutes=10)
    str_time = ten_min.strftime('%Y-%m-%d %H:%M:%S')
    # 先取新完成的订单
    sql_get_sn = ' select ol.*, o.optional_accessory_amount, o.optional_accessory_cast, o.optional_accessory_status' \
                 ' from order_log ol' \
                 '          left join `order` o on ol.pr_sn = o.sn' \
                 ' where ol.date > %s' \
                 '   and ol.pr_status = 900'
    param_get_sn = tuple([str_time])
    cursor.execute(sql_get_sn, param_get_sn)
    data_get_sn = cursor.fetchall()
    data_get_sn_has = filter(lambda x: x.get('optional_accessory_cast', 0) > 0 and x.get('optional_accessory_status', 0) != -2, data_get_sn)
    sn_list_has = [x.get('pr_sn') for x in data_get_sn_has]
    if not sn_list_has or len(sn_list_has) <= 0:
        return
    print print_format.format(time=_now_date(), method='order_oa_sum', data="sn_list_has: {sn_list_has}".format(sn_list_has=json.dumps(sn_list_has)))
    # 再统计对应配件
    sql_sum_select = 'select oa_id, sum(count) as sum from pr_optional_accessory poa '
    sql_sum_where = 'where pr_sn in (%s) and status = 0 and cancel_at is null ' % ','.join(['%s'] * len(sn_list_has))
    sql_sum_group = 'group by oa_id '
    sql_sum = sql_sum_select + sql_sum_where + sql_sum_group
    param_sum = tuple(sn_list_has)
    cursor.execute(sql_sum, param_sum)
    data_sum = cursor.fetchall()
    # 更新对应配件已售数量
    if not data_sum or len(data_sum) <= 0:
        return
    print print_format.format(time=_now_date(), method='order_oa_sum', data="data_sum: {data_sum}".format(data_sum=data_sum))
    sql_update = 'update optional_accessory set sold_num = sold_num + %s where id = %s '
    for di in data_sum:
        oa_id = di.get('oa_id', 0)
        sum = di.get('sum', 0)
        if oa_id <= 0 or sum <= 0:
            continue
        param_update = tuple([sum, oa_id])
        try:
            cursor.execute(sql_update, param_update)
            connect.commit()
        except Exception as e:
            print print_format.format(time=_now_date(), method='order_oa_sum', data=e)
            connect.rollback()


def order_finish():
    """
    订单超过七天自动签收
    :return:
    """
    updated_at = (datetime.datetime.now()-datetime.timedelta(days=7)).strftime('%Y-%m-%d')
    print print_format.format(time=_now_date(), method='order_finish', data=updated_at)
    sql = 'select sn from `order` where updated_at_last < %s and status=800  and updated_at_last != %s limit 1000'
    cursor.execute(sql, (updated_at, '0000-00-00 00:00:00'))
    datas = cursor.fetchall()
    # print sql
    print print_format.format(time=_now_date(), method='order_finish', data=datas)
    created_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if datas:
        for data in datas:
            # print data['sn']
            try:
                sql = 'update `order` set go_received=1, updated_at=%s, status=%s where status = 800 ' \
                      'and updated_at_last < %s and sn = %s'
                cursor.execute(sql, (created_at, 900, updated_at, data['sn']))

                param = tuple([data['sn'], 900, 900, 'web', '', 'finish_order', 0,
                               0, '产品回寄时间超过十五天已被自动接收，寄修服务结束', '', created_at])
                cursor.execute('insert into order_log (pr_sn, pr_status, log_status, log_from, relation_key, '
                               'operation, uid, admin, title, remark, date) values(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)',
                               param)
                connect.commit()
            except Exception as e:
                print print_format.format(time=_now_date(), method='order_finish', data=e)
                connect.rollback()


def cancel_order():
    """取消三十天内审核通过但不寄修的订单"""
    created_at = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d')
    sql = 'select sn from `order` where created_at < %s and (status=200 or status = 300) limit 10'
    cursor.execute(sql, created_at)
    datas = cursor.fetchall()
    # print sql
    print print_format.format(time=_now_date(), method='cancel_order', data=datas)
    updated_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if datas:
        for data in datas:
            # print data['sn']
            try:
                sql = 'update `order` set updated_at=%s, status=%s where status = 200 and sn = %s'
                cursor.execute(sql, (updated_at, -900, data['sn']))

                param = tuple([data['sn'], -900, -900, 'web', '', 'cancel order', 0,
                               0, '产品审核通过后超过三十天未寄修，寄修服务取消', '', updated_at])
                cursor.execute('insert into order_log (pr_sn, pr_status, log_status, log_from, relation_key, '
                               'operation, uid, admin, title, remark, date) values(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)',
                               param)
                connect.commit()
            except Exception as e:
                print print_format.format(time=_now_date(), method='cancel_order', data=e)
                connect.rollback()


@gen.coroutine
def order_status_ar_wxpay():
    """检查 用户弃修 微信已支付,但没记录到支付成功的订单"""
    # 查支付记录表
    # from_date = datetime.datetime(2022, 6,22) # id = 79200
    # from_date_str = from_date.strftime('%Y-%m-%d')
    limit = 50
    sql_1_select = 'select * from pay_order '
    sql_1_where = 'where id >= 79200 and com = 1 and is_paid = 0 and type = 1 and abandon = 0 '
    sql_1_order = 'order by id desc limit %d ' % limit
    sql_1 = sql_1_select + sql_1_where + sql_1_order
    cursor.execute(sql_1)
    pay_orders = cursor.fetchall()
    pay_orders_len = len(pay_orders)
    print print_format.format(time=_now_date(), method='order_status_ar_wxpay', data='pay_orders_len:%d' % pay_orders_len)
    if pay_orders and pay_orders_len > 0:
        pay_pr_sn_list = [x.get('pr_sn') for x in pay_orders]
        pay_rb_sn_list = [x.get('readboy_sn') for x in pay_orders]
        pay_pr_sn_dict = {x.get('pr_sn') : x for x in pay_orders}
        pay_rb_sn_dict = {x.get('readboy_sn') : x for x in pay_orders}
        print print_format.format(time=_now_date(), method='order_status_ar_wxpay', data=pay_pr_sn_list)
        # 查支付回调表
        sql_2_select = 'select * from pay_notify_log '
        sql_2_where = 'where readboy_sn in (%s) ' % ','.join(['%s'] * len(pay_rb_sn_list))
        sql_2 = sql_2_select + sql_2_where
        cursor.execute(sql_2, pay_rb_sn_list)
        pay_notify_logs = cursor.fetchall()
        pay_notify_logs_len = len(pay_notify_logs)
        print print_format.format(time=_now_date(), method='order_status_ar_wxpay', data='pay_notify_logs_len:%d' % pay_notify_logs_len)
        # 查订单信息表
        sql_3_select = 'select * from `order` '
        sql_3_where = 'where sn in (%s) ' % ','.join(['%s'] * len(pay_pr_sn_list))
        sql_3 = sql_3_select + sql_3_where
        cursor.execute(sql_3, pay_pr_sn_list)
        orders = cursor.fetchall()
        orders_len = len(orders)
        print print_format.format(time=_now_date(), method='order_status_ar_wxpay', data='orders_len:%d' % orders_len)
        # 检查支付情况
        if pay_notify_logs and pay_notify_logs_len > 0 and orders and orders_len > 0:
            order_sn_dict = {x.get('sn') : x for x in orders}
            for pn in pay_notify_logs:
                notify_str = pn.get('detail', '{}')
                notify_dic = json.loads(notify_str)
                notify_dic_return_code =  notify_dic.get('return_code')
                notify_dic_result_code =  notify_dic.get('result_code')
                pn_readboy_sn = pn.get('readboy_sn')
                if notify_dic_return_code == 'SUCCESS' and notify_dic_result_code == 'SUCCESS' and pn_readboy_sn:
                    pay_order = pay_rb_sn_dict.get(pn_readboy_sn)
                    pay_appid = pay_order.get('appid')
                    pay_pr_sn = pay_order.get('pr_sn')
                    order = order_sn_dict.get(pay_pr_sn)
                    wxpay_order = yield wxpay_model.query_pay(pay_appid, pn_readboy_sn)
                    print print_format.format(time=_now_date(), method='order_status_ar_wxpay', data='wxpay_order:%s' % wxpay_order)
                    if wxpay_order.get('return_code') == 'SUCCESS' and wxpay_order.get('result_code') == 'SUCCESS' \
                    and wxpay_order.get('trade_state') == 'SUCCESS' \
                    and wxpay_order.get('total_fee') == str(long(order.get('amount_in_ar')*100)):
                        change_data = {
                                'pr_sn': pay_order.get('pr_sn'),
                                'ar_pay_com': pay_order.get('com'),
                                'ar_rb_pay_sn': pay_order.get('readboy_sn'),
                                'ar_is_paid': 1,
                                'status': 600,
                                'ar_pay_sn': notify_dic.get('transaction_id'),
                                'connect': 3,
                                'accessory_amount': 0,
                                'accessory_cast': 0,
                                'amount': order.get('amount_in_ar'),
                                'pay_amount': order.get('amount_in_ar'),
                            }
                        print print_format.format(time=_now_date(), method='order_status_ar_wxpay', data='change_data:%s' % change_data)
                        # TO-DO:通过order检查状态是否仍处于500已检测待支付
                        # change_state = yield repair_model.ar_order_pay_sure(change_data)
                        # print print_format.format(time=_now_date(), method='order_status_ar_wxpay', data='change_state:%s' % change_state)


def main():
    order_oa_sum()
    order_finish()
    order_check()
    order_paied()
    cancel_order()
    # IOLoop.current().run_sync(order_status_ar_wxpay) # 只是临时操作的,用完就关掉了
    print


if __name__ == '__main__':
    main()
