# encoding=utf-8
# __author__ = 'lch'
import string
from collections import OrderedDict
from apscheduler.schedulers.blocking import BlockingScheduler
import sys
import hashlib
import time
import random
import json
import urllib, urllib2
import pymysql
import datetime
import logging
logging.basicConfig()
sched = BlockingScheduler()
reload(sys)
sys.setdefaultencoding('utf-8')


DEBUG = False
PAY_HOST = 'https://api-pay-hub.readboy.com'
APP_ID = 'screen_insurance_h5'
APP_SECRET = 'c10eb100ff2850cbcde0d78e532de8c6'
connect = pymysql.connect(
    host='**************',
    user='root',
    passwd='root',
    # db='rbcare',
    db='post_repair',
    charset='utf8',
    autocommit = True,
    # host='localhost',
    # user='root',
    # passwd='q',
    # db='rbcare',
    # # db='readboydata',
    # charset='utf8',
) if DEBUG else pymysql.connect(
    host='rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',
    user='rbcare',
    passwd='9ZS0sebU2IfMTp3U',
    db='post_repair',
    # db='readboydata',
    charset='utf8',
    autocommit=True,
)
cursor = connect.cursor(cursor=pymysql.cursors.DictCursor)


def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')


class ComplexEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, datetime.date):
            return obj.strftime('%Y-%m-%d')
        else:
            return json.JSONEncoder.default(self, obj)


def print_log(method, data=None, time=None):
    at = OrderedDict()
    at['time'] = time if time else _now_date()
    at['method'] = method
    dic = OrderedDict()
    dic['at'] = at
    dic['data'] = data
    print json.dumps(dic, ensure_ascii=False, cls=ComplexEncoder)


def nonce_str():
    char = string.ascii_letters + string.digits
    return "".join(random.choice(char) for _ in range(32))


def get_sign(obj):
    """生成签名"""
    od = OrderedDict(sorted(obj.items()))
    s = '&'.join([k + '=' + v for k, v in od.iteritems()])

    # 签名步骤二：在string后加入KEY
    app_key = '&appsecret=' + APP_SECRET
    StringA = "{0}{1}".format(s, app_key)
    # 签名步骤三：加密
    sign = hashlib.md5(StringA).hexdigest().upper()
    return sign


def make_data(param):
    default_param = {
        'appid': 'screen_insurance_h5',
        'nonce_str': nonce_str(),
    }
    param.update(default_param)
    param['sign'] = get_sign(param)
    # print param
    return param


def trade_pay_query(out_trade_no):
    url_base = PAY_HOST + '/trade/pay/query'
    param = {
        'out_trade_no': out_trade_no,
    }
    data = make_data(param)
    data = urllib.urlencode(data)
    header = {
        'content-type': 'application/x-www-form-urlencoded',
    }
    url = '%s?%s' % (url_base, data)
    print_log(method='trade_pay_query', data=url)
    req = urllib2.Request(url, headers=header)
    resp = urllib2.urlopen(req)
    read = resp.read()
    print_log(method='trade_pay_query', data=read)
    js = json.loads(read)
    if js.get('ok') == 1:
        return js.get('data')
    return None


def order_pay_sure(data, out_trade_no):
    sql = 'update broken_screen_insurance set is_paid = %s, paid_at = %s, status = %s, trade_plat = %s, pay_id = %s ' \
          'where sn = %s'
    ret = None
    if data.get('trade_plat') == 'alipay':
        trade_plat = 2
    else:
        trade_plat = 1
    try:
        cursor.execute(sql, (1, data['paid_at'], 300, trade_plat, data['pay_id'], out_trade_no))
        connect.commit()
        ret = True
    except Exception as e:
        print_log(method='order_pay_sure', data=e)
    return ret


def query_pay_order():
    print_log(method='query_pay_order', data='scheduler run')
    sql = 'SELECT sn, buy_date, is_supply, audited_at FROM broken_screen_insurance ' \
          'WHERE `status` = 200 AND is_paid = 0 '
    connect.ping(reconnect=True)
    cursor.execute(sql)
    data = cursor.fetchall()
    print_log(method='query_pay_order', data=data)
    if data:
        for di in data:
            ret = trade_pay_query(di['sn'])
            if ret and ret.get('status') == 1:
                print_log(method='query_pay_order', data=ret)
                order_pay_sure(ret, di['sn'])
            else:
                updated_at = _now_date()
                now = datetime.datetime.now()
                # 在购机时间内    允许重新提交申请 # 支付超时
                if (di.get('audited_at', now) + datetime.timedelta(hours=4)) < now:
                    print_log(method='query_pay_order', data={'sn': di['sn'], 'audited_at': di.get('audited_at')})
                    cursor.execute('update broken_screen_insurance set status = -300 , updated_at = %s '
                                'where sn = %s ', (updated_at, di['sn']))
                    connect.commit()


if __name__ == '__main__':
    query_pay_order()
    sched.add_job(query_pay_order, 'interval', seconds=300, id="1")
    sched.start()

