# coding=utf-8
import hashlib
import json
import time
import urllib
from collections import OrderedDict
import random

from tornado import httpclient
import tornado
from tornado.log import app_log
from tornado import gen

MAX_CLIENTS = 1000
httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)
HTTP_TIMEOUT = 30

MESSAGE_PUSH_URL = 'https://api1-yx.readboy.com'
# MESSAGE_PUSH_URL = 'http://api1-yxtest.readboy.com'
MESSAGE_PUSH_APP_ID = 'repair.readboy.com'
MESSAGE_PUSH_APP_KEY = '31879b82704954028802c9325bbcd55c'

SMS_HOST = 'https://api-sms.readboy.com'
SMS_KEY = '8e517d999976168979f81dc73d010c90'


@gen.coroutine
def message_push(data):
    """
    信息推送
    :param data:
    :return:
    """
    url = MESSAGE_PUSH_URL + '/tp/v1/notification/push'
    param = {
        'app_id': MESSAGE_PUSH_APP_ID,
        'timestamp': str(int(time.time()))
    }
    sign = getSign(param, MESSAGE_PUSH_APP_KEY)
    param['sign'] = sign
    param = urllib.urlencode(param)

    header = {
        'Content-Type': 'application/json;charset=utf-8',
    }

    url = url + '?' + param
    data = json.dumps(data)
    # data = urllib.urlencode(data)
    client = httpclient.AsyncHTTPClient()
    req = httpclient.HTTPRequest(url, method='POST', body=data, headers=header, connect_timeout=HTTP_TIMEOUT,
                                 request_timeout=HTTP_TIMEOUT, validate_cert=False)
    try:
        res = yield client.fetch(req)
        resp = tornado.escape.json_decode(res.body)
        app_log.info(resp)
    except:
        app_log.error('error connect', exc_info=True)


def getSign(obj, app_secret):
    """生成签名"""
    # 签名步骤一：按字典序排序参数
    od = OrderedDict(sorted(obj.items()))
    s = '&'.join([k + '=' + v for k, v in od.iteritems()])

    # 签名步骤二：在string后加入KEY
    StringA = s + app_secret
    # print String
    # 签名步骤三：加密

    sign = hashlib.md5(StringA).hexdigest()
    # String = s + '&sign=' + sign
    return sign


@gen.coroutine
def broken_screen_sms_push(sn, endpoint):
    """
    碎屏保信息推送
    :param sn:
    :param endpoint:
    :return:
    """
    h5_url = {
        "name": "碎屏保",
        "url": "https://h5-yx.readboy.com/brokenScreen/record",
        'icon': 'https://dt.readboy.com/rbcare/h5/icon/碎屏保.png',
        'index': 'https://h5-yx.readboy.com/brokenScreen/',
        'key': 'screen_insurance'
    }
    body = {
        'platform': ['ios', 'android'],
        'audience': {'endpoints': [str(endpoint)]},
        'title': '【碎屏增值服务生效提醒】',
        'content': '您申请的订单号为' + sn + '的碎屏增值服务单已生效，感谢您的支持！',
        'slug': 'broken_screen_insurance',
        'action': 'forward',
        'url': 'h5://' + json.dumps(h5_url)
    }
    message_push(body)


@gen.coroutine
def  broken_screen_sms(template, phone, template_param):
    """
    碎屏保生效短信通知
    :return:
    """
    url = SMS_HOST + '/index.php?s=/Sms/Api/send'
    code = random.randint(100000, 999999)
    authKey = str(int(time.time())) + '-' + str(code) + '-' \
              + hashlib.md5(str(int(time.time())) + '-' + str(code) + '-' + SMS_KEY).hexdigest()
    param = {
        'authKey': authKey,
        'appName': 'care.readboy.com',
        'templateCode': template,
        'phoneNumber': phone,
    }
    if template_param:
        param['templateParam'] = template_param
    param = urllib.urlencode(param)
    client = httpclient.AsyncHTTPClient()
    req = httpclient.HTTPRequest(url, method='POST', body=param, connect_timeout=HTTP_TIMEOUT,
                                 request_timeout=HTTP_TIMEOUT, validate_cert=False)
    try:
        res = yield client.fetch(req)
        resp = tornado.escape.json_decode(res.body)
        app_log.info(resp)
    except:
        app_log.error('send message error', exc_info=True)


@gen.coroutine
def pay_overtime_sms_push(sn, endpoint):
    """
    碎屏保信息推送
    :param sn:
    :param endpoint:
    :return:
    """
    h5_url = {
        "name": "碎屏保",
        "url": "https://h5-yx.readboy.com/brokenScreen/record",
        'icon': 'https://dt.readboy.com/rbcare/h5/icon/碎屏保.png',
        'index': 'https://h5-yx.readboy.com/brokenScreen/',
        'key': 'screen_insurance'
    }
    body = {
        'platform': ['ios', 'android'],
        'audience': {'endpoints': [str(endpoint)]},
        'title': '【碎屏增值服务支付即将超时】',
        'content': '订单编号为' + sn + '的碎屏增值服务保单即将支付超时，请及时完成支付！',
        'slug': 'broken_screen_insurance',
        'action': 'forward',
        'url': 'h5://' + json.dumps(h5_url)
    }
    message_push(body)

if __name__ == '__main__':
    broken_screen_sms()
