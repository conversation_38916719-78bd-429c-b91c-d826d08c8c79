# encoding=utf-8
# __author__ = 'lch'

from store import DB_PR
# import util.com as com
from tornado import gen
from log import db_log

@gen.coroutine
def store_order_log(data):
    """
    记录寄修订单操作日志
    :param data: 操作数据
    :return:
    """
    if not isinstance(data, dict) or not data.get('pr_sn'):
        raise gen.Return(False)
    key = ['pr_sn', 'pr_status', 'log_status', 'log_from', 'relation_key', 'operation', 'uid', 'admin', 'title', 'remark', 'date']
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    ### 处理输入数据
    param = tuple([data.get(i) for i in key])
    sql = 'insert into order_log (' + fields + ') values ('+ values +')'
    try:
        cur = yield DB_PR.execute(sql, param)
    except:
        db_log.error('store order log error:', exc_info=True)
        raise gen.Return(False)
    raise gen.Return(True)
