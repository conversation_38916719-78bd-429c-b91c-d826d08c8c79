# encoding=utf-8
# __author__ = 'lch'

import tornado.web
from tornado import gen
import traceback

import util.com as com
from handler import ApiHandler, tokenauth
import store.endpoint_model as endpoint_model


class EndpointOptions(ApiHandler):
    @gen.coroutine
    @tokenauth
    def get(self):
        options = yield endpoint_model.getOptions()
        self.success(options)


class Endpoints(ApiHandler):
    """终端列表"""
    @gen.coroutine
    def get(self):
        tp = com.safeInt(self.get_argument('type', 1), 1)
        city = self.get_argument('city', None)
        district = self.get_argument('district', None)
        lng = com.safeFloat(self.get_argument('lng', 0))
        lat = com.safeFloat(self.get_argument('lat', 0))
        cnt = com.safeInt(self.get_argument('count', 10), 10)

        if city:
            eps = yield endpoint_model.findByDistrict(city, district, tp)
        elif lng > 0 and lat > 0:
            eps = yield endpoint_model.findByLoc(lng, lat, tp, cnt)
        else:
            raise gen.Return(self.error(u'请提交城市或坐标参数', com.ERROR_PARAM))
        
        self.success(eps) if eps else self.error(u'没找到相关网点', com.ERROR_EMPTY_DATA)


class EndpointImage(ApiHandler):
    """终端形象"""
    @gen.coroutine
    @tokenauth
    def get(self):
        endpoint = com.safeInt(self.get_argument('endpoint'))
        info = yield endpoint_model.getImage(endpoint)

        self.success(info) if info else self.error(u'未找到终端(%s)的形象信息' % endpoint, com.ERROR_EMPTY_DATA)


class EndpointNewImage(ApiHandler):
    "终端形象变更"
    @gen.coroutine
    @tokenauth
    def get(self):
        endpoint = com.safeInt(self.get_argument('endpoint'))
        info = yield endpoint_model.getNewImage(endpoint)

        self.success(info) if info else self.error(u'未找到终端(%s)的形象变更申请' % endpoint, com.ERROR_EMPTY_DATA)

    @gen.coroutine
    @tokenauth
    def post(self):
        try:
            info = tornado.escape.json_decode(self.request.body)
            if 'id' not in info and 'endpoint' not in info:
                self.error(u"参数错误，缺少【变更申请编号】或【终端编号】", com.ERROR_PARAM)
                raise gen.Return()
        except:
            traceback.print_exc()
            self.error(u"参数错误，解析JSON格式失败", com.ERROR_PARAM)
            raise gen.Return()

        ok, err = yield endpoint_model.saveNewImage(info)

        self.success() if ok else self.error(err, com.ERROR_UNKNOWN)


class EndpointNotice(ApiHandler):
    @gen.coroutine
    @tokenauth
    def get(self):
        ts = com.safeInt(self.get_argument('ts', 0))
        count = com.safeInt(self.get_argument('count', 10), 10)
        cards = yield endpoint_model.notifications(ts, count)
        self.success(cards) if cards else self.error(u'暂时没有公告', com.ERROR_EMPTY_DATA)
        pass

