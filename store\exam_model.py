#coding=utf-8
#encoding=utf-8
#__author__="QZL"

import tornado
from tornado import gen, httpclient
from store import DB
import random
import time
from hashlib import md5
import json
import urllib
from tornado.log import app_log
from util import com
import datetime
import traceback

MAX_CLIENTS = 1000
httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)
server_address = 'https://api-sms.readboy.com/index.php?s=/Sms/Api/send'
HTTP_TIMEOUT = 30


def changeTS(data, fields):
    """
    转换时间戳
    :param data: 数据
    :param fields: 需要转换时间戳的字段
    :return:
    """
    for i in fields:
        data[i] = data[i].strftime('%Y-%m-%d %H:%M:%S')
    return data


def make_url(RecNum='15819341131',
             SignName=u'读书郎'.encode('utf-8'), TemplateCode='SMS_56415001'):
    """
    构造发送验证码url
    :param RecNum: 接受信息手机号
    :param SignName: 签名
    :param TemplateCode: 模板名
    :return:
    """
    ts = str(int(time.time()))
    key = '8e517d999976168979f81dc73d010c90'
    code = str(random.randint(100000, 999999))
    ParamString = {"code": code}
    authkey = ts+'-'+code+'-'+md5(ts+'-'+code+'-'+key).hexdigest()
    parameters = {
        # user params
        'authKey': authkey,
        'appName': 'care.readboy.com',
        'signName': SignName,
        'templateCode': TemplateCode,
        'phoneNumber': RecNum,
        'templateParam': json.dumps(ParamString),
    }
    url = server_address + "&" + urllib.urlencode(parameters)
    return url, code

@gen.coroutine
def salseman_list(endpoint_id):
    """
    终端导购员列表
    :param endpoint_id: 终端id
    :return:
    """
    if not endpoint_id:
        raise gen.Return(None)
    cur = yield DB.execute('select * from salesman where endpoint_id=%s', endpoint_id)
    data = cur.fetchall()
    if data:
        for i in data:
            i['created_at'] = i['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            i['updated_at'] = i['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
    raise gen.Return(data)


@gen.coroutine
def phone_code(phone):
    """
    手机号验证码
    :param phone: 手机号
    :return:
    """
    client = httpclient.AsyncHTTPClient()
    # t0 = time.time()
    url, code = make_url(phone, TemplateCode='SMS_69985149')
    request = httpclient.HTTPRequest(url, method='GET', connect_timeout=HTTP_TIMEOUT,
                                  request_timeout=HTTP_TIMEOUT, validate_cert=False)
    try:
        response = yield client.fetch(request)
        # t1 = time.time()
        ret = tornado.escape.json_decode(response.body)
        if ret.get('code') == '200':
            # t2 = time.time()
            ts = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            store = yield DB.execute('replace into phone_code set phone=%s, code=%s, updated_at=%s', (phone, code, ts))
            data = True
        else:
            data = None
            app_log.error('phone code send error: phone=%s, code=%s, response:%s' % (phone, code, com.prettyJson(ret)))
        # t3 = time.time()
        # print 't01=%f, t12=%f, t32=%f' % (t1-t0, t2-t1, t3-t2)
    except Exception as e:
        app_log.error('phone code send error: phone=%s, code=%s, error:%s' % (phone, code, e))
        data = None
    raise gen.Return(data)


@gen.coroutine
def check_phone_code(phone, code):
    """
    检查手机验证码是否匹配
    :param phone: 手机号
    :param code: 验证码
    :return:
    """
    if not phone or not code:
        raise gen.Return(False)
    cur = yield DB.execute('select * from phone_code where phone=%s', phone)
    phone_code = cur.fetchone()
    if phone_code.get('code'):
        if phone_code['code'] == code:
            raise gen.Return(True)
    raise gen.Return(False)


@gen.coroutine
def create_salesman(data):
    """
    创建导购员
    :param data: 导购员信息数据dict
    :return:
    """
    if not data.get('phone') or not data.get('phone_code') or not data.get('name'):
        raise gen.Return(0)
    if not check_phone_code(data['phone'], data['phone_code']):
        raise gen.Return(-2)
    field = ['name', 'endpoint_id', 'phone', 'code']
    param = [data.get(i) for i in field]
    param.append(1)
    param.append(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    param.append(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    p_tuple = tuple(param)
    try:
        insert = yield DB.execute('insert into salesman (name, endpoint_id, phone, code, status, created_at, updated_at) '+
                              'values (%s, %s, %s, %s, %s, %s, %s)', p_tuple)
        if insert.lastrowid < 1:
            raise gen.Return(0)
    except Exception as e:
        app_log.error('insert salesman error:%s' % e)
        raise gen.Return(-1)
    raise gen.Return(1)


@gen.coroutine
def set_salesman(endpoint_id, salesman, is_active):
    """
    启用禁用导购员
    :param endpoint_id: 终端id
    :param salesman: 导购员id
    :param is_active: 是否启用
    :return:
    """
    if is_active:
        is_active = 1
    else:
        is_active = 0
    ts = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    param = [is_active, ts, endpoint_id, salesman]
    p_t = tuple(param)
    try:
        cur = yield DB.execute('update salesman set status=%s, updated_at=%s where endpoint_id=%s and id=%s', p_t)
    except Exception as e:
        app_log.error('set salesman error:%s' % e)
        raise gen.Return(False)
    raise gen.Return(True)


@gen.coroutine
def check_salesman(salesman, code):
    """
    验证导购员身份
    :param salesman: 导购员id
    :param code: 导购员验证码
    :return:
    """
    if not salesman or not code:
        raise gen.Return(False)
    cur = yield DB.execute('select count(*) as count from salesman where id=%s and code=%s', (salesman, code))
    data = cur.fetchone()
    if data.get('count') and data['count'] > 0:
        raise gen.Return(True)
    raise gen.Return(False)


@gen.coroutine
def paper(paper):
    """
    获取试卷题目
    :param paper: 试卷id
    :return:
    """
    if not paper:
        raise gen.Return(None)
    cur = yield DB.execute('select * from paper where id=%s', paper)
    paper_info = cur.fetchone()
    if not paper_info or not paper_info.get('number'):
        raise gen.Return(None)
    paper_info = changeTS(paper_info, ['created_at', 'updated_at', 'start', 'end'])
    cur = yield DB.execute('select q.* from paper_question as pq left join question as q on pq.question_id=q.id where pq.paper_id=%s', paper)
    question = cur.fetchall()
    if question:
        q = list()
        for i in question:
            i['options'] = json.loads(i['options']) if i['options'] else None
            i['answers'] = json.loads(i['answers']) if i['answers'] else None
            i['blank'] = json.loads(i['blank']) if i['blank'] else None
            q.append(changeTS(i, ['created_at', 'updated_at']))
        ### 题目乱序随机
        q = random.sample(q, paper_info.get('number'))
        paper_info['questions'] = q
    raise gen.Return(paper_info)


@gen.coroutine
def history(salesman):
    """
    获取导购员的试卷历史，包括可作答但是未作答的试卷
    :param salesman: 导购id
    :return:
    """
    if not salesman:
        raise gen.Return(None)
    # now = datetime.datetime.now().strf('%Y-%m-%d %H:%M:%S')
    cur = yield DB.execute('select * from paper where now() > start and now() < end and status=1')
    papers = cur.fetchall()
    if not papers:
        raise gen.Return(None)
    # print papers
    ret = list()
    for i in papers:
        ret.append(changeTS(i, ['created_at', 'updated_at', 'start', 'end']))
    cur = yield DB.execute('select paper_id, score, response_times, response_at from paper_response where salesman_id=%s', salesman)
    his = cur.fetchall()
    his_dict = dict()
    if his:
        his_dict = {str(i['paper_id']): changeTS(i, ['response_at']) for i in his}
    for i in ret:
        i['history'] = his_dict.get(str(i['id']))
    raise gen.Return(ret)


@gen.coroutine
def response(endpoint_id, data):
    """
    答卷
    :param endpoint_id: 终端id
    :param data: 数据dict
    :return:
    """
    if not endpoint_id or not data:
        raise gen.Return(False)
    if not data.get('salesman_id') or not data.get('paper_id'):
        raise gen.Return(False)
    ### 验证试卷有效性
    cur = yield DB.execute('select id from paper where now() > start and now() < end and status=1 and id=%s', data['paper_id'])
    paper_id = cur.fetchone()
    if not paper_id:
        raise gen.Return(False)
    data['endpoint_id'] = endpoint_id
    ### 检查是否可以作答
    cur = yield DB.execute('select id, score, response_times from paper_response where endpoint_id=%s and salesman_id=%s and paper_id=%s'
                           , (endpoint_id, data['salesman_id'], data['paper_id']))
    old = cur.fetchone()
    ### 超过答题次数
    if old and old.get('response_times') and old['response_times'] >= 3:
        raise gen.Return(False)
    trans = yield DB.begin()
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    try:
        row_id = 0
        ### 有旧记录
        if old:
            row_id = old.get('id')
            ### 分数没有提升
            if old.get('score') > data['score']:
                yield trans.execute('update paper_response set response_times=response_times+1, updated_at=%s where id=%s', (now, row_id))
            else:
                yield trans.execute('update paper_response set response_times=response_times+1, score=%s, accuracy=%s, duration=%s, response_at=%s, updated_at=%s where id=%s',
                              (data.get('score'), data.get('accuracy'), data.get('duration'), now, now, row_id))

        else:
            field = ['paper_id', 'endpoint_id', 'salesman_id', 'score', 'accuracy', 'duration']
            param = [data.get(i) for i in field]
            param.append(now)
            param.append(now)
            param.append(now)
            p_t = tuple(param)
            cur = yield trans.execute('insert into paper_response (response_times, paper_id, endpoint_id, salesman_id, score, accuracy, duration, response_at, created_at, updated_at)'+
                          ' values (1, %s, %s, %s, %s, %s, %s, %s, %s, %s)', p_t)
            if cur.lastrowid > 0:
                row_id = cur.lastrowid
            else:
                raise Exception('insert paper_response error')
        ### 插入答题详情
        questions = data.get('questions')
        if questions:
            for q in questions:
                field = ['paper_id', 'endpoint_id', 'salesman_id']
                param = [data.get(i) for i in field]
                q_field = ['question_id', 'is_right', 'duration']
                for f in q_field:
                    param.append(q.get(f, 0))
                param.append(row_id)
                param.append(now)
                param.append(now)
                p_t = tuple(param)
                yield trans.execute('insert into question_response (paper_id, endpoint_id, salesman_id, question_id, is_right, duration, response_id, created_at, updated_at)'+
                              ' values (%s, %s, %s, %s, %s, %s, %s, %s, %s)', p_t)
        yield trans.commit()
    except:
        traceback.print_exc()
        yield trans.rollback()
        raise gen.Return(False)
    raise gen.Return(True)
