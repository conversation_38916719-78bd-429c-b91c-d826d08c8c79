# encoding=utf-8
import datetime
import math
from operator import itemgetter

from cachetools import cached, TTLCache
from tornado import gen
from tornado.log import app_log

from store import DB_PR


@gen.coroutine
def pr_year_number():
    year = datetime.datetime.now().year
    start = str(year) + '-01-01 00:00:00'
    end = str(year + 1) + '-01-01 00:00:00'
    sql = 'SELECT count( DISTINCT IF(uid = 0, phone, uid)) as people_year_number , count(barcode) as ' \
          'machine_year_number FROM `order` WHERE status != -900  and created_at BETWEEN %s and %s'
    param = [start, end]
    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchone()
    raise gen.Return([data['people_year_number'], data['machine_year_number']])


@gen.coroutine
def post_person_number_stat(start, end, machine_category_id, model_id):
    """
    获取寄修人数 台数
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT count(DISTINCT uid) as people_number, ' \
              'count(barcode) as machine_number FROM `order` ' \
              'WHERE status != -900 and created_at BETWEEN %s and %s and model_id = %s'
        param = tuple([start, end, model_id])
    elif machine_category_id:
        sql = 'SELECT count(DISTINCT o.uid) as people_number, ' \
              'count(o.barcode) as machine_number FROM `order` o ' \
              'right join machine_type mt on o.model_id = mt.model_id ' \
              'WHERE status != -900 and o.created_at BETWEEN %s and %s and mt.category_id = %s'
        param = tuple([start, end, machine_category_id])
    else:
        sql = 'SELECT  count(DISTINCT uid) as people_number, ' \
              'count(barcode) as machine_number FROM `order` ' \
              'WHERE status != -900 and created_at BETWEEN %s and %s'
        param = tuple([start, end])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    if data:
        raise gen.Return([data['people_number'], data['machine_number']])
    raise gen.Return(data)


@gen.coroutine
def post_person_number(start, end, machine_category_id, model_id):
    """
    获取寄修人数 台数
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT date_format(created_at, \'%%Y-%%m-%%d\') as days,  count(DISTINCT uid) as people_number, ' \
              'count(DISTINCT barcode) as machine_number FROM `order` ' \
              'WHERE status != -900 and created_at BETWEEN %s and %s and model_id = %s GROUP BY days'
        param = tuple([start, end, model_id])
    elif machine_category_id:
        sql = 'SELECT date_format(o.created_at, \'%%Y-%%m-%%d\') as days,  count(DISTINCT o.uid) as people_number, ' \
              'count(DISTINCT o.barcode) as machine_number FROM `order` o ' \
              'right join machine_type mt on o.model_id = mt.model_id ' \
              'WHERE status != -900 and o.created_at BETWEEN %s and %s and mt.category_id = %s GROUP BY days'
        param = tuple([start, end, machine_category_id])
    else:
        sql = 'SELECT date_format(created_at, \'%%Y-%%m-%%d\') as days,  count(DISTINCT uid) as people_number, ' \
              'count(DISTINCT barcode) as machine_number FROM `order` ' \
              'WHERE status != -900 and created_at BETWEEN %s and %s GROUP BY days'
        param = tuple([start, end])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def pr_status_number(start, end, machine_category_id, model_id):
    """
    已审核-已检测-已支付-已维修-已回寄数量统计
    :param end:
    :param start:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        select = 'SELECT sum(ol.pr_status=200) as audit_pass, sum(pr_status=400) as come_sure, ' \
                 'sum(ol.pr_status=500) as check_finish ,' \
                 'sum(ol.pr_status=600) as pay_finish, sum(ol.pr_status=700) as repair_finish,' \
                 'sum(ol.pr_status=800) as exp_go_success FROM order_log ol ' \
                 'right join `order` o on ol.pr_sn = o.sn '
        where = ' WHERE ol.pr_status in (200,400,500,600,700,800) and ol.date BETWEEN %s and %s and o.model_id = %s '
        param = tuple([start, end, model_id])
        # if top_agency:
        #     select = select + 'RIGHT JOIN region_view rv ON (o.district = rv.region_name or o.city = rv.region_name) ' \
        #                       'RIGHT JOIN agency_region_view arv ON rv.region_id = arv.region_id'
        #     where = where + ' and arv.agency_id = %s'
        #     param = tuple([start, end, model_id, top_agency])
        sql = select + where
    elif machine_category_id:
        select = 'SELECT sum(ol.pr_status=200) as audit_pass, sum(pr_status=400) as come_sure, ' \
                 'sum(ol.pr_status=500) as check_finish ,' \
                 'sum(ol.pr_status=600) as pay_finish, sum(ol.pr_status=700) as repair_finish,' \
                 'sum(ol.pr_status=800) as exp_go_success FROM order_log ol ' \
                 'right join `order` o on ol.pr_sn = o.sn ' \
                 'right join machine_type mt on o.model_id = mt.model_id '
        where = 'WHERE ol.pr_status in (200,400,500,600,700,800) and ol.date BETWEEN %s and %s  and mt.category_id = %s'
        param = tuple([start, end, machine_category_id])
        # if top_agency:
        #     select = select + 'RIGHT JOIN region_view rv ON (o.district = rv.region_name or o.city = rv.region_name) ' \
        #                       'RIGHT JOIN agency_region_view arv ON rv.region_id = arv.region_id'
        #     where = where + ' and arv.agency_id = %s'
        sql = select + where
    else:
        select = 'SELECT  sum(ol.pr_status=200) as audit_pass, sum(ol.pr_status=400) as come_sure, ' \
                 'sum(ol.pr_status=500) as check_finish ,sum(ol.pr_status=600) as pay_finish, ' \
                 'sum(ol.pr_status=700) as repair_finish,sum(ol.pr_status=800) as exp_go_success ' \
                 'FROM order_log ol '
        where = 'WHERE ol.pr_status in (200, 400, 500,600,700,800) and ol.date BETWEEN %s and %s '
        param = tuple([start, end])
        # if top_agency:
        #     select = select + ' right join `order` o on ol.pr_sn = o.sn RIGHT JOIN region_view rv ON ' \
        #                       '(o.district = rv.region_name or o.city = rv.region_name) ' \
        #                       'RIGHT JOIN agency_region_view arv ON rv.region_id = arv.region_id'
        #     where = where + ' and arv.agency_id = %s'
        sql = select + where
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def order_to_status_group_number():
    """
    待知会+待检测+待支付+待维修+待发货-数量统计
    """
    # 读取数据
    sql_base = 'SELECT `status`, count(*) AS count FROM `order` '
    sql_where = 'WHERE `status` IN (400, 410, 480, 500, 600, 700) '
    sql_group = 'GROUP BY `status` '
    sql = sql_base + sql_where + sql_group
    cur = yield DB_PR.execute(sql)
    data = cur.fetchall()
    # 返回格式
    to_customer_inform = 0
    to_check_num = 0
    to_pay_num = 0
    to_repair_num = 0
    to_exp_go_num = 0
    for di in data:
        if di and di.get('status'):
            di_status = di.get('status')
            di_count = di.get('count', 0)
            if di_status == 400:
                to_customer_inform = di_count
            if di_status == 410 or di_status == 480:
                to_check_num += di_count
            if di_status == 500:
                to_pay_num = di_count
            if di_status == 600:
                to_repair_num = di_count
            if di_status == 700:
                to_exp_go_num = di_count
    ret = {
        'to_customer_inform': to_customer_inform,
        'to_check_num': to_check_num,
        'to_pay_num': to_pay_num,
        'to_repair_num': to_repair_num,
        'to_exp_go_num': to_exp_go_num,
    }
    raise gen.Return(ret)


@gen.coroutine
def order_four_day_not_push_pay_number():
    """
    快递签收后,超4天未检测-数量统计
    """
    sql_base = 'SELECT count(*) AS count FROM `order` '
    sql_where = 'WHERE `status` IN (400, 410, 480) AND receive_time < DATE_SUB(now(), interval 4 day) '
    sql = sql_base + sql_where
    cur = yield DB_PR.execute(sql)
    data = cur.fetchone()
    ret = 0
    if data and data.get('count'):
        ret = data.get('count')
    raise gen.Return(ret)


@gen.coroutine
def order_not_exp_go_count():
    """
    从快递签收后并未回寄的机器-分段统计
    """
    # 读取数据
    sql = 'SELECT SUM(DATEDIFF(now(), o.receive_time) < 3)                        AS lt_three, ' \
          '       SUM(IF(DATEDIFF(now(), o.receive_time) BETWEEN 3 AND 4, 1, 0))  AS three_to_five, ' \
          '       SUM(IF(DATEDIFF(now(), o.receive_time) BETWEEN 5 AND 6, 1, 0))  AS five_to_seven, ' \
          '       SUM(IF(DATEDIFF(now(), o.receive_time) BETWEEN 7 AND 14, 1, 0)) AS seven_to_fifteen, ' \
          '       SUM(DATEDIFF(now(), o.receive_time) >= 15)                      AS gte_fifteen ' \
          'FROM `order` o ' \
          'WHERE o.`status` IN (400, 410, 480, 490, 500, 600, 700) ' \
          '  AND o.receive_time IS NOT NULL '
    cur = yield DB_PR.execute(sql)
    data = cur.fetchone()
    # 返回格式
    lt_three = data.get('lt_three', 0)
    three_to_five = data.get('three_to_five', 0)
    five_to_seven = data.get('five_to_seven', 0)
    seven_to_fifteen = data.get('seven_to_fifteen', 0)
    gte_fifteen = data.get('gte_fifteen', 0)
    ret = [
        {
            'time': '< 3天',
            'count': lt_three
        },
        {
            'time': '3-5天',
            'count': three_to_five
        },
        {
            'time': '5-7天',
            'count': five_to_seven
        },
        {
            'time': '7-15天',
            'count': seven_to_fifteen
        },
        {
            'time': '15天以上',
            'count': gte_fifteen
        }
    ]
    count = reduce(lambda t, x: t + x.get('count', 0), ret, 0)
    for ri in ret:
        divide = (ri.get('count', 0) / count) if count > 0 else 0
        ri['rate'] = '%.2f%%' % (100 * divide)
    ret.append({
        'time': '全部',
        'count': count,
        'rate': '100%'
    })
    raise gen.Return(ret)


@gen.coroutine
def pr_in_period(start, end, machine_category_id, model_id):
    """
    保内保外
    # :param top_agency:
    :param start:
    :param end:
    :param machine_category_id:
    :param model_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        select = 'SELECT DATE_FORMAT(o.updated_at_last, \'%%Y-%%m-%%d\' ) as days, sum(o.pay_amount = 0) ' \
                 'as in_period_number, sum(o.pay_amount > 0) as out_period_number from `order` o '
        where = 'WHERE o.status in(800, 900) and o.updated_at_last BETWEEN %s and %s and o.model_id = %s '
        param = [start, end, model_id]
    elif machine_category_id:
        select = 'SELECT DATE_FORMAT(o.updated_at_last, \'%%Y-%%m-%%d\' ) as days, sum(o.pay_amount = 0) ' \
                 'as in_period_number, sum(o.pay_amount > 0) as out_period_number from `order` o ' \
                 'right join machine_type mt on o.model_id = mt.model_id '
        where = ' WHERE o.status in(800, 900) and o.updated_at_last BETWEEN %s and %s and mt.category_id = %s '
        param = [start, end, machine_category_id]
    else:
        select = 'SELECT DATE_FORMAT(o.updated_at_last, \'%%m-%%d\' ) as days, sum(o.pay_amount = 0) ' \
                 'as in_period_number, sum(o.pay_amount > 0) as out_period_number from `order` o '
        where = ' WHERE o.status in(800, 900) and o.updated_at_last BETWEEN %s and %s '
        param = [start, end]
    # if top_agency:
    #     select = select + 'RIGHT JOIN region_view rv ON (o.district = rv.region_name or o.city = rv.region_name) ' \
    #                       'RIGHT JOIN agency_region_view arv ON rv.region_id = arv.region_id'
    #     where = where + ' and arv.agency_id = %s'
    #     param.append(top_agency)
    param = tuple(param)
    sql = select + where + ' GROUP BY days'
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def administration_distribute(start, end, machine_category_id, model_id):
    """
    行政保外
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT count(DISTINCT(o.sn)) count, r.region_name as distribute FROM `order` o ' \
              'RIGHT JOIN rbcare.region r ON o.province = r.region_name ' \
              'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s and model_id = %s ' \
              'GROUP BY province'
        param = tuple([start, end, model_id])
    elif machine_category_id:
        sql = 'SELECT count(DISTINCT(o.sn)) count, r.region_name as distribute FROM `order` o ' \
              'RIGHT JOIN rbcare.region r ON o.province = r.region_name ' \
              'right join machine_type mt on o.model_id = mt.model_id ' \
              'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s ' \
              ' and mt.category_id = %s GROUP BY province'
        param = tuple([start, end, machine_category_id])
    else:
        sql = 'SELECT count(DISTINCT(o.sn)) count, r.region_name as distribute FROM `order` o ' \
              'RIGHT JOIN rbcare.region r ON o.province = r.region_name ' \
              'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s GROUP BY province'
        param = tuple([start, end])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def damage_accessory(start, end, machine_category_id, model_id):
    """
    损坏物料
    :param end:
    :param start:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT mt.model_id, mt.name as model_name, pum.mat_id, concat_ws(\'-\',mt.name,mat.title) as title, ' \
              'sum(pum.count) as count, o.reason FROM pr_used_material as pum ' \
              'LEFT JOIN machine_accessory_tree as mat ON pum.mat_id =mat.id ' \
              'LEFT JOIN machine_type as mt ON mat.model_id = mt.model_id ' \
              'LEFT JOIN `order` o on o.sn = pum.pr_sn ' \
              'WHERE pum.created_at BETWEEN %s and %s and o.model_id=%s GROUP BY pum.mat_id'
        param = tuple([start, end, model_id])
    elif machine_category_id:
        sql = 'SELECT mt.model_id, mt.name as model_name, pum.mat_id, concat_ws(\'-\',mt.name,mat.title) as title, ' \
              'sum(pum.count) as count, o.reason FROM pr_used_material as pum ' \
              'LEFT JOIN machine_accessory_tree as mat ON pum.mat_id =mat.id ' \
              'LEFT JOIN machine_type as mt ON mat.model_id = mt.model_id ' \
              'LEFT JOIN `order` o on o.sn = pum.pr_sn ' \
              'WHERE pum.created_at BETWEEN %s and %s and mt.category_id = %s GROUP BY pum.mat_id'
        param = tuple([start, end, machine_category_id])
    else:
        sql = 'SELECT mt.model_id, mt.name as model_name, pum.mat_id, concat_ws(\'-\',mt.name,mat.title) as title, ' \
              'sum(pum.count) as count, o.reason FROM pr_used_material as pum ' \
              'LEFT JOIN machine_accessory_tree as mat ON pum.mat_id =mat.id ' \
              'LEFT JOIN machine_type as mt ON mat.model_id = mt.model_id ' \
              'LEFT JOIN `order` o on o.sn = pum.pr_sn ' \
              'WHERE pum.created_at BETWEEN %s and %s  GROUP BY pum.mat_id'
        param = tuple([start, end])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


# @cached(cache=TTLCache(maxsize=10240, ttl=432000))
@gen.coroutine
def machine_proportion(start, end, machine_category_id, model_id):
    """
    机型分布
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        select = 'SELECT count(DISTINCT(o.sn)) count, o.model_name as `name` FROM `order` o '
        where = 'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s and model_id = %s'
        param = [start, end, model_id]
    elif machine_category_id:
        select = 'SELECT count(DISTINCT(o.sn)) count, o.model_name as `name` FROM `order` o ' \
                 'right join machine_type mt on o.model_id = mt.model_id '
        where = 'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s ' \
                'and mt.category_id = %s'
        param = [start, end, machine_category_id]
    else:
        select = 'SELECT count(DISTINCT(o.sn)) count, o.model_name as `name` FROM `order` o'
        where = ' WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s'
        param = [start, end]
    # if top_agency:
    #     select = select + 'RIGHT JOIN region_view rv ON (o.district = rv.region_name or o.city = rv.region_name) ' \
    #                       'RIGHT JOIN agency_region_view arv ON rv.region_id = arv.region_id'
    #     where = where + ' and arv.agency_id = %s'
    #     param.append(top_agency)
    param = tuple(param)
    sql = select + where + ' GROUP BY o.model_id'
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def channel(start, end, machine_category_id, model_id):
    """
    途径
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        select = 'SELECT count(DISTINCT(o.sn)) count, o.type FROM `order` o '
        where = 'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s and model_id = %s'
        param = [start, end, model_id]
    elif machine_category_id:
        select = 'SELECT count(DISTINCT(o.sn)) count, o.type FROM `order` o ' \
                 'right join machine_type mt on o.model_id = mt.model_id '
        where = 'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s ' \
                'and mt.category_id = %s'
        param = [start, end, machine_category_id]
    else:
        select = 'SELECT count(DISTINCT(o.sn)) count, o.type FROM `order` o'
        where = ' WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s'
        param = [start, end]
    # if top_agency:
    #     select = select + 'RIGHT JOIN region_view rv ON (o.district = rv.region_name or o.city = rv.region_name) ' \
    #                       'RIGHT JOIN agency_region_view arv ON rv.region_id = arv.region_id'
    #     where = where + ' and arv.agency_id = %s'
    #     param.append(top_agency)
    param = tuple(param)
    sql = select + where + ' GROUP BY o.type'
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


# @cached(cache=TTLCache(maxsize=10240, ttl=432000))
@gen.coroutine
def category_proportion(start, end, machine_category_id, model_id):
    """
    品类分布
    # :param top_agency:
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    if machine_category_id or model_id:
        raise gen.Return([])
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'

    select = 'SELECT count(DISTINCT(o.sn)) count, mc.`name` FROM `order` o ' \
             'right join machine_type mt on o.model_id = mt.model_id ' \
             'RIGHT JOIN machine_category mc ON mt.category_id = mc.id '
    where = ' WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s and mc.visible = 1'
    param = [start, end]
    # if top_agency:
    #     select = select + 'RIGHT JOIN region_view rv ON (o.district = rv.region_name or o.city = rv.region_name) ' \
    #                       'RIGHT JOIN agency_region_view arv ON rv.region_id = arv.region_id'
    #     where = where + ' and arv.agency_id = %s'
    #     param.append(top_agency)
    param = tuple(param)
    sql = select + where + ' GROUP BY mc.id ORDER BY count DESC'
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def agency_distribute(start, end, machine_category_id, model_id):
    """
    代理地区分布
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT count(DISTINCT(o.sn)) count, a.`name` as distribute FROM `order` o ' \
              'RIGHT JOIN rbcare.region r ON (o.district = r.region_name or o.city = r.region_name) ' \
              'RIGHT JOIN rbcare.agency_region ar ON r.region_id = ar.region_id ' \
              'RIGHT JOIN rbcare.agency a ON ar.agency_id = a.id  ' \
              'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s ' \
              'and o.model_id = %s and a.`level` = 1 AND a.deleted_at is NULL  GROUP BY a.id'
        param = tuple([start, end, model_id])
    elif machine_category_id:
        sql = 'SELECT count(DISTINCT(o.sn)) count, a.`name` as distribute FROM `order` o ' \
              'right join machine_type mt on o.model_id = mt.model_id ' \
              'RIGHT JOIN rbcare.region r ON (o.district = r.region_name or o.city = r.region_name) ' \
              'RIGHT JOIN rbcare.agency_region ar ON r.region_id = ar.region_id ' \
              'RIGHT JOIN rbcare.agency a ON ar.agency_id = a.id  ' \
              'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s ' \
              'and mt.category_id = %s and a.`level` = 1 AND a.deleted_at is NULL  GROUP BY a.id'
        param = tuple([start, end, machine_category_id])
    else:
        sql = 'SELECT count(DISTINCT(o.sn)) count, a.`name` as distribute FROM `order` o ' \
              'RIGHT JOIN rbcare.region r ON (o.district = r.region_name or o.city = r.region_name) ' \
              'RIGHT JOIN rbcare.agency_region ar ON r.region_id = ar.region_id ' \
              'RIGHT JOIN rbcare.agency a ON ar.agency_id = a.id ' \
              'WHERE o.`status` != -200 and o.`status` != -800 AND o.created_at BETWEEN %s and %s and a.`level` = 1 ' \
              'AND a.deleted_at is NULL  GROUP BY a.id'
        param = tuple([start, end])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def damage_distribute(start, end, machine_category_id, model_id):
    """
    设备故障分布
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT DISTINCT d.id, o.model_name, mc.`name` as category_name, d.title,  ' \
              'concat_ws(\'-\', o.model_name, d.title) as contact_title,  count(`do`.order_id) as count ' \
              'FROM damage d  LEFT JOIN damage_order `do` ON d.id = `do`.damage_id ' \
              'LEFT JOIN machine_category mc ON d.machine_category_id = mc.id ' \
              'RIGHT JOIN `order` o ON `do`.order_id = o.id ' \
              'WHERE d.visible = 1 AND d.machine_category_id = %s AND `do`.created_at BETWEEN %s and %s ' \
              'AND o.model_id = %s GROUP BY d.id ORDER BY count DESC'
        param = [machine_category_id, start, end, model_id]
    elif machine_category_id:
        sql = 'SELECT DISTINCT d.id, o.model_name, mc.`name` as category_name, d.title,  ' \
              'concat_ws(\'-\', o.model_name, d.title) as contact_title,  count(`do`.order_id) as count ' \
              'FROM damage d  LEFT JOIN damage_order `do` ON d.id = `do`.damage_id ' \
              'LEFT JOIN machine_category mc ON d.machine_category_id = mc.id ' \
              'RIGHT JOIN `order` o ON `do`.order_id = o.id ' \
              'WHERE d.visible = 1 AND d.machine_category_id = %s AND `do`.created_at BETWEEN %s and %s ' \
              'GROUP BY d.id, o.model_id ORDER BY count DESC'
        param = [machine_category_id, start, end]
    else:
        sql = 'SELECT DISTINCT d.id, o.model_name, mc.`name` as category_name, d.title,  ' \
              'concat_ws(\'-\', o.model_name, d.title) as contact_title,  count(`do`.order_id) as count ' \
              'FROM damage d  LEFT JOIN damage_order `do` ON d.id = `do`.damage_id ' \
              'LEFT JOIN machine_category mc ON d.machine_category_id = mc.id ' \
              'RIGHT JOIN `order` o ON `do`.order_id = o.id ' \
              'WHERE d.visible = 1 AND `do`.created_at BETWEEN %s and %s GROUP BY d.id, mc.id, o.model_id ' \
              'ORDER BY count  DESC, d.machine_category_id'
        param = [start, end]
    param = tuple(param)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


def num_group_count(data, group_step, group_count):
    # 计算-group
    group_len = group_count + 1
    group_value = [0] * group_len
    for di in data:
        minute = di
        if minute <= 0:
            minute = 1
        min_div = int(math.ceil(minute / group_step))
        if min_div > group_count:
            min_div = group_count
        # min_div -= 1
        group_value[min_div] += 1
    group_ret = [{'index': vi, 'value': vv} for vi, vv in enumerate(group_value)]
    # 计算-avg
    sum_minute = 0
    avg_hour = 0
    count = len(data)
    if count <= 0:
        avg_hour = 0
    else:
        for di in data:
            minute = di
            if minute == 0:
                minute = 1
            sum_minute += minute
        avg_hour = round((float(sum_minute)/count)/60, 2)
    # 计算-ratio
    for ri in group_ret:
        ratio = round(float(ri['value']) / count * 100, 2) if count else 0.00
        ri['ratio'] = ratio
    return avg_hour, group_ret


@gen.coroutine
def inform_time_consuming(start, end, machine_category_id, model_id):
    """
    客服知会耗时分布情况
    :param start:
    :param end:
    :param machine_category_id:
    :param model_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    sql_select = 'SELECT MIN(TIMESTAMPDIFF(minute, o.receive_time, ol2.date)) AS minute_diff '
    sql_from = 'FROM `order` o '
    sql_join = 'LEFT JOIN order_extend oe ON o.sn = oe.sn ' \
               'LEFT JOIN order_log2 ol2 ON o.sn = ol2.pr_sn '
    sql_where = 'WHERE oe.quality_time BETWEEN %s and %s ' \
                '  AND o.`status` != -900 ' \
                '  AND o.receive_time IS NOT NULL ' \
                '  AND ol2.pr_status = 410 ' \
                '  AND oe.order_mark = 0 '
    sql_order = 'GROUP BY o.id ORDER BY o.id DESC '
    if model_id:
        sql_where += 'AND o.model_id = %s '
        param = [start, end, model_id]
    elif machine_category_id:
        sql_join += 'RIGHT JOIN machine_type mt on o.model_id = mt.model_id '
        sql_where += 'AND mt.category_id = %s '
        param = [start, end, machine_category_id]
    else:
        param = [start, end]
    sql = sql_select + sql_from + sql_join + sql_where + sql_order
    param = tuple(param)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    # 计算
    group_name = [
        '0-2小时',
        '2-4小时',
        '4-6小时',
        '6-8小时',
        '8-10小时',
        '10-12小时',
        '12-14小时',
        '14-16小时',
        '16-18小时',
        '18-20小时',
        '20-22小时',
        '22-24小时',
        '>24小时',
    ]
    group_count = 12
    group_step = 120
    num_list = [di.get('minute_diff', 0) for di in data]
    avg_hour, group_ret = num_group_count(num_list, group_step, group_count)
    for index in range(len(group_ret)):
        group_ret[index]['name'] = group_name[index]
    # 返回
    ret = {
        'count': len(data),
        'avg_hour': avg_hour,
        'group': group_ret
    }
    raise gen.Return(ret)


@gen.coroutine
def pay_time_consuming(start, end, machine_category_id, model_id):
    """
    客户支付耗时分布情况
    :param start:
    :param end:
    :param machine_category_id:
    :param model_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    sql_select = 'SELECT TIMESTAMPDIFF(minute, ol2.date, pay_time) AS minute_diff '
    sql_from = 'FROM `order` o '
    sql_join = 'LEFT JOIN order_extend oe ON o.sn = oe.sn ' \
               'LEFT JOIN order_log2 ol2 ON o.sn = ol2.pr_sn '
    sql_where = 'WHERE oe.quality_time BETWEEN %s and %s ' \
                '  AND o.`status` != -900 ' \
                '  AND o.pay_time IS NOT NULL ' \
                '  AND ol2.pr_status = 500 ' \
                '  AND oe.order_mark = 0 '
    sql_order = 'ORDER BY o.id DESC '
    if model_id:
        sql_where += 'AND o.model_id = %s '
        param = [start, end, model_id]
    elif machine_category_id:
        sql_join += 'RIGHT JOIN machine_type mt on o.model_id = mt.model_id '
        sql_where += 'AND mt.category_id = %s '
        param = [start, end, machine_category_id]
    else:
        param = [start, end]
    sql = sql_select + sql_from + sql_join + sql_where + sql_order
    param = tuple(param)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    # 计算
    group_name = [
        '0-2小时',
        '2-4小时',
        '4-6小时',
        '6-8小时',
        '8-10小时',
        '10-12小时',
        '12-14小时',
        '14-16小时',
        '16-18小时',
        '18-20小时',
        '20-22小时',
        '22-24小时',
        '>24小时',
    ]
    group_count = 12
    group_step = 120
    num_list = [di.get('minute_diff', 0) for di in data]
    avg_hour, group_ret = num_group_count(num_list, group_step, group_count)
    for index in range(len(group_ret)):
        group_ret[index]['name'] = group_name[index]
    # 返回
    ret = {
        'count': len(data),
        'avg_hour': avg_hour,
        'group': group_ret
    }
    raise gen.Return(ret)


@gen.coroutine
def time_consuming(start, end, machine_category_id, model_id):
    """
    耗时分布情况
    :param start:
    :param end:
    :param machine_category_id:
    :param model_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    sql_select = 'SELECT SUM(IF(DATEDIFF(oe.quality_time,o.receive_time)=1 ' \
                 'OR DATEDIFF(oe.quality_time,o.receive_time)=0, 1, 0)) as one_day, ' \
                 'SUM(DATEDIFF(oe.quality_time,o.receive_time)=2) as two_day, ' \
                 'SUM(DATEDIFF(oe.quality_time,o.receive_time)=3) as three_day, ' \
                 'SUM(DATEDIFF(oe.quality_time,o.receive_time)=4) as four_day, ' \
                 'SUM(DATEDIFF(oe.quality_time,o.receive_time)=5) fine_day, ' \
                 'SUM(DATEDIFF(oe.quality_time,o.receive_time)=6) as six_day, ' \
                 'SUM(DATEDIFF(oe.quality_time,o.receive_time)=7) seven_day, ' \
                 'SUM(if(DATEDIFF(oe.quality_time,o.receive_time)>=8 ' \
                 'AND DATEDIFF(oe.quality_time,o.receive_time) < 15, 1, 0)) as eight_fourteen_day, ' \
                 'SUM(DATEDIFF(oe.quality_time,o.receive_time)>14) as more_days  '
    sql_from = 'FROM `order` o '
    sql_join = 'LEFT JOIN order_extend oe ON o.sn = oe.sn '
    sql_where = 'WHERE oe.quality_time BETWEEN %s and %s ' \
                '  AND o.receive_time IS NOT NULL ' \
                '  AND o.`status` != -900 ' \
                '  AND oe.order_mark = 0 '
    if model_id:
        sql_where += 'AND o.model_id = %s '
        param = [start, end, model_id]
    elif machine_category_id:
        sql_join += 'RIGHT JOIN machine_type mt on o.model_id = mt.model_id '
        sql_where += 'AND mt.category_id = %s '
        param = [start, end, machine_category_id]
    else:
        param = [start, end]
    sql = sql_select + sql_from + sql_join + sql_where
    param = tuple(param)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    raise gen.Return(data)

@gen.coroutine
def avg_time(start, end, machine_category_id, model_id):
    """
    平均耗时
    :param start:
    :param end:
    :param machine_category_id:
    :param model_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT DATEDIFF(o.updated_at_last,o.receive_time) as time_difference ' \
              'FROM `order` o ' \
              'WHERE o.updated_at_last BETWEEN %s and %s AND o.model_id = %s ' \
              'and o.receive_time IS NOT NULL AND o.`status` != -900'
        param = [start, end, model_id]
    elif machine_category_id:
        sql = 'SELECT DATEDIFF(o.updated_at_last,o.receive_time) as time_difference ' \
              'FROM `order` o right join machine_type mt on o.model_id = mt.model_id' \
              ' where o.updated_at_last BETWEEN %s and %s AND mt.category_id = %s ' \
              'and o.receive_time IS NOT NULL' \
              ' AND o.`status` != -900'
        param = [start, end, machine_category_id]
    else:
        sql = 'SELECT DATEDIFF(o.updated_at_last,o.receive_time) as time_difference ' \
              'FROM `order` o ' \
              'WHERE o.updated_at_last BETWEEN %s and %s AND o.receive_time IS NOT NULL AND o.`status` != -900'
        param = [start, end]
    param = tuple(param)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    count = len(data)
    sum_days = 0
    if count == 0:
        avg_time = 0
    else:
        for i in data:
            if i['time_difference'] == 0 :
                i['time_difference'] = 1
            sum_days += i['time_difference']
            avg_time = round(sum_days/count,2)
    raise gen.Return(avg_time)



@gen.coroutine
def repeat_order_proportion_by_model(start, end, machine_category_id, model_id):
    """
    二次维修机型分布情况
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT count(o.id) AS count, o.model_name FROM `order` o ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s AND o.`status` != -200 ' \
              'AND o.`status` != -900 AND o.model_id = %s and o.repair_man != 0 GROUP BY o.model_id'
        param = tuple([start, end, model_id])
    elif machine_category_id:
        sql = 'SELECT count(o.id) AS count, o.model_name FROM `order` o ' \
              'RIGHT JOIN machine_type mt ON o.model_id = mt.model_id ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s AND o.`status` != -200 ' \
              'AND o.`status` != -900 AND mt.category_id = %s and o.repair_man != 0 GROUP BY o.model_id'
        param = tuple([start, end, machine_category_id])
    else:
        sql = 'SELECT count(o.id) AS count, o.model_name FROM `order` o ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s AND o.`status` != -200 ' \
              'AND o.`status` != -900 and o.repair_man != 0 GROUP BY o.model_id'
        param = tuple([start, end])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def repeat_order_proportion_by_repair_man(start, end, machine_category_id, model_id):
    """
    二次维修维修人员分布情况
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT o.id, o.barcode FROM `order` o ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s ' \
              'AND o.`status` != -200 AND o.`status` != -900 AND o.model_id = %s '
        param = tuple([start, end, model_id])
    elif machine_category_id:
        sql = 'SELECT o.id, o.barcode FROM `order` o ' \
              'RIGHT JOIN machine_type mt ON o.model_id = mt.model_id ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s ' \
              'AND o.`status` != -200 AND o.`status` != -900 AND mt.category_id = %s'
        param = tuple([start, end, machine_category_id])
    else:
        sql = 'SELECT o.id, o.barcode FROM `order` o ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s ' \
              'AND o.`status` != -200 AND o.`status` != -900'
        param = tuple([start, end])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return([])
    data_page = dict()
    for i in data:
        sql = 'SELECT o.id, o.repair_man, au.`name` FROM `order` o ' \
              'LEFT JOIN admin_users au ON o.repair_man = au.id ' \
              'WHERE o.id < %s AND o.barcode = %s AND o.`status` != -200 AND o.`status` != -900 ' \
              ' ORDER BY id DESC LIMIT 1 '
        param = [i['id'], i['barcode']]
        cur = yield DB_PR.execute(sql, tuple(param))
        data2 = cur.fetchone()
        if not data2 or not data2.get('name'):
            continue
        if data_page.get(data2['name']):
            data_page[data2['name']] = data_page[data2['name']] + 1
        else:
            data_page[data2['name']] = 1
    ret = []
    for key, value in data_page.items():
        ret.append({'name': key, 'count': value})
    raise gen.Return(ret)


@gen.coroutine
def repeat_order_proportion_by_damage(start, end, machine_category_id, model_id):
    """
    设备故障分布
    :param start:
    :param end:
    :param model_id:
    :param machine_category_id:
    :return:
    """
    start = start + ' 00:00:00'
    end = end + ' 23:59:59'
    if model_id:
        sql = 'SELECT o.id, o.barcode FROM `order` o ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s ' \
              'AND o.`status` != -200 AND o.`status` != -900 AND o.model_id = %s '
        param = tuple([start, end, model_id])
    elif machine_category_id:
        sql = 'SELECT o.id, o.barcode FROM `order` o ' \
              'RIGHT JOIN machine_type mt ON o.model_id = mt.model_id ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s ' \
              'AND o.`status` != -200 AND o.`status` != -900 AND mt.category_id = %s'
        param = tuple([start, end, machine_category_id])
    else:
        sql = 'SELECT o.id, o.barcode FROM `order` o ' \
              'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s ' \
              'AND o.`status` != -200 AND o.`status` != -900'
        param = tuple([start, end])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return([])
    data_page = dict()
    for i in data:
        sql = 'SELECT DISTINCT d.id, o.model_name, mc.`name` as category_name, d.title, ' \
              'concat_ws(\'-\', o.model_name, d.title) as contact_title FROM damage d  ' \
              'RIGHT JOIN damage_order `do` ON d.id = `do`.damage_id ' \
              'LEFT JOIN machine_category mc ON d.machine_category_id = mc.id ' \
              'RIGHT JOIN `order` o ON `do`.order_id = o.id ' \
              'WHERE o.id < %s AND o.barcode = %s AND o.`status` != -200 AND o.`status` != -900 ' \
              'AND  d.visible = 1  ORDER BY id DESC LIMIT 1'
        param = [i['id'], i['barcode']]
        cur = yield DB_PR.execute(sql, tuple(param))
        data2 = cur.fetchone()
        if not data2:
            continue
        if data_page.get(data2['model_name']):
            data_page[data2['model_name']]['count'] = data_page[data2['model_name']]['count'] + 1
        else:
            ls = {
                'category_name': data2['category_name'],
                'model_name': data2['model_name'],
                'title': data2['title'],
                'contact_title': data2['contact_title'],
                'count': 1,
            }
            data_page[data2['model_name']] = ls
    ret = []
    for key, value in data_page.items():
        ret.append(value)
    raise gen.Return(ret)


@gen.coroutine
def machine_malfunction(model_id, start, end, mat_id):
    """
    故障原因
    :param model_id:
    :param start:
    :param end:
    :param mat_id:
    :return:
    """
    sql = 'SELECT mm.title, count(1) as count FROM machine_malfunction mm ' \
          'RIGHT JOIN pr_material pm ON mm.id = pm.malfunction_id ' \
          'RIGHT JOIN pr_used_material pum ON pm.pr_sn = pum.pr_sn ' \
          'RIGHT JOIN `order` o ON pum.pr_sn = o.sn ' \
          'WHERE o.model_id = %s AND pum.mat_id = %s AND pm.mat_id = %s ' \
          'AND pum.created_at BETWEEN %s and %s GROUP BY pm.malfunction_id'
    cur = yield DB_PR.execute(sql, (model_id, mat_id, mat_id, start, end))
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def accessory_damage_distribute(model_id, start, end, mat_id):
    """
    配件故障类型分布
    :param model_id:
    :param start:
    :param end:
    :param mat_id:
    :return:
    """
    sql = 'SELECT DISTINCT d.title, count(`do`.order_id) as count FROM damage d ' \
          'RIGHT JOIN damage_order `do` ON d.id = `do`.damage_id ' \
          'RIGHT JOIN `order` o ON `do`.order_id = o.id  ' \
          'RIGHT JOIN pr_used_material pum ON o.sn = pum.pr_sn ' \
          'WHERE  o.model_id = %s AND pum.mat_id = %s AND d.visible = 1 ' \
          'AND pum.created_at BETWEEN %s and %s GROUP BY d.id ORDER BY count DESC'
    cur = yield DB_PR.execute(sql, (model_id, mat_id, start, end))
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def screen_insurance_total(start, end, model_id, top_agency, second_agency):
    """
    碎屏保投保总数和已使用数量
    :param start:
    :param end:
    :param model_id:
    :param top_agency:
    :param second_agency:
    :return:
    """
    sql = 'SELECT count(1) as count, COALESCE(SUM(IF(`status`=400,1,0)),0) AS used_count  FROM broken_screen_insurance' \
          ' WHERE `status` IN (300, 400) '
    param = []
    if model_id:
        sql = sql + 'and model_id = %s '
        param.append(model_id)
    if top_agency and second_agency:
        sql = sql + 'and top_agency = %s and second_agency = %s '
        param.append(top_agency)
        param.append(second_agency)
    elif top_agency:
        sql = sql + 'and top_agency = %s '
        param.append(top_agency)
    if start and end:
        sql = sql + 'and created_at between %s and %s '
        param.append(start)
        param.append(end)

    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchone()
    raise gen.Return([data['count'], data['used_count']])


@gen.coroutine
def screen_insurance_agency_distribute(start, end, model_id, top_agency, second_agency):
    """
    碎屏保--代理商分布
    :param start:
    :param end:
    :param model_id:
    :param top_agency:
    :param second_agency:
    :return:
    """
    param = []
    if top_agency and second_agency:
        sql = 'SELECT count(1) AS count, a.`name` FROM broken_screen_insurance bsi ' \
              'RIGHT JOIN rbcare.agency a ON bsi.second_agency = a.id ' \
              'WHERE bsi.`status` IN (300, 400) AND a.deleted_at IS NULL ' \
              'AND bsi.top_agency = %s AND bsi.second_agency = %s '
        param.append(top_agency)
        param.append(second_agency)
        group = ' GROUP BY bsi.second_agency'
    elif top_agency:
        sql = 'SELECT count(1) AS count, a.`name` FROM broken_screen_insurance bsi ' \
              'RIGHT JOIN rbcare.agency a ON bsi.second_agency = a.id ' \
              'WHERE bsi.`status` IN (300, 400) AND a.deleted_at IS NULL ' \
              'AND bsi.top_agency = %s '
        param.append(top_agency)
        group = ' GROUP BY bsi.second_agency'
    else:
        sql = 'SELECT count(1) AS count, a.`name` FROM broken_screen_insurance bsi ' \
              'RIGHT JOIN rbcare.agency a ON bsi.top_agency = a.id ' \
              'WHERE bsi.`status` IN (300, 400) AND a.deleted_at IS NULL '
        group = ' GROUP BY bsi.top_agency'
    if model_id:
        sql = sql + 'and bsi.model_id = %s '
        param.append(model_id)
    if start and end:
        sql = sql + 'and bsi.created_at between %s and %s '
        param.append(start)
        param.append(end)
    sql = sql + group
    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def screen_insurance_model_distribute(start, end, model_id, top_agency, second_agency):
    """
    碎屏保机器分布
    :param start:
    :param end:
    :param model_id:
    :param top_agency:
    :param second_agency:
    :return:
    """
    sql = 'SELECT COUNT(1) AS count, model_name FROM broken_screen_insurance WHERE `status` in (300, 400) '
    param = []
    if model_id:
        sql = sql + 'and model_id = %s '
        param.append(model_id)
    if top_agency and second_agency:
        sql = sql + 'and top_agency = %s and second_agency = %s '
        param.append(top_agency)
        param.append(second_agency)
    elif top_agency:
        sql = sql + 'and top_agency = %s '
        param.append(top_agency)
    if start and end:
        sql = sql + 'and created_at between %s and %s '
        param.append(start)
        param.append(end)
    sql = sql + ' GROUP BY model_id'
    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def screen_insurance_used_model_distribute(start, end, model_id, top_agency, second_agency):
    """
    碎屏保机器分布
    :param start:
    :param end:
    :param model_id:
    :param top_agency:
    :param second_agency:
    :return:
    """
    sql = 'SELECT COUNT(bsi.id) AS count, bsi.model_id, bsi.model_name, bsis.`name`, bsis.`month` ' \
          'FROM broken_screen_insurance bsi ' \
          'LEFT JOIN broken_screen_insurance_standard bsis ON bsi.standard = bsis.id ' \
          'WHERE bsi.`status` = 400 '
    param = []
    if model_id:
        sql = sql + 'and bsi.model_id = %s '
        param.append(model_id)
    if top_agency and second_agency:
        sql = sql + 'and bsi.top_agency = %s and bsi.second_agency = %s '
        param.append(top_agency)
        param.append(second_agency)
    elif top_agency:
        sql = sql + 'and bsi.top_agency = %s '
        param.append(top_agency)
    if start and end:
        sql = sql + 'and bsi.created_at between %s and %s '
        param.append(start)
        param.append(end)
    sql = sql + ' GROUP BY bsi.model_id, bsi.standard'
    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchall()
    raise gen.Return(data)


def now_time():
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')


@gen.coroutine
def order_sign_stat(start_time, end_time):
    """
    七天维修量
    :return:
    """

    sql = 'SELECT count(*) AS order_sign FROM `order` WHERE receive_time BETWEEN %s AND %s '
    cur = yield DB_PR.execute(sql, [start_time, end_time])
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def exp_go_stat(start_time, end_time):
    """
    今日回寄量
    :return:
    """

    sql = 'SELECT count(*) AS exp_go_sum FROM `order` WHERE updated_at_last BETWEEN %s AND %s '
    cur = yield DB_PR.execute(sql, [start_time, end_time])
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def seven_day_exp_go_stat(start_time, end_time):
    """
    七日回寄情况
    :return:
    """

    sql = 'SELECT DATE_FORMAT(updated_at_last,\'%%m.%%d\') AS date, count(*) AS count ' \
          'FROM `order` WHERE updated_at_last BETWEEN %s AND %s GROUP BY date'
    cur = yield DB_PR.execute(sql, [start_time, end_time])
    data = cur.fetchall()
    raise gen.Return(data)


@cached(cache=TTLCache(maxsize=10240, ttl=300))
@gen.coroutine
def month_statistics_damage_distribute(month_start_time, month_end_time, repeat_order, limit):
    """
    月统计故障分布
    :param limit: 取多少条数据
    :param repeat_order:   是否二次维修订单
    :param month_start_time:  开始时间
    :param month_end_time: 结束时间
    :return:
    """
    sql = 'SELECT concat_ws(\'：\', o.model_name, d.title) as contact_title, count(`do`.order_id) as count ' \
          'FROM damage d ' \
          'LEFT JOIN damage_order `do` ON d.id = `do`.damage_id ' \
          'RIGHT JOIN `order` o ON `do`.order_id = o.id ' \
          'WHERE d.visible = 1 AND `do`.created_at ' \
          'BETWEEN %s AND %s '
    if repeat_order:
        sql = sql + ' AND o.repeat_order = 2 '
    sql = sql + 'GROUP BY d.id, o.model_id ORDER BY count DESC, contact_title limit %s'

    cur = yield DB_PR.execute(sql, [month_start_time, month_end_time, limit])
    data = cur.fetchall()
    raise gen.Return(data)


@cached(cache=TTLCache(maxsize=10240, ttl=300))
@gen.coroutine
def month_statistics_damage_accessory(month_start_time, month_end_time):
    """
    月统计配件损坏
    :param month_start_time:
    :param month_end_time:
    :return:
    """
    sql = 'SELECT concat_ws(\'-\',mt.name,mat.title) as title, sum(pum.count) as count ' \
          'FROM pr_used_material as pum ' \
          'LEFT JOIN machine_accessory_tree as mat ON pum.mat_id =mat.id ' \
          'LEFT JOIN machine_type as mt ON mat.model_id = mt.model_id ' \
          'LEFT JOIN `order` o on o.sn = pum.pr_sn ' \
          'WHERE pum.created_at BETWEEN %s AND %s ' \
          'GROUP BY pum.mat_id limit 10'

    cur = yield DB_PR.execute(sql, [month_start_time, month_end_time])
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def daily_statistics_exp_go():
    """
    日回寄分布
    :return:
    """
    sql = 'SELECT DATE_FORMAT(updated_at_last, \'%%H:00\') AS date, COUNT(*) as count ' \
          'FROM `order` WHERE updated_at_last BETWEEN %s AND %s GROUP BY date ORDER BY date'
    start_time = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
    end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    cur = yield DB_PR.execute(sql, [start_time, end_time])
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def repair_man_stat_connect(month_start_time, month_end_time):
    data = yield repair_man_stat(month_start_time, month_end_time)
    if data and len(data) > 0:
        to_check_list = yield repair_man_to_check_num()
        seven_day_not_quality = yield repair_man_seven_day_not_quality()
        for man in data:
            man['to_check_num'] = 0
            man['seven_day_not_quality'] = 0
            for check_item in to_check_list:
                if man.get('check_man') > 0 and check_item.get('check_man') > 0 \
                and man.get('check_man') == check_item.get('check_man') \
                and check_item.get('count') > 0:
                    man['to_check_num'] = check_item.get('count')
                    break
            for seven_item in seven_day_not_quality:
                if man.get('check_man') > 0 and seven_item.get('check_man') > 0 \
                and man.get('check_man') == seven_item.get('check_man') \
                and seven_item.get('count') > 0:
                    man['seven_day_not_quality'] = seven_item.get('count')
                    break
    raise gen.Return(data)


@gen.coroutine
def repair_man_to_check_num():
    """
    维修人员待检测数量
    """
    sql_base = 'SELECT o.check_man, au.`name`, count(*) AS count FROM `order` o '
    sql_join = 'LEFT JOIN admin_users au ON o.check_man = au.id '
    sql_where = 'WHERE o.status IN (410, 480) '
    sql_group = 'GROUP BY o.check_man '
    sql = sql_base + sql_join + sql_where + sql_group
    cur = yield DB_PR.execute(sql)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def repair_man_seven_day_not_quality():
    """
    已分派超7天未品检
    """
    sql = 'SELECT o.check_man, au.`name`, count(*) AS count ' \
          'FROM `order` o ' \
          '         LEFT JOIN order_extend oe ON o.sn = oe.sn ' \
          '         LEFT JOIN admin_users au ON o.check_man = au.id ' \
          'WHERE o.status IN (410, 480, 490, 500, 600, 700) ' \
          '  AND oe.allocation_time < date_sub(now(), interval 7 day) ' \
          '  AND oe.allocation_time is not null ' \
          '  AND oe.quality_time is null ' \
          'GROUP BY o.check_man '
    cur = yield DB_PR.execute(sql)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def repair_man_stat(month_start_time, month_end_time):
    """
    维修人员效率排名
    :param month_start_time:
    :param month_end_time:
    :return:
    """
    sql = 'SELECT o.check_man, au.`name`, count(*) AS month_stat, ' \
          '(SELECT count(*) as daily_stat FROM `order` o2 ' \
          'WHERE o2.updated_at_last BETWEEN %s AND %s AND o2.check_man = o.check_man) AS daily_stat ' \
          'FROM `order` o LEFT JOIN admin_users au ON o.check_man = au.id ' \
          'WHERE o.updated_at_last BETWEEN %s AND %s AND o.check_man != 0 ' \
          'GROUP BY o.check_man ORDER BY daily_stat DESC'
    daily_start_time = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
    daily_end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    cur = yield DB_PR.execute(sql, [daily_start_time, daily_end_time, month_start_time, month_end_time])
    data = cur.fetchall()
    raise gen.Return(data)


@cached(cache=TTLCache(maxsize=10240, ttl=300))
@gen.coroutine
def repeat_order_model_distribute(month_start_time, month_end_time):
    sql = 'SELECT o.model_name, count(*) as count ' \
          'FROM `order` o ' \
          'WHERE o.updated_at_last BETWEEN %s AND %s AND o.repeat_order = 2 ' \
          'GROUP BY o.model_id ORDER BY count DESC LIMIT 5'
    cur = yield DB_PR.execute(sql, [month_start_time, month_end_time])
    data = cur.fetchall()
    raise gen.Return(data)


@cached(cache=TTLCache(maxsize=10240, ttl=300))
@gen.coroutine
def repeat_order_repair_man_distribute(month_start_time, month_end_time):
    sql = 'SELECT o.id, o.barcode FROM `order` o ' \
          'WHERE o.repeat_order = 2 AND o.created_at BETWEEN %s and %s ' \
          'AND o.`status` != -200 AND o.`status` != -900'
    param = tuple([month_start_time, month_end_time])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return([])
    data_page = dict()
    detail = dict()
    for i in data:
        sql = 'SELECT o.id, o.sn, o.repair_man, au.`name`, o.created_at FROM `order` o ' \
              'LEFT JOIN admin_users au ON o.repair_man = au.id ' \
              'WHERE o.id < %s AND o.barcode = %s AND o.`status` != -200 AND o.`status` != -900 ' \
              ' ORDER BY id DESC LIMIT 1 '
        param = [i['id'], i['barcode']]
        cur = yield DB_PR.execute(sql, tuple(param))
        data2 = cur.fetchone()
        if not data2 or not data2.get('name'):
            continue
        if data_page.get(data2['name']):
            data_page[data2['name']] = data_page[data2['name']] + 1
            detail[data2['name']].append({'sn': data2['sn'], 'barcode': i['barcode'],
                                      'created_at': data2['created_at'].strftime('%Y-%m-%d %H:%M:%S')})
        else:
            data_page[data2['name']] = 1
            detail[data2['name']] = [{'sn': data2['sn'], 'barcode': i['barcode'],
                                      'created_at': data2['created_at'].strftime('%Y-%m-%d %H:%M:%S')}]
    ret = []
    for key, value in data_page.items():
        detail_1 = detail.get(key)
        if detail_1:
            detail_1 = sorted(detail_1, key=itemgetter('created_at'), reverse=True)
        ret.append({'name': key, 'count': value, 'detail': detail_1})
    if ret:
        ret = sorted(ret, key=itemgetter('count'), reverse=True)
    raise gen.Return(ret)

@gen.coroutine
def repair_daily_users_record_list():
    now = datetime.datetime.now().strftime('%Y-%m-%d')
    sql = 'select director, repair_man, quantity_man, group_leader, material_man, repair_man_num, absentee_num ' \
          'from repair_daily_users where date = %s'
    cur = yield DB_PR.execute(sql, [now])
    data = cur.fetchone()
    ret = {'director': '', 'repair_man': '', 'quantity_man': '', 'group_leader': '', 'material_man': '',
           'repair_man_num': 0, 'absentee_num': 0}
    if data:
        ret = {'director': data['director'], 'repair_man': data['repair_man'], 'quantity_man': data['quantity_man'],
               'group_leader': data['group_leader'], 'material_man': data['material_man'],
               'repair_man_num': data['repair_man_num'], 'absentee_num': data['absentee_num']}

    raise gen.Return(ret)


@gen.coroutine
def repair_daily_users_record(director, repair_man, quantity_man, group_leader, material_man, repair_man_num,
                              absentee_num):
    now = datetime.datetime.now().strftime('%Y-%m-%d')
    sql = 'select date from repair_daily_users where date = %s'
    cur = yield DB_PR.execute(sql, [now])
    data = cur.fetchone()
    key = ['director', 'repair_man', 'quantity_man', 'group_leader', 'material_man', 'repair_man_num', 'absentee_num']
    param = [director, repair_man, quantity_man, group_leader, material_man, repair_man_num, absentee_num, now]
    ret = None
    try:
        if data:
            field = '=%s,'.join(i for i in key) + '=%s'
            sql = 'update repair_daily_users set ' + field + 'where date = %s'
            yield DB_PR.execute(sql, param)
        else:
            field = ','.join(i for i in key) + ',date'
            sql = 'insert into repair_daily_users (' + field + ') values (%s,%s,%s,%s,%s,%s,%s,%s)'
            yield DB_PR.execute(sql, param)
        ret = True
    except Exception as e:
        app_log.error('record error :%s' % e, exc_info=True)
    raise gen.Return(ret)