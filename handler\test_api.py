# encoding=utf-8
# __author__ = 'lch'
import datetime
from tornado import gen, auth
from tornado.log import app_log
import tornado.web
import util.com as com
from handler import ApiHandler, tokenauth
import json
from store import repair_model, warranty_model, region_model, sf_model, user_model, alipay_model
import cache.user_cache as auth_cache
import time


class TEST_CreateMail(ApiHandler):
    @gen.coroutine
    def get(self):
        sn = repair_model._unique_sn()
        info = {
            'orderid': sn,
            'custid': '',
            'pay_method': '1',
            'is_docall': '1',
            'j_contact' : "寄修部门",
            'j_tel' : "137707651572",
            'j_province' : "广东省",
            'j_city' : "中山市",
            'j_county' : "五桂山镇",
            'j_address' : "广东省 中山市 五桂山镇 长命水工业区长逸路38号读书郎教育科技有限公司",
            'd_contact' : "寄修部门",
            'd_tel' : "18898489001",
            'd_province': '广东省',
            'd_city': '珠海市',
            'd_county': '香洲区',
            'd_address' : "唐家湾镇科技六路39号读书郎网络教育有限公司",
        }
        data = yield sf_model.create_order(info)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class TEST_MailRoute(ApiHandler):
    @gen.coroutine
    def get(self):
        info = {
            'tracking_number': '201907181143236176950831'
        }
        data = yield sf_model.mail_route(info)
        if not data:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        for i in data:
            i['com'] = '顺丰'
            if com.safeInt(i.get('opcode', 0)) in [50,54,43,46]:
                i['status'] = 1
            elif com.safeInt(i.get('opcode', 0)) in [30,31,130,123,607,36,77]:
                i['status'] = 2
            elif com.safeInt(i.get('opcode', 0)) in [44,204,125,47,126,658,657,630,664,34]:
                i['status'] = 3
            elif com.safeInt(i.get('opcode', 0)) in [80]:
                i['status'] = 4
            else:
                i['status'] = 0
            yield repair_model.store_exp_route(i)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class TEST_Test(ApiHandler):
    @gen.coroutine
    def get(self):
        appid = self.appauth.get('appid')
        if not appid:
            raise gen.Return(self.error(u'应用不可支付', com.ERROR_BAD_STATE))
        data = yield alipay_model.query_pay(appid, '')
        raise gen.Return(self.success(data))

class TEST_MES(ApiHandler):
    @gen.coroutine
    def post(self):
        # 获取参数
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        if not content.get('barcode'):
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        barcode = content['barcode']
        # 查询数据
        machine = yield warranty_model.checkMESV1(None, barcode, None)
        # 返回
        imei = machine['imei1']
        ret = {'content': content, 'machine': machine, 'imei': imei or u''}
        raise gen.Return(self.success(ret))

class TEST_AR_REPEAT(ApiHandler):
    @gen.coroutine
    def get(self):
        # 获取参数
        barcode = self.get_argument('barcode', '')
        if not barcode:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        # 查询数据
        data = yield repair_model.ar_repeat(barcode)
        # 返回
        ret = {'barcode': barcode, 'data': data}
        raise gen.Return(self.success(ret))
