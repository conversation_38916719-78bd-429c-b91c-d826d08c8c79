# encoding=utf-8
# __author__ = 'qry'
import json
import random

from tornado import gen
from tornado.log import app_log
from cache import agent_cache
from store import DB_PR, repair_model
from conf import config
import datetime


def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')


def _changeTS(data, fields):
    """
    转换时间戳
    :param data: 数据
    :param fields: 需要转换时间戳的字段
    :return:
    """
    for i in fields:
        if data.get(i):
            data[i] = data[i].strftime('%Y-%m-%d %H:%M:%S')
    return data


def _unique_sn(sn=None, t=0):
    """
    生成唯一流水号字符串
    :param sn: 已有流水号生成关联号
    :param t: 关联类型，1寄来快递单，2寄去快递单
    :return:
    """
    if sn and t:
        s = sn + '%04d' % t
        return s
    else:
        ts = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        rand = random.randint(100000, 999999)
        s = ts + str(rand)
        return s


def _image_to_str(data, fields):
    """
    数据中的文件数组转换成字符串
    :param data: 数据
    :param fields: 要转换的字段
    :return:
    """
    for i in fields:
        if data.get(i) and isinstance(data[i], list):
            rets = list()
            for img in data[i]:
                if img.startswith(config.OSS_CNAME):
                    ret = img[len(config.OSS_CNAME):]
                    rets.append(ret)
            s = json.dumps(rets)
            data[i] = s
        else:
            data[i] = ''
    return data


@gen.coroutine
def check_order_cache(uid, barcode, agent_order_sn, endpoint_id):
    """
    查看是否有保存的订单  即缓存
    :param endpoint_id:
    :param agent_order_sn:
    :param uid:
    :param barcode: 条码
    :return:
    """
    sql = 'select count(*) as count from order_cache aoc right join agent_order_correlation_cache aocc ' \
          'on aoc.sn = aocc.order_sn where  aoc.barcode = %s and aocc.agent_order_sn = %s ' \
          'and aoc.submit_status = 0 and aoc.type = 2'
    param = [barcode, agent_order_sn]
    if endpoint_id:
        sql = sql + " AND aoc.endpoint = %s"
        param.append(endpoint_id)
    else:
        sql = sql + " AND aoc.uid = %s"
        param.append(uid)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    # print data
    if data and data['count'] > 0:
        raise gen.Return(True)
    raise gen.Return(False)


@gen.coroutine
def modify_agent_repair(uid, sn, data, endpoint_id):
    data = _image_to_str(data, ['period_file', 'upload_file', 'video_file'])
    key = ['name', 'phone', 'damage', 'period_file', 'upload_file', 'video_file',
           'description', 'attachment', 'is_user_address', 'updated_at']
    if data.get('is_user_address') == 1:
        key.extend(['province', 'city', 'district', 'address'])
    if data.get('external_fault'):
        key.append('external_fault')
    if data.get('internal_fault'):
        key.append('internal_fault')
    data['updated_at'] = _now_date()
    field = '=%s,'.join(key) + '=%s'
    sql = 'update order_cache set ' + field + ' where  sn = %s '
    if endpoint_id:
        sql = sql + " and endpoint = %s "
        param = tuple([data.get(i) for i in key] + [sn, endpoint_id])
    else:
        sql = sql + "and uid = %s "
        param = tuple([data.get(i) for i in key] + [sn, uid])
    # print sql

    # print param
    ret = None
    try:
        yield DB_PR.execute(sql, param)
        ret = True
    except:
        app_log.error('update order_cache error', exc_info=True)
    raise gen.Return(ret)

@gen.coroutine
def check_order_cache_by_sn(uid, sn, endpoint_id):
    """
    查看是否有保存的订单  即缓存
    :param endpoint_id:
    :param uid:
    :param sn: 单号
    :return:
    """
    sql = 'select count(*) as count from order_cache where sn = %s and submit_status = 0 and type = 2 '
    if endpoint_id:
        sql = sql + " and endpoint = %s"
        param = [sn, endpoint_id]
    else:
        sql = sql + " and uid = %s"
        param = [sn, uid]
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    if data and data['count'] > 0:
        raise gen.Return(True)
    raise gen.Return(False)


@gen.coroutine
def save_repair(uid, data, agent_order_sn, endpoint_id):
    """
    提交订单缓存
    :param endpoint_id:
    :param agent_order_sn:
    :param uid: 用户id
    :param data: 订单数据
    :return:
    """

    if not uid or not isinstance(data, dict):
        raise gen.Return(False)
    created_at = _now_date()
    # 将数据预处理
    data['uid'] = uid
    data['endpoint'] = endpoint_id
    data['sn'] = _unique_sn()
    data['created_at'] = created_at
    data['status'] = 100
    # data['come_exp_type'] = 2
    data['type'] = 2
    data = _image_to_str(data, ['period_file', 'upload_file', 'video_file'])
    # 多了省份地址信息
    key = [
        'barcode',
        'imei',
        'model_name',
        'color',
        'model_id',
        'serial',
        'in_period',
        'has_warranty',
        'in_si_period',
        'has_screen_insurance',
        'reason',
        'damage',
        'period_file',
        'upload_file',
        'video_file',
        'description',
        'name',
        'phone',
        'repair_endpoint',
        'uid',
        'endpoint',
        'created_at',
        'sn',
        'status',
        'repeat_order',
        'ar_repeat',
        'attachment',
        'type',
        'is_user_address',
    ]
    if data.get('is_user_address') == 1:
        key.extend(['province', 'city', 'district', 'address'])
    if data.get('external_fault'):
        key.append('external_fault')
    if data.get('internal_fault'):
        key.append('internal_fault')
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    param_insert = tuple([data.get(i) for i in key])
    sql = 'insert into `order_cache` (' + fields + ') values (' + values + ')'
    ret = None
    trans = yield DB_PR.begin()
    try:
        if not agent_order_sn:
            agent_order_sn = _unique_sn()
            param = tuple([uid, endpoint_id, agent_order_sn, 2, created_at])
            # 大单处理
            yield trans.execute(
                'insert into agent_order (uid, endpoint, sn, type, created_at) values(%s,%s,%s,%s,%s)', param)
        # 插入大单-小单关联表
        yield trans.execute('insert into agent_order_correlation_cache (agent_order_sn, order_sn, created_at) '
                            'values(%s,%s,%s)', (agent_order_sn, data['sn'], created_at))
        # 小单缓存
        yield trans.execute(sql, param_insert)
        trans.commit()
        ret = {
            'agent_order_sn': agent_order_sn,
            'order_sn': data['sn']
        }
    except Exception as e:
        trans.rollback()
        app_log.info('add order_agent insert error:', exc_info=True)
        raise gen.Return(ret)
    raise gen.Return(ret)


@gen.coroutine
def get_order(uid, agent_order_sn, endpoint_id):
    """
    获取缓存数据
    :param endpoint_id:
    :param agent_order_sn:
    :param uid:
    :return:
    """
    sql = 'select aoc.uid, aoc.endpoint, aoc.sn, aoc.`status`, aoc.barcode, aoc.imei, aoc.model_name, aoc.color, ' \
          'aoc.model_id, aoc.serial,  aoc.in_period, aoc.has_warranty, aoc.in_si_period, aoc.has_screen_insurance, ' \
          'aoc.reason, aoc.damage, aoc.period_file, aoc.upload_file, aoc.video_file, aoc.description, ' \
          'aoc.repair_endpoint, aoc.repeat_order, aoc.ar_repeat, aoc.created_at, ' \
          'aoc.attachment, aoc.come_exp_type, aoc.name, aoc.phone, aoc.province, aoc.city, aoc.district, aoc.address, ' \
          'aoc.is_user_address, aoc.external_fault, aoc.internal_fault ' \
          'from order_cache aoc RIGHT JOIN agent_order_correlation_cache ao ' \
          'ON aoc.sn = ao.order_sn WHERE ao.agent_order_sn = %s and aoc.submit_status = 0 and ' \
          'aoc.type = 2 '
    if endpoint_id:
        sql = sql + " and aoc.endpoint = %s"
        param = [agent_order_sn, endpoint_id]
    else:
        sql = sql + " and aoc.uid = %s"
        param = [agent_order_sn, uid]
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)


@gen.coroutine
def add_agent_order(datas):
    """
    提交订单
    :param datas:
    :return:
    """
    trans = yield DB_PR.begin()
    # 大单流水号
    sn = datas['agent_order_sn']
    # datas['type'] = 2
    created_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    key = [
        'barcode', 'imei', 'model_name', 'color', 'model_id', 'serial', 'in_period', 'has_warranty', 'in_si_period',
        'has_screen_insurance', 'reason', 'damage', 'period_file', 'upload_file', 'video_file', 'description',
        'repair_endpoint', 'created_at', 'sn', 'status', 'repeat_order', 'ar_repeat', 'type', 'attachment',
        'come_exp_type', 'name', 'phone', 'province', 'city', 'district', 'address', 'agency', 'endpoint'
    ]
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    # print values
    sql = 'insert into `order` (' + fields + ') values (' + values + ')'
    # print sql
    ret = None
    try:
        now = _now_date()
        # 更新大单信息
        param_agent_order = tuple([datas['province'], datas['city'], datas['district'], datas['address'],
                                   datas['agency'], datas['name'], datas['phone'], datas['come_exp_type'], created_at, sn])
        yield trans.execute('update agent_order set status = 1, province = %s, city = %s, district = %s, address = %s, '
                            'agency = %s, name = %s, phone = %s, come_exp_type = %s, updated_at = %s where sn = %s '
                            'and type = 2', param_agent_order)
        # 提交所有小单
        for data in datas['order']:
            data['type'] = 3  # 终端代寄
            data['come_exp_type'] = datas['come_exp_type']
            # 如果以代寄人为回寄地址则存代寄人地址
            if data['is_user_address'] == 0:
                for i in ['province', 'city', 'district', 'address']:
                    data[i] = datas[i]
            param = tuple([data.get(i) for i in key])
            yield trans.execute("insert into order_bug_log (sn, barcode, repeat_order, created_at) "
                                "values (%s,%s,%s,%s)", (data['sn'], data['barcode'], data['repeat_order'], now))
            # 提交小单
            yield trans.execute(sql, param)
            yield trans.execute('insert into order_extend (sn, is_user_address, external_fault, '
                                'internal_fault, created_at , is_tell) values(%s,%s,%s,%s,%s,%s)',
                                (data['sn'], data['is_user_address'], data['external_fault'], data['internal_fault'],
                                 created_at ,datas.get('is_tell' , 0) ))
            # 更新缓存状态
            yield trans.execute('update order_cache set submit_status = 1, updated_at = %s where sn = %s and '
                                'submit_status = 0 and type = 2',
                                (created_at, data.get('sn')))
            # 插入大单-小单关联表
            yield trans.execute('insert into agent_order_correlation (agent_order_sn, order_sn, phone, created_at) '
                                'values(%s,%s,%s,%s)', (sn, data.get('sn'), data['phone'], created_at))
            log = {
                'pr_sn': data.get('sn', ''),
                'pr_status': data.get('status', 0),
                'log_status': data.get('status', 0),
                'log_from': data.get('log_from', 'web'),
                'relation_key': data.get('relation_key', ''),
                'operation': data.get('operation', 'create_order'),
                'uid': data.get('uid', 0),
                'admin': data.get('admin', 0),
                'title': data.get('title', '寄修订单提交，寄修服务中心等待接收中'),
                'remark': data.get('remark', ''),
                'date': data.get('date', _now_date()),
            }
            repair_model.add_log(log)
            ret = True
        trans.commit()
    except:
        ret = None
        app_log.error('add agent_order error', exc_info=True)
        trans.rollback()
    raise gen.Return(ret)


@gen.coroutine
def change_express_check_order(uid, sn, endpoint_id):
    """
    检查用户是否有权限操作订单
    :param endpoint_id:
    :param uid: 用户id
    :param sn: 订单流水号
    :return:
    """
    sql = 'SELECT sum(o.`status` = 200 or `status` = -300) as count, ' \
          'sum(o.`status` = 100) as check_count FROM agent_order_correlation aoc  LEFT JOIN `order` o ' \
          'ON aoc.order_sn = o.sn WHERE  aoc.agent_order_sn = %s '

    if endpoint_id:
        sql = sql + " and (o.uid = %s or o.endpoint = %s)"
        param = [sn, uid, endpoint_id]
    else:
        sql = sql + " and o.uid = %s"
        param = [sn, uid]
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    # 有待审核订单
    if data.get('check_count') > 0:
        raise gen.Return(-1)

    if data.get('count') > 0:
        raise gen.Return(1)
    else:
        raise gen.Return(0)


@gen.coroutine
def get_order_sn(uid, sn, endpoint_id):
    """
    获取子订单流水号
    :param endpoint_id:
    :param uid: 用户id
    :param sn: 大单流水号
    :return:
    """
    sql = 'SELECT o.sn FROM `order` o LEFT JOIN agent_order_correlation aoc ON o.sn = aoc.order_sn ' \
          'WHERE aoc.agent_order_sn = %s and (o.`status` = 200 or o.`status` = -300) '
    if endpoint_id:
        sql = sql + " and (o.uid= %s or o.endpoint = %s)"
        param = [sn, uid, endpoint_id]
    else:
        sql = sql + " and o.uid= %s "
        param = [sn, uid]
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    raise gen.Return(data)

@gen.coroutine
def change_order(sn, data, order_sn):
    """
    用户更新自己订单数据
    :param order_sn: 子订单流水号
    :param sn: 大订单流水号
    :param data: 修改的数据
    :return:
    """
    data['updated_at'] = _now_date()
    data['sn'] = sn
    data['status'] = 300
    created_at = _now_date()
    key_total = ['come_exp_type', 'come_exp_com', 'come_exp_sn', 'updated_at', 'sn']
    sql_total = 'update `agent_order` set come_exp_type=%s, come_exp_com=%s, come_exp_sn=%s, updated_at=%s where sn=%s'

    sql_order = 'update `order` set come_exp_type=%s, come_exp_com=%s, come_exp_sn=%s, updated_at=%s, status=%s ' \
                'where sn=%s'

    param = tuple([data.get(i) for i in key_total])
    trans = yield DB_PR.begin()
    try:
        # 大单处理
        cur = yield trans.execute(sql_total, param)
        # 小单处理
        for i in order_sn:
            # print i
            param = tuple([data.get('come_exp_type'), data.get('come_exp_com'), data.get('come_exp_sn'),
                           data.get('updated_at'), 300, i.get('sn')])
            # print param
            yield trans.execute(sql_order, param)
            # 添加日志
            # param = tuple([i.get('sn'), 300, 300, 'app', '', 'exp_come_sure', data.get('uid'), 0,
            #                '寄修产品发货成功，维修中心等待接收中', '', created_at])
            # yield trans.execute('insert into order_log (pr_sn, pr_status, log_status, log_from, relation_key, '
            #                     'operation, uid, admin, title, remark, date) values '
            #                     '(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)', param)
            log = {
                'pr_sn': i.get('sn', ''),
                'pr_status': 300,
                'log_status': 300,
                'log_from':  'app',
                'relation_key': '',
                'operation': 'exp_come_sure',
                'uid': data.get('uid', 0),
                'admin': data.get('admin', 0),
                'title': data.get('title', '寄修产品发货成功，维修中心等待接收中'),
                'remark': data.get('remark', ''),
                'date': data.get('date', _now_date()),
            }
            repair_model.add_log(log)
        trans.commit()
    except:
        trans.rollback()
        app_log.error('update order error', exc_info=True)
        raise gen.Return(False)
    raise gen.Return(True)


@gen.coroutine
def total_order(uid, order_sn, order_type, barcode, status, start, end, phone, endpoint_id):
    """
    大单获取
    :param endpoint_id:
    :param phone:
    :param uid: 用户id
    :param order_sn: 订单流水号
    :param order_type: 类型：1--大单  2--小单
    :param barcode: 条码
    :param status: 子订单状态
    :param start: 起始时间
    :param end: 结束时间
    :return:
    """
    param = []
    sql = 'SELECT distinct ao.id, ao.sn as agent_order_sn, ao.created_at, ao.come_exp_type FROM agent_order ao ' \
          'RIGHT JOIN agent_order_correlation aoc ON ao.sn = aoc.agent_order_sn RIGHT JOIN `order` o ON ' \
          'aoc.order_sn = o.sn WHERE  ao.status = 1 and ao.type = 2 '

    # sn或者status
    if barcode and status:
        sql = sql + ' and o.barcode = %s and o.status = %s '
        param.append(barcode)
        param.append(status)
    elif barcode:
        sql = sql + ' and o.barcode = %s '
        param.append(barcode)
    elif status:
        sql = sql + ' and o.status = %s '
        param.append(status)
    else:
        sql = 'SELECT distinct ao.id, ao.sn as agent_order_sn, ao.created_at, ao.come_exp_type FROM agent_order ao ' \
              'RIGHT JOIN agent_order_correlation aoc ON ao.sn = aoc.agent_order_sn WHERE ao.type =2 '
    if endpoint_id:
        sql = sql + " and (ao.endpoint = %s or ao.uid = %s) "
        param.append(endpoint_id)
        param.append(uid)
    else:
        sql = sql + " and ao.uid = %s "
        param.append(uid)
    if order_sn and order_type == 1:
        sql = sql + ' and ao.sn = %s '
        param.append(order_sn)
    if order_sn and order_type == 2:
        sql = sql + ' and aoc.order_sn = %s '
        param.append(order_sn)

    if start and end:
        sql = sql + '  AND ao.created_at BETWEEN %s AND %s '
        param.append(start)
        param.append(end)

    if phone:
        sql = sql + ' AND ao.phone = %s '
        param.append(phone)
    sql = sql + ' order by created_at desc '
    cur = yield DB_PR.execute(sql, tuple(param))
    data = cur.fetchall()
    # print data
    if not data:
        raise gen.Return([])
    for i in data:
        sql = 'select count(*) as count, sum(o.`status` = 100) AS wait_audit, sum(o.`status` = 200) AS audit_pass, ' \
              'sum(o.`status` = -200) as aidot_no_pass, sum(o.`status` = 400) AS come_sure, sum(o.`status` = 500) ' \
              'AS no_pay, sum(o.`status` = 600) AS pay_finish, sum(o.`status` = 700) AS repair_finish, ' \
              'sum(o.`status` = 800) AS exp_go, sum(o.`status` = 900) AS order_finish from agent_order_correlation ' \
              'aoc LEFT JOIN `order` o ON aoc.order_sn = o.sn where agent_order_sn =  %s'
        cur2 = yield DB_PR.execute(sql, i['agent_order_sn'])
        data2 = cur2.fetchone()
        i.update(data2)
        i = _changeTS(i, ['created_at'])
        i['sub_order'] = yield sub_order(i['agent_order_sn'])
    raise gen.Return(data)


@gen.coroutine
def sub_order(order_sn):
    """
    小单获取
    :param order_sn:    大单单号
    :return:
    """
    sql = 'SELECT aoc.agent_order_sn, o.id, o.phone, o.model_name, o.sn as order_sn, o.barcode, o.`status`, ' \
          'o.created_at FROM `order` o RIGHT JOIN agent_order_correlation aoc ON o.sn = aoc.order_sn ' \
          'WHERE o.type = 3 and aoc.agent_order_sn = %s'
    cur = yield DB_PR.execute(sql, order_sn)
    data = cur.fetchall()
    for i in data:
        if i.get('status') in [410, 480, 490]:
            i['status'] = 400
        i = _changeTS(i, ['created_at'])
    raise gen.Return(data)


@gen.coroutine
def check_order_type(uid, order_sn, endpoint_id):
    """
    判断order_sn订单类型
    :param endpoint_id:
    :param uid: 用户id
    :param order_sn: 订单流水号
    :return: 1 -- 大单  0 -- 小单
    """
    sql = 'select count(*) as count from agent_order where sn = %s and (uid = %s or endpoint = %s)'
    cur = yield DB_PR.execute(sql, (order_sn, uid, endpoint_id))
    data = cur.fetchone()
    if data.get('count') > 0:
        raise gen.Return(1)
    else:
        raise gen.Return(2)


@gen.coroutine
def repair_history(phone, date=None, number=10):
    """
    个人寄修历史列表
    :param phone: 用户id
    :param date: 小于日期时间
    :param number: 多少条
    :return:
    """
    if not date:
        date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    sql = 'select DISTINCT o.model_name, o.sn, o.created_at, o.`status`, o.appraise from `order` o RIGHT JOIN ' \
          'agent_order_correlation aoc ON o.phone = aoc.phone where aoc.phone= %s and o.created_at < %s ' \
          'order by o.created_at desc limit %s'
    param = tuple([phone, date, number+1])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    l = len(data)
    is_end = l < number + 1
    size = l if is_end else number
    data = data[0:size]
    for i in data:
        if i.get('status') in [410, 480, 490]:
            i['status'] = 400
        i = _changeTS(i, ['created_at'])
    ret = {
        'data': data,
        'is_end': is_end,
        'size': size
    }
    raise gen.Return(ret)


@gen.coroutine
def check_order(sn):
    """
    检查用户是否有权限操作订单
    :param sn: 订单流水号
    :return:
    """
    ret = 0

    sql = 'select o.status from `order` o RIGHT JOIN agent_order_correlation aoc ON o.sn = aoc.order_sn right join ' \
          'agent_order ao on aoc.agent_order_sn = ao.sn where o.sn=%s'
    param = [sn]

    try:
        cur = yield DB_PR.execute(sql, param)
        data = cur.fetchone()
        if data.get('status'):
            ret = data['status']
    except:
        ret = 0
    raise gen.Return(ret)


@gen.coroutine
def cancel_agent_repair(uid, sn, endpoint_id):
    sql = 'delete from order_cache where sn = %s'
    sql2 = 'delete from agent_order_correlation_cache where order_sn = %s'
    if endpoint_id:
        sql = sql + ' and (uid = %s or endpoint = %s)'
        param = [sn, uid, endpoint_id]
    else:
        sql = sql + ' and uid = %s '
        param = [sn, uid]
    ret = None
    trans = yield DB_PR.begin()
    try:
        yield trans.execute(sql, param)
        yield trans.execute(sql2, sn)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        app_log.error('delete order_cache error')
    raise gen.Return(ret)