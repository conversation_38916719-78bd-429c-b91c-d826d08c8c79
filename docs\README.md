# ReadboyCare API简介

## HOST
+ 测试：`http://**************:8000`
+ 正式：`https://api-repair-hub.readboy.com`

## 应用授权
+ 提交`app_id`,`app_name`,`app_description`给API负责人
+ 获得`app_secret`
+ 后台网页自行申请（未开放）

## API签名
**签名参数（作为Query参数添加到所有请求中）**
+ `device_id`: 设备信息，依次包含如下字段，用`/`连接
    + `model`: 机型
    + `unique_id`: 设备唯一id（可是mac地址、设备id）
    + `app_id`: 一般是应用包名，务必与申请应用授权时的应用id保持一致
    + `app_version`: 应用版本，最好是version code
+ `t`: 机器时间戳，单位：秒
+ `sn`: 签名值，算法：`md5( device_id + app_secret + t )`

<p class="danger">
**注意**：`device_id`可能出现非ASCII字符，请先计算`sn`值，再`urlencode(device_id)`
</p>

## 返回格式
**成功**
```json
{
    "ok": 1,
    "msg": "success or custom message",
    "data": json object or array
}

```
**失败**
```json
{
    "ok": 0,
    "msg": "未知错误",
    "errno": 7000  // 错误码
}

```

## 错误码
```json
FETAL_UNAUTH = 6003  # 未授权的机型

ERROR_UNKNOWN = 7000  # 未知错误
ERROR_PARAM = 7004  # 参数错误
ERROR_TOOFAST = 7005  # 频繁调用
ERROR_ALREADY_EXISTS = 7006  # 已经存在
ERROR_NOT_PERMIT = 7010  # 权限不足
ERROR_EMPTY_DATA = 7013  # 空数据
ERROR_BAD_TOKEN = 7200  # token无效
```