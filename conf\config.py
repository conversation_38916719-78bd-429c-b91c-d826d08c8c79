# encoding=utf-8
# @description：web app配置信息
# __author__ = 'lch'

import platform
import tornado_mysql

"""服务器基本配置模块"""

# 基本配置
DEBUG = platform.system() == 'Windows' or True

APP_NAME = 'rbcare'
APP_DATE = '20170316'

FILE_HOST = 'https://file1.readboy.com'
FILE_HOST_HTTP = 'http://file1.readboy.com'


REDIS_HOST = '**************' if DEBUG else '127.0.0.1'  # docker环境下运行不能使用127.0.0.1，除非docker内部自己有redis
REDIS_PORT = 6379
REDIS_DB = 3
REDIS_PASS = '' if DEBUG else 'ILq38ppdVT96TDtj'
REDIS_URI = 'redis://:'+REDIS_PASS+'@'+REDIS_HOST+':'+str(REDIS_PORT)+'/'+str(REDIS_DB)  # 'redis://127.0.0.1:6379/0'

MYSQL_DB = {
    # 'host': 'rds30hy45pn1373h5qlto.mysql.rds.aliyuncs.com',
    # 'port': 3306,
    # 'user': 'rbcare',
    # 'passwd': '9ZS0sebU2IfMTp3U',
    # 'db': 'rbcare',
    # 'charset': 'utf8mb4',
    # 'autocommit': True,
    # 'cursorclass': tornado_mysql.cursors.DictCursor
	
    # 'host': '**************' if DEBUG else 'rds30hy45pn1373h5qlt.mysql.rds.aliyuncs.com',
    'host': 'rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com' if DEBUG else 'rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',  ## 'rdsoi13i3919783uz8x3.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'yxpy' if DEBUG else 'yxpy',
    'passwd': '83GhKv75FDn3PA2u' if DEBUG else '83GhKv75FDn3PA2u',
    'db': 'rbcare_test' if DEBUG else 'rbcare',
    'charset': 'utf8mb4',
    'autocommit': True,
    'cursorclass': tornado_mysql.cursors.DictCursor
}
MYSQL_DB1 = {
    # 'host': '**************' if DEBUG else 'rdscrm503u2dsur95s82.mysql.rds.aliyuncs.com',
    'host': 'rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com' if DEBUG else 'rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',  ## 'rdsoi13i3919783uz8x3.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'yxrepair' if DEBUG else 'yxrepair',
    'passwd': 'clC30J3DQLN3w7ma' if DEBUG else 'clC30J3DQLN3w7ma',
    'db': 'post_repair_test' if DEBUG else 'post_repair',
    'charset': 'utf8mb4',
    'autocommit': True,
    'cursorclass': tornado_mysql.cursors.DictCursor
}


MYSQL_DB2 = {
    # 'host': '**************' if DEBUG else 'rdscrm503u2dsur95s82.mysql.rds.aliyuncs.com',
    'host': 'rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com' if DEBUG else 'rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',  ## 'rdsoi13i3919783uz8x3.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'yxrepair' if DEBUG else 'yxrepair',
    'passwd': 'clC30J3DQLN3w7ma' if DEBUG else 'clC30J3DQLN3w7ma',
    'db': 'rbcare_data' if DEBUG else 'rbcare_data',
    'charset': 'utf8mb4',
    'autocommit': True,
    'cursorclass': tornado_mysql.cursors.DictCursor
}

OSS_ENDPOINT = 'http://oss-cn-shenzhen.aliyuncs.com' # 假设Bucket处于杭州区域
OSS_BUCKET = 'publicdatas'
OSS_CNAME = 'https://dt.readboy.com/'

OSS_ACCESS_ID = 'LTAIv1JhaYLZMqqZ'
OSS_ACCESS_KEY = 'wRYJxuSOSvUAO3tAqH7cYd6JC6JLed'

SF_HOST = 'https://dkh-ms.sf-express.com'
SF_HEAD = 'swt'
SF_CUSTID = '7604923706'
SF_CHECKCODE = 'dsljkdj7b8na8bah66b'
SF_VERIFYCODE = 'needVerifyCode'

LOG_DIR = r'logs' if DEBUG else r'/data/log/post_repair'
API_LOG_DIR = r'logs' if DEBUG else r'/data/userlog'

ORDER_CREATED = 100
ORDER_PASS = 200
ORDER_COME_SUCCESS = 300
ORDER_COME_FAIL = -300
ORDER_RECEIVE = 400
ORDER_CHECK = 500
ORDER_PAY = 600
ORDER_REPAIRED = 700
ORDER_REPAIR_ABANDON = -700
ORDER_GO_SUCCESS = 800
ORDER_GO_FAIL = -800
ORDER_FINISH = 900
ORDER_CANCEL = -900

APP_SECRET = {
    'sf_web': '15b2e8f1d5178a57fb569550cf4d280c',
    'com.readboy.rbmanager': 'c1931fd0714859df1411e42685781902',
    'com.readboy.ParentAssistant': 'e64dbbedd199825ec50e7355fec7ef43',
    'com.readboy.smartwatch.ios': 'bc86e2007886bb434e836ab2326feaa4',
    'com.readboy.smartwatch.android': 'a659257bd844d5091b8e389491348911',
    'postrepair_h5': 'cdea87bca1f9496c0c4c9f65ba88a988',        # 寄修h5
    'postrepair_agency': '1ec9df10c27957037adbf4c735315926',    # 代理商寄修
    'screen_insurance_h5': 'c10eb100ff2850cbcde0d78e532de8c6',  # 碎屏保
    'yx.readboy.com': '710eb100ff2120cbcde0d78e532de8cg',       # 终端服务器
    'postrepair_endpoint': 'e5cea1a83aa90dec8f0fcfb1b668e09e',  # 寄修服务终端版
}

WXPAY_NOTIFY_URL = "https://pay-repair-hub.readboy.com/callback/wxpay"
AR_WXPAY_NOTIFY_URL = "https://pay-repair-hub.readboy.com/callback/ar_wxpay"
WXREFUND_NOTIFY_URL = "https://pay-repair-hub.readboy.com/callback/wxrefund"

WXPAY_APPID = {
    'com.readboy.rbmanager': 'wx4219220e3ccac9bf',
    'com.readboy.ParentAssistant': 'wx4219220e3ccac9bf',
    'com.readboy.smartwatch.ios': 'wx5dbfddae75400ec7',
    'com.readboy.smartwatch.android': 'wx5dbfddae75400ec7',
    'postrepair_h5': 'wx25e116fe3f5d5287',
}

WXPAY_APPNAME = {
    'com.readboy.rbmanager': '读书郎家长助手',
    'com.readboy.ParentAssistant': '读书郎家长助手',
    'com.readboy.smartwatch.ios': '读书郎电话手表',
    'com.readboy.smartwatch.android': '读书郎电话手表',
}

ALIPAY_NOTIFY_URL = "https://pay-repair-hub.readboy.com/callback/alipay"
AR_ALIPAY_NOTIFY_URL = "https://pay-repair-hub.readboy.com/callback/ar_alipay"

ALIPAY_APPID = {
    'com.readboy.rbmanager': '2019081266189270',
    'com.readboy.ParentAssistant': '2019081266189270',
    'com.readboy.smartwatch.ios': '2019082066307833',
    'com.readboy.smartwatch.android': '2019082066307833',
    'postrepair_h5': '2019082066307833',
}

ALIPAY_APPNAME = {
    'com.readboy.rbmanager': '读书郎家长助手',
    'com.readboy.ParentAssistant': '读书郎家长助手',
    'com.readboy.smartwatch.ios': '读书郎电话手表',
    'com.readboy.smartwatch.android': '读书郎电话手表',
}

ALIPAY_PRIVATE_KEY = {
    'com.readboy.rbmanager': 'MIIEogIBAAKCAQEAvVXluypItzBRx3k8wUTxdK0/1SoNvYosn3R4RD7Je5qYhPmnoBXJcHOCyPByujh0eE9xp63egXSem/7W9JMHLQsl8pGjTZywo3jpk2yYm9QjSnDJJEJItNdyLt7u0JA+xcPallhpASuANxaKNkoRkKV4yN55s+JI0dQ11a6eGUlqioGjFya5ZCvdYEzSIRjM2FjZhzAskhLA/l57rmtvINDbKhju3ChOy2/jAok+40P6JxE0It2syce/v0elLma/Qg/71uuCNu7B7H4QKeMpbFWSQdlE8HNLapXhNwji0IrdN8TYYLfPYyIaoa3XwEzCpu8rmnvq+r+aRVH6ToX7pQIDAQABAoIBAHGln7HK3b7nyA693RP9eBma7JfdGEZPLB3CYXZFYddJooce+9kBetp+22AVxCe3P2H//a6ylL6L67CjNQ+1OTd2W57QgH9TsEtPhpSxOKFAorZKINIc31t9ftJXim2yR5uZxhk8lDrZi6qFYwzXRNfYGzonIFIzX5ZjlLTgjZGLEUF98O5Z4Xefd/kQPdnfAJfSmfJt19AX/tsOf5rxuR8gC8dF1z5WHIJO0JkwK9z8GV5F8vHAxb0GNLtwDxRii+SrZ1bKbCslPSUpq6EKRb93raANeLdbHcvaLBHDqYN+fwqshwQdRmUnE3cHdKuSUr79jqSmsnfh8ctujZ5UqwECgYEA52eIXDzeFQ2BVo9kyazy5PO+aFqv67jK1sK1ZQbeJbexY3kSL12KvsVvInU1Edk/XliSB199sJGwOCS7QhCBnojpw8/qDvD+SxjvlzigzJbs2gElvbmjjMyFel9OrpMN3xWmlZ8vbqbX1eWsLTtK5bC4z3mWV5Nz8LnCbHtH+kUCgYEA0XWtyN/qBWkg/tc0Vh2rDOdG1SbGjEX/9aqvpNn3BgLnxMyYcGluO6r6Q7v76WCmFqTCxNmiFK0nH62SXvLvATR8420MSS8nN9RHs9hz15ISMwC13a7d3zBOLGC+BbPdAP0yQSNNdC2AZUJbyWX/hK7IJdcDOwnDoJuN7uoRweECgYAHtiONL1oXSYPt5oltTx4pExZdU+hY6qeX0JVK8JemmiCHOBmrCmr9QIcbltxV4++ArWLwa/Y8VB6TQGYdJX+sUNO5esxKAy32QrZ5S6HQL5dlDHS9hdo1bvtjwJlWp//mV0pd4hgqj9TRg/3g5ADy9piyEVM5R+itNQONqe4UIQKBgAxpB4nuUmRNrUnMIGq1yUuzbEv2xy7KevboTQ3caDlv9KGBacrHRGO+pqNawPs74btXub6GEoddFwNWemSryiNtI7Up4d94Lr+tES6DtaFhIzjoYt4mnTi7GPaiKMAXvxFu7wpS+CTZCVF2lA+jvnbzECLF0Ygu9OtQLCGqtrChAoGAVCJQmwDomjetKXP9j0aH6QZCKTEleOIKW3D79xrQwbrfoCZxKfPHt0LTVZiZItAdS3tJQkHZcY/LayzAKijS/ogQCJliCJ9pSbLfDYt2wz3ALJvZ6N8DMpu/8/ojxggv7edjRhLBat4SjASPlaEXtvJ0v/erpcccTZMEOhn3q/w=',
    'com.readboy.ParentAssistant': 'MIIEogIBAAKCAQEAvVXluypItzBRx3k8wUTxdK0/1SoNvYosn3R4RD7Je5qYhPmnoBXJcHOCyPByujh0eE9xp63egXSem/7W9JMHLQsl8pGjTZywo3jpk2yYm9QjSnDJJEJItNdyLt7u0JA+xcPallhpASuANxaKNkoRkKV4yN55s+JI0dQ11a6eGUlqioGjFya5ZCvdYEzSIRjM2FjZhzAskhLA/l57rmtvINDbKhju3ChOy2/jAok+40P6JxE0It2syce/v0elLma/Qg/71uuCNu7B7H4QKeMpbFWSQdlE8HNLapXhNwji0IrdN8TYYLfPYyIaoa3XwEzCpu8rmnvq+r+aRVH6ToX7pQIDAQABAoIBAHGln7HK3b7nyA693RP9eBma7JfdGEZPLB3CYXZFYddJooce+9kBetp+22AVxCe3P2H//a6ylL6L67CjNQ+1OTd2W57QgH9TsEtPhpSxOKFAorZKINIc31t9ftJXim2yR5uZxhk8lDrZi6qFYwzXRNfYGzonIFIzX5ZjlLTgjZGLEUF98O5Z4Xefd/kQPdnfAJfSmfJt19AX/tsOf5rxuR8gC8dF1z5WHIJO0JkwK9z8GV5F8vHAxb0GNLtwDxRii+SrZ1bKbCslPSUpq6EKRb93raANeLdbHcvaLBHDqYN+fwqshwQdRmUnE3cHdKuSUr79jqSmsnfh8ctujZ5UqwECgYEA52eIXDzeFQ2BVo9kyazy5PO+aFqv67jK1sK1ZQbeJbexY3kSL12KvsVvInU1Edk/XliSB199sJGwOCS7QhCBnojpw8/qDvD+SxjvlzigzJbs2gElvbmjjMyFel9OrpMN3xWmlZ8vbqbX1eWsLTtK5bC4z3mWV5Nz8LnCbHtH+kUCgYEA0XWtyN/qBWkg/tc0Vh2rDOdG1SbGjEX/9aqvpNn3BgLnxMyYcGluO6r6Q7v76WCmFqTCxNmiFK0nH62SXvLvATR8420MSS8nN9RHs9hz15ISMwC13a7d3zBOLGC+BbPdAP0yQSNNdC2AZUJbyWX/hK7IJdcDOwnDoJuN7uoRweECgYAHtiONL1oXSYPt5oltTx4pExZdU+hY6qeX0JVK8JemmiCHOBmrCmr9QIcbltxV4++ArWLwa/Y8VB6TQGYdJX+sUNO5esxKAy32QrZ5S6HQL5dlDHS9hdo1bvtjwJlWp//mV0pd4hgqj9TRg/3g5ADy9piyEVM5R+itNQONqe4UIQKBgAxpB4nuUmRNrUnMIGq1yUuzbEv2xy7KevboTQ3caDlv9KGBacrHRGO+pqNawPs74btXub6GEoddFwNWemSryiNtI7Up4d94Lr+tES6DtaFhIzjoYt4mnTi7GPaiKMAXvxFu7wpS+CTZCVF2lA+jvnbzECLF0Ygu9OtQLCGqtrChAoGAVCJQmwDomjetKXP9j0aH6QZCKTEleOIKW3D79xrQwbrfoCZxKfPHt0LTVZiZItAdS3tJQkHZcY/LayzAKijS/ogQCJliCJ9pSbLfDYt2wz3ALJvZ6N8DMpu/8/ojxggv7edjRhLBat4SjASPlaEXtvJ0v/erpcccTZMEOhn3q/w=',
    'com.readboy.smartwatch.ios': 'MIIEogIBAAKCAQEAvXK/oNNAME3PmSXdEucipgNZjlehoYelGo2U5eHISVJx7kyqhs/ROSqca+pDT4WEVyh4sSoudDC6BQ2Sozdf2MiAh9JLbQDHt+PUM8QIH97tfjZGop6rL9wYeJGLWwxpPzzfoAyBvaLYTcFEOSfBA6XV9EL2bGQhojFs1XC8oHlvdLvDn7P3wffIO7gjWlo+vXGB3oz89T44vg/Ja1E6BpyHza7rnB9PXGmncC/o49/dssJctH7GGgEu5yoGZOi3V65+UP6D5IqwLAehbMaPWmWQ/VZwKrVMW+tzVDAzfFf/T5h2PVnaCfgw94xzroLUUJaGZ/fM/1cr4juB0yt/DwIDAQABAoIBADMa1ES/F+6v3tOSWjuyLC+acbsrOofHfqtWDBejO5ND8UX2Wn35a5g/PWkDDdtSyit02HH6TgO77UJVsfvjgTCKjdmVyhECh928UM8SD5sBWRGV9KIDqWxEtDk6Edyi0XVkp3KWTR1p422+N/gvGCsyvi+hLLZ6oHQPXyuIuOmUwbCINXroPJKM57jNGKj65OVpONbFHPn8ZjGhtNZAWaL5IlFEPOqxMQ9VDjH+Xh8vHjtAtcBsHIdpbAwg/yzVXJTc+tXKjr6zPWmfekNmk44e2yh+4EXdwmDpKOs40kqwhLO/3zc1IL0agfmcrjl4w60DP7+/TeDIu7or15iJpikCgYEA4+tnqYpERbNIoX1Qcy9HLqqS6IuzENxWkbetxaN0LY/Dohu7Ly7H/iv++GFdNEOollImsO3cDp7jqZomR/Nmqkbp9OsQufdICHIGjNJAy9YTEDzAs9ePGv+gv4U9O6OBing96KX0qsK0C6IjkFR//artQBd7Jmtm57w7qxzFzt0CgYEA1Mn0zkqVbLaqQMT3obc1U0meUQmNR+bl/8K/AGDgMUw/wx/dfczWj32uytD0U9hPS76/E14AdsKbJYEv4/NCdhp9jtExxEpIddB+dDSRZGYut7q/sFpq/GVR32dNPxGpaqDth715UDQelReRlJ6A+cA/mYicxhYJXi+Io5OVKNsCgYAzQhb/LyRplIeKIhe8v+4/VYdSm1B7Q0KI5QlEwJz5PjLeO2RemSPLSvO9wo/mmCzLGdCvy97Ivv0ugIeJePkm5Gnrdg499JoTpClK9GI0NYH2DeO8zobVqJXrXLjHt98DWEV8FA6xdLZvQ1jCNe7PCujumBlPK6pqBZBZzE/EVQKBgELecFr/nzN5t8kA3NzqCQzyXpkSyl4lFnQoTAOfbfSwBH2aHu2wmGChW3OK9K0AlfK0r7EzYivv/fdsQJN+14zryDdAUEZiHaFAcuyrtGD7p4S/YAvYbjSA1pjBCspeD4JdridxCJWGu3GY5Xvxz5AstQ/XmYS6WhlKEMNu1nnvAoGAU4WgUk/yrcel1KQVSFeE6VcOg3afvxPd2B4El/Wc/dRV/3a3BmfiJ8ce0kRi9dVfsymCwgtZXEVxttTkUiMfc96xKbjajfktx1PB4f3j5oQ+guWS4W4AZjO3mOlUMo28KHm0h9opPwquV/NiSPIA8qie5sHvV53uKS5n2PAHQCU=',
    'com.readboy.smartwatch.android': 'MIIEogIBAAKCAQEAvXK/oNNAME3PmSXdEucipgNZjlehoYelGo2U5eHISVJx7kyqhs/ROSqca+pDT4WEVyh4sSoudDC6BQ2Sozdf2MiAh9JLbQDHt+PUM8QIH97tfjZGop6rL9wYeJGLWwxpPzzfoAyBvaLYTcFEOSfBA6XV9EL2bGQhojFs1XC8oHlvdLvDn7P3wffIO7gjWlo+vXGB3oz89T44vg/Ja1E6BpyHza7rnB9PXGmncC/o49/dssJctH7GGgEu5yoGZOi3V65+UP6D5IqwLAehbMaPWmWQ/VZwKrVMW+tzVDAzfFf/T5h2PVnaCfgw94xzroLUUJaGZ/fM/1cr4juB0yt/DwIDAQABAoIBADMa1ES/F+6v3tOSWjuyLC+acbsrOofHfqtWDBejO5ND8UX2Wn35a5g/PWkDDdtSyit02HH6TgO77UJVsfvjgTCKjdmVyhECh928UM8SD5sBWRGV9KIDqWxEtDk6Edyi0XVkp3KWTR1p422+N/gvGCsyvi+hLLZ6oHQPXyuIuOmUwbCINXroPJKM57jNGKj65OVpONbFHPn8ZjGhtNZAWaL5IlFEPOqxMQ9VDjH+Xh8vHjtAtcBsHIdpbAwg/yzVXJTc+tXKjr6zPWmfekNmk44e2yh+4EXdwmDpKOs40kqwhLO/3zc1IL0agfmcrjl4w60DP7+/TeDIu7or15iJpikCgYEA4+tnqYpERbNIoX1Qcy9HLqqS6IuzENxWkbetxaN0LY/Dohu7Ly7H/iv++GFdNEOollImsO3cDp7jqZomR/Nmqkbp9OsQufdICHIGjNJAy9YTEDzAs9ePGv+gv4U9O6OBing96KX0qsK0C6IjkFR//artQBd7Jmtm57w7qxzFzt0CgYEA1Mn0zkqVbLaqQMT3obc1U0meUQmNR+bl/8K/AGDgMUw/wx/dfczWj32uytD0U9hPS76/E14AdsKbJYEv4/NCdhp9jtExxEpIddB+dDSRZGYut7q/sFpq/GVR32dNPxGpaqDth715UDQelReRlJ6A+cA/mYicxhYJXi+Io5OVKNsCgYAzQhb/LyRplIeKIhe8v+4/VYdSm1B7Q0KI5QlEwJz5PjLeO2RemSPLSvO9wo/mmCzLGdCvy97Ivv0ugIeJePkm5Gnrdg499JoTpClK9GI0NYH2DeO8zobVqJXrXLjHt98DWEV8FA6xdLZvQ1jCNe7PCujumBlPK6pqBZBZzE/EVQKBgELecFr/nzN5t8kA3NzqCQzyXpkSyl4lFnQoTAOfbfSwBH2aHu2wmGChW3OK9K0AlfK0r7EzYivv/fdsQJN+14zryDdAUEZiHaFAcuyrtGD7p4S/YAvYbjSA1pjBCspeD4JdridxCJWGu3GY5Xvxz5AstQ/XmYS6WhlKEMNu1nnvAoGAU4WgUk/yrcel1KQVSFeE6VcOg3afvxPd2B4El/Wc/dRV/3a3BmfiJ8ce0kRi9dVfsymCwgtZXEVxttTkUiMfc96xKbjajfktx1PB4f3j5oQ+guWS4W4AZjO3mOlUMo28KHm0h9opPwquV/NiSPIA8qie5sHvV53uKS5n2PAHQCU=',
    'postrepair_h5': 'MIIEogIBAAKCAQEAvXK/oNNAME3PmSXdEucipgNZjlehoYelGo2U5eHISVJx7kyqhs/ROSqca+pDT4WEVyh4sSoudDC6BQ2Sozdf2MiAh9JLbQDHt+PUM8QIH97tfjZGop6rL9wYeJGLWwxpPzzfoAyBvaLYTcFEOSfBA6XV9EL2bGQhojFs1XC8oHlvdLvDn7P3wffIO7gjWlo+vXGB3oz89T44vg/Ja1E6BpyHza7rnB9PXGmncC/o49/dssJctH7GGgEu5yoGZOi3V65+UP6D5IqwLAehbMaPWmWQ/VZwKrVMW+tzVDAzfFf/T5h2PVnaCfgw94xzroLUUJaGZ/fM/1cr4juB0yt/DwIDAQABAoIBADMa1ES/F+6v3tOSWjuyLC+acbsrOofHfqtWDBejO5ND8UX2Wn35a5g/PWkDDdtSyit02HH6TgO77UJVsfvjgTCKjdmVyhECh928UM8SD5sBWRGV9KIDqWxEtDk6Edyi0XVkp3KWTR1p422+N/gvGCsyvi+hLLZ6oHQPXyuIuOmUwbCINXroPJKM57jNGKj65OVpONbFHPn8ZjGhtNZAWaL5IlFEPOqxMQ9VDjH+Xh8vHjtAtcBsHIdpbAwg/yzVXJTc+tXKjr6zPWmfekNmk44e2yh+4EXdwmDpKOs40kqwhLO/3zc1IL0agfmcrjl4w60DP7+/TeDIu7or15iJpikCgYEA4+tnqYpERbNIoX1Qcy9HLqqS6IuzENxWkbetxaN0LY/Dohu7Ly7H/iv++GFdNEOollImsO3cDp7jqZomR/Nmqkbp9OsQufdICHIGjNJAy9YTEDzAs9ePGv+gv4U9O6OBing96KX0qsK0C6IjkFR//artQBd7Jmtm57w7qxzFzt0CgYEA1Mn0zkqVbLaqQMT3obc1U0meUQmNR+bl/8K/AGDgMUw/wx/dfczWj32uytD0U9hPS76/E14AdsKbJYEv4/NCdhp9jtExxEpIddB+dDSRZGYut7q/sFpq/GVR32dNPxGpaqDth715UDQelReRlJ6A+cA/mYicxhYJXi+Io5OVKNsCgYAzQhb/LyRplIeKIhe8v+4/VYdSm1B7Q0KI5QlEwJz5PjLeO2RemSPLSvO9wo/mmCzLGdCvy97Ivv0ugIeJePkm5Gnrdg499JoTpClK9GI0NYH2DeO8zobVqJXrXLjHt98DWEV8FA6xdLZvQ1jCNe7PCujumBlPK6pqBZBZzE/EVQKBgELecFr/nzN5t8kA3NzqCQzyXpkSyl4lFnQoTAOfbfSwBH2aHu2wmGChW3OK9K0AlfK0r7EzYivv/fdsQJN+14zryDdAUEZiHaFAcuyrtGD7p4S/YAvYbjSA1pjBCspeD4JdridxCJWGu3GY5Xvxz5AstQ/XmYS6WhlKEMNu1nnvAoGAU4WgUk/yrcel1KQVSFeE6VcOg3afvxPd2B4El/Wc/dRV/3a3BmfiJ8ce0kRi9dVfsymCwgtZXEVxttTkUiMfc96xKbjajfktx1PB4f3j5oQ+guWS4W4AZjO3mOlUMo28KHm0h9opPwquV/NiSPIA8qie5sHvV53uKS5n2PAHQCU=',

}

ALIPAY_PUBLIC_KEY = {
    'com.readboy.rbmanager': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApZTNI/zqPbaI0Hz79vRADStXzV/SpRzSD5Nx+iRTul3aopHspcbJGDrqj/xDb+rIObkoCri+Nu2YEM81Gry34oBogNP2g9EuvKqfHBq+TReAm8h9J4L4YAKmK1hT0zeuwBUYn9IXQJzxMh2bv+C6qKzTg7xe+xgyTyyj0NrsItkvWjJYkSBp7fE7feB8wzq9E1XILhZytUsPq9uNfrdTGaLyiLTqUZb0uxPgfH/kcMXNqdI0UBgu2epCdSgHHT51A0bm8ZNizsFBdJuACqkR0gvNbWfsb9WZSOc3F3rwwp8xqnu6y602BmIDHogt9n79C3H02AEI39kezfiIKVG+LQIDAQAB',
    'com.readboy.ParentAssistant': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApZTNI/zqPbaI0Hz79vRADStXzV/SpRzSD5Nx+iRTul3aopHspcbJGDrqj/xDb+rIObkoCri+Nu2YEM81Gry34oBogNP2g9EuvKqfHBq+TReAm8h9J4L4YAKmK1hT0zeuwBUYn9IXQJzxMh2bv+C6qKzTg7xe+xgyTyyj0NrsItkvWjJYkSBp7fE7feB8wzq9E1XILhZytUsPq9uNfrdTGaLyiLTqUZb0uxPgfH/kcMXNqdI0UBgu2epCdSgHHT51A0bm8ZNizsFBdJuACqkR0gvNbWfsb9WZSOc3F3rwwp8xqnu6y602BmIDHogt9n79C3H02AEI39kezfiIKVG+LQIDAQAB',
    'com.readboy.smartwatch.ios': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApZTNI/zqPbaI0Hz79vRADStXzV/SpRzSD5Nx+iRTul3aopHspcbJGDrqj/xDb+rIObkoCri+Nu2YEM81Gry34oBogNP2g9EuvKqfHBq+TReAm8h9J4L4YAKmK1hT0zeuwBUYn9IXQJzxMh2bv+C6qKzTg7xe+xgyTyyj0NrsItkvWjJYkSBp7fE7feB8wzq9E1XILhZytUsPq9uNfrdTGaLyiLTqUZb0uxPgfH/kcMXNqdI0UBgu2epCdSgHHT51A0bm8ZNizsFBdJuACqkR0gvNbWfsb9WZSOc3F3rwwp8xqnu6y602BmIDHogt9n79C3H02AEI39kezfiIKVG+LQIDAQAB',
    'com.readboy.smartwatch.android': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApZTNI/zqPbaI0Hz79vRADStXzV/SpRzSD5Nx+iRTul3aopHspcbJGDrqj/xDb+rIObkoCri+Nu2YEM81Gry34oBogNP2g9EuvKqfHBq+TReAm8h9J4L4YAKmK1hT0zeuwBUYn9IXQJzxMh2bv+C6qKzTg7xe+xgyTyyj0NrsItkvWjJYkSBp7fE7feB8wzq9E1XILhZytUsPq9uNfrdTGaLyiLTqUZb0uxPgfH/kcMXNqdI0UBgu2epCdSgHHT51A0bm8ZNizsFBdJuACqkR0gvNbWfsb9WZSOc3F3rwwp8xqnu6y602BmIDHogt9n79C3H02AEI39kezfiIKVG+LQIDAQAB',
    'postrepair_h5': 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApZTNI/zqPbaI0Hz79vRADStXzV/SpRzSD5Nx+iRTul3aopHspcbJGDrqj/xDb+rIObkoCri+Nu2YEM81Gry34oBogNP2g9EuvKqfHBq+TReAm8h9J4L4YAKmK1hT0zeuwBUYn9IXQJzxMh2bv+C6qKzTg7xe+xgyTyyj0NrsItkvWjJYkSBp7fE7feB8wzq9E1XILhZytUsPq9uNfrdTGaLyiLTqUZb0uxPgfH/kcMXNqdI0UBgu2epCdSgHHT51A0bm8ZNizsFBdJuACqkR0gvNbWfsb9WZSOc3F3rwwp8xqnu6y602BmIDHogt9n79C3H02AEI39kezfiIKVG+LQIDAQAB',
}


# yx_host = 'http://localhost:8020'
yx_host = 'http://api-yxtest.readboy.com/api'
# yx_host = 'https://api-yx.readboy.com/api'
