#!/usr/bin/env python
#coding: utf-8
import sys
import urllib,urllib2
import base64
import hmac
from hashlib import sha1,md5
import time
import uuid
import json
import random
access_key_id = 'LTAIHj3XuFGVdxkq'
access_key_secret = 'CAUbhWmIe1sMlmgVRzvR6XG4Q6PHaq'
# server_address = 'https://sms.aliyuncs.com'
server_address = 'https://api-sms.readboy.com/index.php?s=/Sms/Api/send'
#定义参数
# user_params = {'Action': 'SingleSendSms', 'ParamString': json.dumps({"barcode": "测试"}), 'RecNum': '15819341131','SignName': u'读书郎'.encode('utf-8'),'TemplateCode': 'SMS_56225243' }

def percent_encode(encodeStr):
    encodeStr = str(encodeStr)
    res = urllib.quote(encodeStr.decode('utf8').encode('utf8'), '')
    # res = urllib.quote(encodeStr.encode('utf8'), '')
    res = res.replace('+', '%20')
    res = res.replace('*', '%2A')
    res = res.replace('%7E', '~')
    return res

def compute_signature(parameters, secret):
    sortedParameters = sorted(parameters.items(), key=lambda kv: kv[0])
    canonicalizedQueryString = ''
    for (k,v) in sortedParameters:
        canonicalizedQueryString += '&' + percent_encode(k) + '=' + percent_encode(v)
    stringToSign = 'GET&%2F&' + percent_encode(canonicalizedQueryString[1:])
    # print "stringToSign:  "+stringToSign
    h = hmac.new(secret + "&", stringToSign, sha1)
    signature = base64.encodestring(h.digest()).strip()
    return signature

def compose_url(ParamString={"barcode": "哈哈哈"}, RecNum='15819311131', Action='SingleSendSms',
                SignName=u'读书郎'.encode('utf-8'), TemplateCode='SMS_56415001'):
    timestamp = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime(time.time()))
    parameters = {
        # user params
        'Action': Action,
        'ParamString': json.dumps(ParamString),
        'RecNum': RecNum,
        'SignName': SignName,
        'TemplateCode': TemplateCode,
        # common params
        'Format'        : 'JSON',
        'Version'       : '2016-09-27',
        'AccessKeyId'   : access_key_id,
        'SignatureVersion'  : '1.0',
        'SignatureMethod'   : 'HMAC-SHA1',
        'SignatureNonce'    : str(uuid.uuid1()),
        # 'RegionId': 'cn-hangzhou',
        'Timestamp'     : timestamp,
    }
    # for key in user_params.keys():
    #     parameters[key] = user_params[key]
    signature = compute_signature(parameters, access_key_secret)
    parameters['Signature'] = signature
    url = server_address + "/?" + urllib.urlencode(parameters)
    return url

def make_url(ParamString={"barcode":"test"}, RecNum='15819341131', 
             SignName=u'读书郎'.encode('utf-8'), TemplateCode='SMS_56415001'):
    ts = str(int(time.time()))
    key = '8e517d999976168979f81dc73d010c90'
    code = str(random.randint(100000, 999999))
    authkey = ts+'-'+code+'-'+md5(ts+'-'+code+'-'+key).hexdigest()
    parameters = {
        # user params
        'authKey': authkey,
        'appName': 'care.readboy.com',
        'signName': SignName,
        'templateCode': TemplateCode,
        'phoneNumber': RecNum,
        'templateParam': json.dumps(ParamString),
    }
    url = server_address + "&" + urllib.urlencode(parameters)
    return url

def make_request(quiet=False):
    # url = compose_url()
    url = make_url()
    print url
    return
    request = urllib2.Request(url)
    try:
        conn = urllib2.urlopen(request)
        response = conn.read()
    except urllib2.HTTPError, e:
        print(e.read().strip())
        raise SystemExit(e)
    try:
        obj = json.loads(response)
        if quiet:
            return obj
    except ValueError, e:
        raise SystemExit(e)
    json.dump(obj, sys.stdout, sort_keys=True, indent=2, ensure_ascii=false)
    sys.stdout.write('\n')

if __name__ == '__main__':
    # print sys.stdin.encoding
    make_request()
