# API手册

## HOST
http://**************/post_repair

+ 测试：`http://**************/post_repair`
+ 正式：`https://api-repair-hub.readboy.com`

## API签名
**签名参数（作为Query参数添加到所有请求中）**
> + `ua`: 设备信息，依次包含如下字段，用`/`连接
>>    + `签名算法版本：1`
>>    + `系统名+版本号`
>>    + `机型`
>>    + `app_id`
>>    + `应用版本号`
>>    + `设备唯一id`
>+ `t`: 机器时间戳，单位：秒
>+ `sn`: 签名值，算法：`md5( ua + app_secret + t )`

<p class="danger">
**注意**：`ua`可能出现非ASCII字符，请先计算`sn`值，再`urlencode(device_id)`
</p>

## 注意
> + 每个接口都需要有签名参数：sn,t,ua
> + 目前测试接口的access_token字段暂时不传，用uid代表身份，后期用access_token替换
> + 每个接口需要处理鉴权级别错误：
> + 错误码：6003，7200
```json
{
    "ok": 0,
    "msg": "未登录或令牌过期",//错误提示
    "errno": 7200  // 错误码
}
```

## 查看保修期
__url__: `GET /repair/period`

__params__:
> + `access_token`: 认证中心令牌
> + `code`: 机器序列号或者条码，IMEI

__response__:
```json
{
    "msg": "success",
    "data": {
        "can": true,//能否申请寄修
        "in_period": 2,//1保内，2保外，0没有保卡信息
        "has_warranty": 1,//有没有保卡1有，0没有
        "barcode": "ZS17040000037",//条码
        "damage": [
            {
                "title": "无法充电"
            },
            {
                "title": "无法开机"
            },
            {
                "title": "屏幕损坏"
            },
            {
                "title": "其他"
            }
        ],//损坏类型
        "contact": {
            "province": "440000",
            "city": "442000",
            "name": "李四",
            "district": "442001006",
            "phone": "123",
            "address": "文昌路富城书店",
            "city_name": "中山市",
            "district_name": "五桂山街道",
            "id": 13,
            "province_name": "广东省"
        },//默认联系人
        "endpoint": [
            {
                "province": 440000,
                "city": 442000,
                "name": "读书郎总部寄修中心",
                "district": 442001006,
                "phone": "111",
                "contact": "寄修部",
                "address": "广东省中山市五桂山镇长逸路38号读书郎教育科技有限公司",
                "city_name": "中山市",
                "district_name": "五桂山街道",
                "id": 1,
                "province_name": "广东省"
            }
        ],//寄修中心地址
        "model_id": 150,//机型id
        "model_name": "ZS"//机型名称
    },
    "ok": 1
}
{
    "msg": "success",
    "data": {
        "can": false,
        "history": {
            "status": 100,
            "created_at": "2019-07-19 11:08:38",
            "model_name": "G800",
            "sn": "201907191108389401301254"
        }
    },
    "ok": 1
}
{
    "msg": "机器正在寄修中",
    "errno": 7006,
    "ok": 0
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
{
    "msg": "寄修服务未支持该机型",
    "errno": 7008,
    "ok": 0
}
```

## 查看保修期v1

__url__: `GET /repair/v1/period`

__params__:

> + `access_token`: 认证中心令牌
> + `number`: 机器序列号(以下三选一)
> + `barcode`: 条码
> + `imei`: IMEI

__response__:

```json
{
    "msg": "success",
    "data": {
        "can": true,//能否申请寄修
        "in_period": 2,//1保内，2保外，0没有保卡信息
        "has_warranty": 1,//有没有保卡1有，0没有
        "barcode": "ZS17040000037",//条码
        "damage": [
            {
                "image": [
                    "http://dt.readboy.com/rbcare/image/damage/aa0775cd4905c44a7a781e9d6e7a05e9.png",
                    "http://dt.readboy.com/rbcare/image/damage/早教平板.png"
                ],
                "title": "无法充电"
            },
            {
                "image": [],
                "title": "无法开屏"
            }
        ],//损坏类型
        "contact": {
            "province": "440000",
            "city": "442000",
            "name": "李四",
            "district": "442001006",
            "phone": "123",
            "address": "文昌路富城书店",
            "city_name": "中山市",
            "district_name": "五桂山街道",
            "id": 13,
            "province_name": "广东省"
        },//默认联系人
        "endpoint": [
            {
                "province": 440000,
                "city": 442000,
                "name": "读书郎总部寄修中心",
                "district": 442001006,
                "phone": "111",
                "contact": "寄修部",
                "address": "广东省中山市五桂山镇长逸路38号读书郎教育科技有限公司",
                "city_name": "中山市",
                "district_name": "五桂山街道",
                "id": 1,
                "province_name": "广东省"
            }
        ],//寄修中心地址
        "model_id": 150,//机型id
        "model_name": "ZS"//机型名称
    },
    "ok": 1
}
{
    "msg": "success",
    "data": {
        "can": false,
        "history": {
            "status": 100,
            "created_at": "2019-07-19 11:08:38",
            "model_name": "G800",
            "sn": "201907191108389401301254"
        }
    },
    "ok": 1
}
{
    "msg": "机器正在寄修中",
    "errno": 7006,
    "ok": 0
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
{
    "msg": "寄修服务未支持该机型",
    "errno": 7008,
    "ok": 0
}
```


## 查看寄修历史

__url__: `GET /repair/history`

__params__:
> + `access_token`: 认证中心令牌
> + `date`: 返回比该日期早的历史，例如 2019-07-10 14:14:33
> + `number`: 多少条

__response__:
```json
{
    "msg": "success",
    "data": {
        "is_end": false,//是否最后一页
        "data": [
            {
                "status": 500,
                "created_at": "2019-08-15 17:19:06",
                "appraise": 0,
                "model_name": "ZS",
                "sn": "20190815171906643077"
            },
            {
                "status": 100,
                "created_at": "2019-07-10 14:14:33",
                "model_name": "G800",
                "appraise": 0,
                "sn": "201907101414337815391044"
            }
        ],
        "size": 2
    },
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


##  地区筛选
__url__: `GET /repair/exp_filter`

__params__:
> + `access_token`: 认证中心令牌
> + `province`: '广东省',//寄方省
> + `city`: '中山市',//寄方市
> + `district`: '三乡镇',//寄方镇区
> + `address`: '文昌路三乡市场',//寄方地址
> + `d_province`: '广东省',//收方省
> + `d_city`: '中山市',//收方市
> + `d_district`: '三乡镇',//收方镇区
> + `d_address`: '文昌路三乡市场',//收方地址

__response__:
```json
{
    "msg": "success",
    "data": {
        "self": false,//能否自主寄件
        "visit": false,//能否上门收件
    },
    "ok": 1
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```


##  申请寄修

__url__: `POST /repair/add`
__url__: `POST /repair/add_v2`

`v2` 仅改变了返回格式: `data` 返回的是生成的寄修单号

__params__:

> + `access_token`: 认证中心令牌
> + `content`: 数据内容json字符串，json参数如下：

```json
{
    "barcode": "123",//条码
    "model_name": "G800",//机器名称
    "model_id": 100,//机器id
    "serial": "7d9513234707",//机器序列号
    "in_period": 1,//保内1保外2
    "has_warranty": 1,//有保1，没保0
    "reason": 1,//损坏状态1人为损坏2元件损坏
    "damage": "屏幕不灵",//损坏类型，英文逗号分隔
    "period_file": ["http://dt.readboy.com/rbcare/repair/upload/11915/e0099b2a5e7a6b12d958e77697a52b36.png",...],//保修期证明文件完整地址
    "upload_file": ["http://dt.readboy.com/rbcare/repair/upload/11915/e0099b2a5e7a6b12d958e77697a52b36.png",...],//文件完整地址
    "video_file":  ["http://dt.readboy.com/rbcare/repair/upload/11915/e0099b2a5e7a6b12d958e77697a52b36.png"], // 可选  为空传空数组
    "description": "点击屏幕无反应",//损坏描述
    "name": "大哥",//联系人
    "phone": "18845678901",//联系电话
    "province": "广东省",//省
    "city": "中山市",//市
    "district": "三乡镇",//镇区
    "address": "文昌路三乡市场",//地址
    "come_exp_type": 1,//1上门还是2自寄
    "repair_endpoint": 1,//回寄中心id
    "need_invoice": 1,//是否需要发票，1需要
    "invoice_type": 1,//发票类型，1个人，2企业
    "invoice_title": "个人",//发票抬头
    "invoice_tax_id": "*********",//发票税号
    "invoice_email": "<EMAIL>",//发票接收邮箱 
    "is_send_back": 1,  // 默认1   是否回寄维修配件   1 支持  0不支持
    "external_fault": 0, // 外表故障   0--正常      1--刮痕-裂痕
    "internal_fault": 0, // 内部故障   0--正常      1--浸液
    "is_tell": 0 // 是否需要客服知会   0--不需要      1--需要
}
```

__response__:

```json
// v1
{
    "msg": "success",
    "data": true,
    "ok": 1
}
// v2
{
    "msg": "success",
    "data": "20220907185520353654",
    "ok": 1
}

{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```

##  禁止寄修

__url__: `GET /repair/ban`

__params__:

> + `access_token`: 认证中心令牌

__response__:

```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "春节将至，为不影响您的寄修体验，1.12-1.31期间将暂停寄修服务，给您带来不便，敬请谅解。",
    "errno": 7015,
    "ok": 0
}
{
    "msg": "未登录或令牌过期",
    "errno": 7200,
    "ok": 0
}
```

##  取消订单

__url__: `GET /repair/cancel`

__params__:

> + `access_token`: 认证中心令牌
> + `order_sn`: 寄修订单号

__response__:

```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "未登录或令牌过期",
    "errno": 7200,
    "ok": 0
}
{
    "msg": "参数错误",
    "errno": 7004,
    "ok": 0
}
{
    "msg": "状态不符，无法取消订单",
    "errno": 7015,
    "ok": 0
}
```

##  设备列表机器信息

__url__: `GET /repair/fault_equip_info`

__params__:

> + `access_token`: 认证中心令牌
>
> + ` content `: 内容json字符串，格式如下：
>
>   ```json
>   {
>   	"imei": ["7d364208a924", "7d364208a9241", "7d5612dcc8e6"]
>   }
>   ```
>

__response__:

```json

{
    "msg": "success",
    "data": [
        {
            "in_period": 1,//1保内，2保外，0没有保卡信息
            "has_warranty": 1,//有没有保卡1有，0没有
            "is_repair": 0,
            "barcode": "8007081923994",
            "period": "2021/01/07",//保修期截止时间
            "has_info": true,//有无数据
            "imei": "7d364208a924"
        },
        {
            "imei": "7d364208a9241",
            "has_info": false	//未找到数据
        },
        {
            "in_period": 0,
            "has_warranty": 0,
            "is_repair": 0,
            "barcode": "7012111929830",
            "period": "",
            "has_info": true,
            "imei": "7d5612dcc8e6"
        }
    ],
    "ok": 1
}
{
    "msg": "未登录或令牌过期",
    "errno": 7200,
    "ok": 0
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```

##  修改收件地址

__url__: `POST /repair/change_address`

__params__:

> + `access_token`: 认证中心令牌
> + `order_sn`:寄修订单号
> + `content`: 内容json字符串，格式如下：

```json
{
    'name': '李四',
    'phone': '123',
    'province': '广东',
    'city': '中山市',
    'district': '五桂山',
    'address': '长逸路38号读书郎',
}
```



__response__:

```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}

{
    "msg": "未登录或令牌过期",
    "errno": 7200,
    "ok": 0
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
{
    "msg": "无权限修改订单",
    "errno": 7004,
    "ok": 0
}
{
    "msg": "修改失败",
    "errno": 7004,
    "ok": 0
}
```


##  修改寄修
__url__: `POST /repair/change`

__params__:
> + `access_token`: 认证中心令牌
> + `order_sn`: 订单号
> + `content`: 数据内容json字符串，json参数如下：
```json
{
    'come_exp_type': 2,//1上门还是2自寄
    'come_exp_com': '顺丰',//快递公司名称
    'come_exp_sn': '987',//快递单号
}
```

__response__:
```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```


##  确认收货
__url__: `POST /repair/finish`

__params__:
> + `access_token`: 认证中心令牌
> + `order_sn`: 订单号

__response__:
```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```

##  评价订单
__url__: `POST /repair/appraise`

__params__:
> + `access_token`: 认证中心令牌
> + `order_sn`: 订单号
> + `content`: 评价详情json字符串，json参数如下：
```json
{
    'star': 2,//星
    'description': '评价描述详情',//评价描述详情，最多200字
}
```

__response__:
```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```


##  寄修详情
__url__: `GET /repair/detail`

__params__:
> + `access_token`: 认证中心令牌
> + `order_sn`: 订单号
> + info字段说明：
```json
`uid` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户id',
`sn` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '订单号',
`status` INT(11) NOT NULL DEFAULT '0' COMMENT '订单状态，已下单100,审核通过200,审核不通过-200,用户已发货300,发货失败-300,已收货400,已检测500,用户已支付600,已维修700,已回（到付发货）800,回寄失败-800,已完成900,已取消-900',
`barcode` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '机器条码',
`serial` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '机器序列号',
`model_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '机型名称',
`model_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机型id',
`in_period` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '保内保外，保内1，保外2',
`has_warranty` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '有没有保卡，1有，0没有',
`reason` INT(11) NOT NULL DEFAULT '0' COMMENT '损坏原因，人为1，质量问题2',
`damage` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '受损类型，多个类型用英文逗号分隔',
`period_file` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '保内证明文件，json数组字符串',
`upload_file` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '损坏文件，json数组字符串',
`description` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '损坏描述，最多200字',
`name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '联系人，最多20字',
`phone` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '电话号码',
`province` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '省',
`city` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '市',
`district` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '区',
`address` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '详细地址，最多200字',
`come_exp_type` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '寄来快递类型，1上门服务，2自主寄件',
`rb_come_exp_sn` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '公司内部寄来快递单号',
`come_exp_sn` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '寄来快递单号',
`come_exp_com` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '寄来快递公司',
`audit_status` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '审核状态，1同意寄修，2不需要寄修',
`audit_opinion` VARCHAR(500) NOT NULL DEFAULT '0' COMMENT '审核意见，最多200字',
`come_sure` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '寄来收货确认，1已收货',
`pay_com` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '支付公司',
`rb_pay_sn` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '公司内部支付单号',
`pay_sn` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '支付单号',
`is_paid` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '是否已支付， 1为已支付',
`check_man` INT(11) NOT NULL DEFAULT '0' COMMENT '检查人，维修工id',
`repair_man` INT(11) NOT NULL DEFAULT '0' COMMENT '维修人，维修工id',
`deal` VARCHAR(200) NOT NULL DEFAULT '' COMMENT '维修处理，最多100字',
`accessory_cast` DECIMAL(12,2) NOT NULL DEFAULT '0.00' COMMENT '配件费用',
`staff_cast` DECIMAL(12,2) NOT NULL DEFAULT '0.00' COMMENT '人工费用',
`amount` DECIMAL(12,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
`pay_amount` DECIMAL(12,2) NOT NULL DEFAULT '0.00' COMMENT '待付金额',
`deal_remark` VARCHAR(200) NOT NULL DEFAULT '' COMMENT '维修备注，最多100字',
`receive_case` VARCHAR(200) NOT NULL DEFAULT '' COMMENT '收到的配件，最多100字',
`repair_status` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '维修状态，0未维修，1维修中，2维修完，3弃修',
`rb_go_exp_sn` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '寄去公司内部单号',
`go_exp_sn` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '寄去快递单号',
`go_exp_com` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '寄去快递公司',
`go_exp_type` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '寄去快递支付类型，1为月结，2为到付',
`go_sure` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '寄去快递确认发货，1为确认发货',
`go_received` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '寄去快递确认收货，1为确认收货',
`repair_endpoint` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '选择的寄修中心id',
`need_invoice` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '是否需要发票，1需要',
`invoice_type` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '发票类型，1个人，2企业',
`invoice_title` VARCHAR(100) NULL DEFAULT NULL COMMENT '发票抬头',
`invoice_tax_id` VARCHAR(100) NULL DEFAULT NULL COMMENT '发票税号',
`invoice_email` VARCHAR(100) NULL DEFAULT NULL COMMENT '发票接收邮箱',
`appraise` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '是否已评价',
```
__response__:

`optional_accessory_cast` 自选配件费用
`optional_accessory_status` 订单的自选配件状态，0正常 -1部分取消 -2全部取消
`optional_accessory.status` 各个自选配件的状态，0正常 -1取消

```json
{
    "msg": "success",
    "data": {
        "info": {
            "province": "广东省",
            "uid": 1,
            "deal": "",
            "come_sure": 0,
            "repair_man": 0,
            "updated_at": "2019-07-19 13:58:59",
            "go_exp_sn": "",
            "come_exp_com": "顺丰",
            "go_exp_com": "",
            "pay_sn": "",
            "serial": "abcdefg",
            "id": 992,
            "city": "中山市",
            "in_period": 1,
            "district": "三乡镇",
            "pay_com": "",
            "rb_go_exp_sn": "",
            "check_man": 0,
            "damage": "屏幕不灵",
            "audit_status": 0,
            "staff_cast": 0.00,
            "period_file": "",
            "status": 300,
            "model_id": 100,
            "go_sure": 0,
            "description": "点击屏幕无反应",
            "pay_amount": 0.00,
            "go_exp_type": 0,
            "barcode": "123",
            "phone": "",
            "reason": 1,
            "rb_come_exp_sn": "",
            "address": "文昌路三乡市场",
            "deal_remark": "",
            "optional_accessory_cast": 32.00,
            "optional_accessory_status": -1,
            "accessory_cast": 0.00,
            "repair_status": 0,
            "upload_file": "",
            "come_exp_type": 2,
            "audit_opinion": "0",
            "name": "大哥",
            "come_exp_sn": "987",
            "is_paid": 0,
            "receive_case": "",
            "go_received": 0,
            "created_at": "2019-07-10 14:14:33",
            "amount": 0.00,
            "sn": "201907101414337815391044",
            "rb_pay_sn": "",
            "model_name": "G800",
            "repair_endpoint": 1
        },
        "malfunction": [
            {
                "title": "触屏失效"
            }
        ],
        "endpoint": {
            "province": 440000,
            "city": 442000,
            "name": "读书郎总部寄修中心",
            "district": 442001006,
            "phone": "111",
            "contact": "寄修部",
            "address": "广东省中山市五桂山镇长逸路38号读书郎教育科技有限公司",
            "city_name": "中山市",
            "district_name": "五桂山街道",
            "id": 1,
            "province_name": "广东省"
        },
        "log": [
            {
                "remark": "",
                "pr_sn": "201907101414337815391044",
                "relation_key": "",
                "uid": 1,
                "pr_status": 300,
                "title": "",
                "admin": 0,
                "log_from": "app",
                "date": "2019-07-11 14:14:33",
                "operation": "come_exp_sent",
                "id": 3
            },
            {
                "remark": "",
                "pr_sn": "201907101414337815391044",
                "relation_key": "",
                "uid": 1,
                "pr_status": 100,
                "title": "寄修订单提交，寄修服务中心等待接收中",
                "admin": 0,
                "log_from": "app",
                "date": "2019-07-10 14:14:33",
                "operation": "create_order",
                "id": 1
            }
        ],
        "optional_accessory": [
            {
                "count": 2,
                "status": -1,
                "image": "https://dt.readboy.com/rbcare/repair/optional_accessory/accessory_image/015fb88a36f1c2caa1b9a763dfd0fd2c.jpg",
                "price": 50.00,
                "name": "C6皮套"
            },
            {
                "count": 4,
                "status": 0,
                "image": "https://dt.readboy.com/rbcare/repair/optional_accessory/accessory_image/3ba6c6c7f6ad6c73569a377a7bf87c08.jpg",
                "price": 8.00,
                "name": "Q10底壳散热膜"
            }
        ],
        "accessory": [
            {
                "count": 1,
                "price": 480.00,
                "title": "触摸屏"
            }
        ]
    },
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 上传保内证明文件
__url__: `POST /repair/period_file`
__params__:
> + `access_token`: 认证中心令牌
> + `images`: 图片文件流，（暂不支持多个）

__response__:
```json
{
    "data": "http://dt.readboy.com/rbcare/images/xxx/xxx/xxxxx.jpeg",
    "msg": "success",
    "ok": 1
}
{
    "msg": "上传文件失败",
    "errno": 7000,
    "ok": 0
}
```


## 上传说明证明文件
__url__: `POST /repair/upload_file`
__params__:
> + `access_token`: 认证中心令牌
> + `images`: 图片文件流，（暂不支持多个）

__response__:
```json
{
    "data": "http://dt.readboy.com/rbcare/images/xxx/xxx/xxxxx.jpeg",
    "msg": "success",
    "ok": 1
}
{
    "msg": "上传文件失败",
    "errno": 7000,
    "ok": 0
}
```


## 联系人列表
__url__: `GET /repair/contacts`

__params__:
> + `access_token`: 认证中心令牌

__response__:
```json
{
    "msg": "success",
    "data": {
        "default": 11,//默认联系人id
        "contacts": [
            {
                "province": "440000",
                "city": "442000",
                "name": "张三",
                "district": "442001006",
                "phone": "123456",
                "address": "文昌路三乡市场",
                "city_name": "中山市",
                "district_name": "五桂山街道",
                "id": 11,
                "province_name": "广东省"
            },
            {
                "province": "440000",
                "city": "442000",
                "name": "赵柳",
                "district": "442001006",
                "phone": "456",
                "address": "文昌路大记酒楼",
                "city_name": "中山市",
                "district_name": "五桂山街道",
                "id": 12,
                "province_name": "广东省"
            }
        ]
    },
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 新建联系人
__url__: `POST /repair/add_contact`
__params__:
> + `access_token`: 认证中心令牌
> + `set_default`: 设为默认联系人，1为设置，不传或者0为不设置
> + `content`: 内容json字符串，格式如下：
```json
{
    'name': '李四',
    'phone': '123',
    'province': 440000,
    'city': 442000,
    'district': 442001006,
    'address': '文昌路富城书店',
}
```

__response__:
```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```


## 修改联系人
__url__: `POST /repair/update_contact`
__params__:
> + `access_token`: 认证中心令牌
> + `id`: 联系人id
> + `set_default`: 设为默认联系人，1为设置，不传或者0为不设置
> + `content`: 内容json字符串，格式如下：
```json
{
    'name': '李四',
    'phone': '123',
    'province': 440000,
    'city': 442000,
    'district': 442001006,
    'address': '文昌路富城书店',
}
```

__response__:
```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```


## 修改默认联系人
__url__: `POST /repair/default_contact`
__params__:
> + `access_token`: 认证中心令牌
> + `id`: 联系人id

__response__:
```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```


## 删除联系人
__url__: `POST /repair/drop_contact`
__params__:
> + `access_token`: 认证中心令牌
> + `id`: 联系人id

__response__:
```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
{
    "msg": "数据有误",
    "errno": 7004,
    "ok": 0
}
```


## 地区列表
__url__: `GET /repair/region`

__params__:
> + `pid`: 父级地区id，用于获取所有子级地区
> + `level`: 批量获取level以下的所有地区，与pid同时使用时无效

__response__:
```json
{
    "data": [
        {
            "parent_id": 650000,
            "region_id": 652900,
            "region_name": "阿克苏地区",
            "region_type": 1,
            "shortname": "阿克苏",
            "sort": 1
        },
        {
            "parent_id": 650000,
            "region_id": 659002,
            "region_name": "阿拉尔市",
            "region_type": 1,
            "shortname": "阿拉尔",
            "sort": 2
        },
        ...
        {
            "parent_id": 650000,
            "region_id": 654000,
            "region_name": "伊犁哈萨克自治州",
            "region_type": 1,
            "shortname": "伊犁",
            "sort": 18
        }
    ],
    "msg": "success",
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 说明文件配置项
__url__: `GET /repair/explain`

__params__:
> + `key`: 配置项
举例：
```json
'service_phone':客服电话,
'service_agreement':服务协议,
'repair_explain':服务说明,
'watch_barcode_guide':手表找码指引,
'pad_barcode_guide':平板找码指引,
'express_fee_description':快递费用说明,
'abandon_first_explain':弃修首次提示,
'abandon_second_explain':弃修二次提示,
```

__response__:
```json
{
    "msg": "success",
    "data": "同意协议代表认同规则由读书郎解析",//富文本字符串
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 机型列表
__url__: `GET /repair/machine`

__params__:
> + `name`: 名称模糊搜索

__response__:
```json
{
    "msg": "success",
    "data": [
        {
            "model_id": 8,
            "name": "G90"
        },
        {
            "model_id": 18,
            "name": "Q5"
        },
    ]
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 配件列表
__url__: `GET /repair/accessory`

__params__:
> + `model_id`: 机型id

__response__:
```json
{
    "msg": "success",
    "data": [
        {
            "price": 50.00,
            "title": "主控IC（CPU）"
        },
        {
            "price": 30.00,
            "title": "WIFI模块"
        },
    ]
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```

## 微信JSSDK签名

__url__: `GET /repair/wx_scan`

__params__:

> + `access_token`: 认证中心令牌
> + `url`: 地址

__response__:

```json
{
    "msg": "success",
    "data": {
        "timestamp": 1585623547,
        "noncestr": "orts6o9axloog6o8aphqwujzj8kxxfzb",
        "sign": "046b2e522fa009661aa37c46214c27039d6506fe"
    },
    "ok": 1
}
{
    "msg": "未登录或令牌过期",
    "errno": 7200,
    "ok": 0
}
{
    "msg": "参数错误",
    "errno": 7004,
    "ok": 0
}
{
    "msg": "签名获取失败",
    "errno": 7001,
    "ok": 0
}
```


## 快递信息

__url__: `GET /repair/exp_route`

__params__:
> + `exp_sn`: 快递内部流水号

__response__:
```json
{
    "msg": "success",
    "data": [
        {
            "remark": "顺丰速运 已收取快件（测试数据）",
            "accept_address": "广东省深圳市软件产业基地",
            "opcode": "50",
            "accept_time": "2018-05-01 08:01:44"
        },
        {
            "remark": "已签收,感谢使用顺丰,期待再次为您服务（测试数据）",
            "accept_address": "广东省深圳市软件产业基地",
            "opcode": "80",
            "accept_time": "2018-05-02 12:01:44"
        }
    ]
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 订单微信支付
__url__: `POST /repair/wxpay`

__params__:
> + `order_sn`: 订单流水号
> + `access_token`: 用户令牌
> + `trade_type`: 支付方式   ("APP", "JSAPI")   默认"APP"
> + `openid`:微信用户唯一标识码     非必填     trade_type="JSAPI"时必填     

__response__:
```json
{
    "msg": "success",
    "data": {
        "package": "Sign=WXPay",
        "timestamp": 1566297907,
        "sign": "4690095690239C08355634FFC0DAE25B",
        "partnerid": "1492060162",
        "appid": "wx1ddb8aacc25dcf08",
        "prepayid": "wx201847037344857b07d02cb21036046800",
        "noncestr": "cdbj7vz6v9u6aaocukut2a03lp3wa1uc"
    },
    "ok": 1
}
{
    "msg": "订单状态不可支付",
    "errno": 7015,
    "ok": 0
}
{
    "msg": "应用不可支付",
    "errno": 7015,
    "ok": 0
}
```


## 订单支付宝支付
__url__: `POST /repair/alipay`

__params__:
> + `order_sn`: 订单流水号
> + `access_token`: 用户令牌

__response__:
```json
{
    "msg": "success",
    "data": "format=json&timestamp=2019-08-23+09%3A05%3A10&charset=utf-8&app_id=2019081266189270&biz_content=%7B%22out_trade_no%22%3A%2220190815171906643077jxzf6970%22%2C%22subject%22%3A%22%E8%AF%BB%E4%B9%A6%E9%83%8E%E5%AE%B6%E9%95%BF%E5%8A%A9%E6%89%8B-%E5%AF%84%E4%BF%AE%E8%B4%B9%E7%94%A8%22%2C%22timeout_express%22%3A%2290m%22%2C%22total_amount%22%3A%220.01%22%7D&sign=ZyT%2Bbm90nwx60mRvc7XAA9XD1e83286NxKS8ncu6KdDPIvmiyz8ZAAjO6EPTivxmRi%2F56oWunvY%2BiKL9bvNGZdxhbwcvUR801Nt5f1YhN1RcdvlanOyV2H9KrqEwWyOq%2FgegyzYVe1QyidrnwZAEipA%2FjUteA0xGqLkwUpqvog6a2lMWOdzVPzaRXjVWp6GgaYF6zywYsyl0PFr9g3IoaHL0yoBtdkqSvMYDADZDw3GCezrUCDPMEoUiIsTN83MwdMYXu%2BX3tpamZDFYE0pL8V1OOwXk9KzFMTemQ4J2mak%2FOd0%2BIdxvkr7lsOob8ynWJFp0yaAKwHmy6zWF6qV0hw%3D%3D&version=1.0&sign_type=RSA2&method=alipay.trade.app.pay",
    "ok": 1
}
{
    "msg": "订单状态不可支付",
    "errno": 7015,
    "ok": 0
}
{
    "msg": "应用不可支付",
    "errno": 7015,
    "ok": 0
}
```


## 机型品类
__url__: `GET /repair/machine_category`

__params__:

__response__:
```json
{
    "msg": "success",
    "data": [
        {
            "image": "http://dt.readboy.com/rbcare/image/machneCategory/学生平板.png",
            "id": 1,
            "name": "学生平板"
        },
        {
            "image": "http://dt.readboy.com/rbcare/image/machneCategory/早教平板.png",
            "id": 2,
            "name": "早教平板"
        },
        {
            "image": "http://dt.readboy.com/rbcare/image/machneCategory/电话手表.png",
            "id": 3,
            "name": "电话手表"
        },
        {
            "image": "",
            "id": 4,
            "name": "学生手机"
        },
        {
            "image": "",
            "id": 5,
            "name": "学生电脑"
        },
        {
            "image": "",
            "id": 6,
            "name": "点读机"
        }
    ],
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 机型品类的故障
__url__: `GET /repair/self_test_damage`

__params__:
> + `machine_category_id`: 机型品类id

__response__:
```json
{
    "msg": "success",
    "data": [
        {
            "id": 1,
            "title": "无法充电"
        },
        {
            "id": 2,
            "title": "无法开机"
        },
        {
            "id": 5,
            "title": "电池漏液"
        },
        {
            "id": 6,
            "title": "无法开屏"
        }
    ],
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 机型品类的故障
__url__: `GET /repair/self_test_damage`

__params__:
> + `machine_category_id`: 机型品类id

__response__:
```json
{
    "msg": "success",
    "data": [
        {
            "id": 1,
            "title": "无法充电"
        },
        {
            "id": 2,
            "title": "无法开机"
        },
        {
            "id": 5,
            "title": "电池漏液"
        },
        {
            "id": 6,
            "title": "无法开屏"
        }
    ],
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 自检问题列表
__url__: `GET /repair/self_test_list`

__params__:
> + `machine_category_id`: 机型品类id， 可选
> + `damage_id`: 故障id， 可选
> + `page`: 第几页
> + `count`: 每页条数， 0-100

__response__:
```json
{
    "msg": "success",
    "data": {
        "isEnd": true,
        "data": [
            {
                "id": 1,
                "title": "无法开机"
            }
        ],
        "size": 1
    },
    "ok": 1
}
{
    "msg": "未找到数据",
    "errno": 7013,
    "ok": 0
}
```


## 自检问题详情
__url__: `GET /repair/self_test_detail`

__params__:
> + `access_token`: 用户令牌
> + `id`: 自检问题id

__response__:
```json
{
    "msg": "success",
    "data": {
        "appraised": false,
        "detail": {
            "content": "<p></p><p>试试看是不是没电</p>",
            "id": 1,
            "title": "无法开机"
        }
    },
    "ok": 1
}
```


## 自检问题评论
__url__: `POST /repair/self_test_appraise`

__params__:
> + `access_token`: 用户令牌
> + `id`: 自检问题id
> + `helpful`: 有没有用，1有用，2没用
> + `appraise`: 评论详情， 200字以内

__response__:
```json
{
    "msg": "success",
    "data": true,
    "ok": 1
}
```


## 自选配件获取配件分类列表
__url__: `GET /optional_accessory/category/list`

__params__:
> + `only_have`: 是否只返回有配件的分类，默认 `true`

__response__:

`sort` 是由大到小的排序
`count` 该分类下 所含 启用的配件 的数量

```json
{
  "msg": "success",
  "data": [
    {
      "id": 1,
      "sort": 3,
      "name": "皮套",
      "count": 1
    },
    {
      "id": 2,
      "sort": 2,
      "name": "手表带",
      "count": 0
    },
    {
      "id": 3,
      "sort": 0,
      "name": "卡壳",
      "count": 0
    }
  ],
  "ok": 1
}
```


## 自选配件获取配件列表
__url__: `GET /optional_accessory/list`

__params__:
> + `category_id`: 配件分类id
> + `page`: 页码
> + `count`: 每页数量

__response__:

`sort` 是由大到小的排序
`quantity` 对应物料库存

```json
{
  "msg": "success",
  "data": {
    "is_end": true,
    "all_count": 1,
    "data": [
      {
        "id": 1,
        "sort": 0,
        "sell_price": 50.00,
        "image": "https://dt.readboy.com/rbcare/repair/optional_accessory/accessory_image/015fb88a36f1c2caa1b9a763dfd0fd2c.jpg",
        "category_id": 1,
        "name": "C6皮套",
        "quantity": 0
      }
    ],
    "size": 1
  },
  "ok": 1
}
```


## 取消自选配件
__url__: `POST /optional_accessory/cancel_all`

__params__:
> + `order_sn`: 指定寄修订单号

__response__:

```json
{
  "msg": "success",
  "data": true,
  "ok": 1
}
```


## 提交自选配件
__url__: `POST /optional_accessory/add`

仅可提交给 终端代寄缓存的小单 或 状态处于`100`待审核的已提交的订单
本接口提交的配件是`修改`的，所以需要提交所有要选购的配件，每次都是全量数据

__params__:
> + `endpoint_id`: 终端id，终端代寄时才传
> + `order_sn`: 指定寄修订单号，必传
> + `content`: 数据内容json字符串，json参数如下：
```json
[
    {
        "oa_id": 1, // 配件id
        "count": 1, // 选购数量
    }
]
```

__response__:

```json
{
  "msg": "success",
  "data": true,
  "ok": 1
}
{
  "msg": "请求配件数量超过库存",
  "errno": 7004,
  "ok": 0
}
```

