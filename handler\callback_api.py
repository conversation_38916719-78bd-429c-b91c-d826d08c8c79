# encoding=utf-8
# __author__ = 'lch'
import base64
import hashlib
import os
from hashlib import md5
import lxml.etree as ET
from Crypto.Cipher import AES
from tornado import gen
from tornado.log import app_log
from tornado.concurrent import run_on_executor
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
import oss2
from tornado.web import Request<PERSON>andler
from log import sf_log, wxpay_log, alipay_log
from handler import ApiHandler, tokenauth
import util.com as com
import conf.config as config
import json
from store import repair_model, sf_model, work_wechat_model, wxpay_model, alipay_model, call_log_model
from tornado.web import RequestHandler

KEY = "xHZKBP2EwiBtdjjkVSOvPei23vPAdsvf"
class BaseHandler(RequestHandler):

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.dumpJson(ret)
        # app_log.info(s)

    def dumpJson(self, ret):
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            s = json.dumps(ret, ensure_ascii=False, indent=4)
        else:
            s = json.dumps(ret, ensure_ascii=False)
        self.write(s)
        return s


class CB_SF_Route(ApiHandler):
    @gen.coroutine
    def post(self):
        """顺丰快递路由推送"""
        xml = self.get_argument('xml', '')
        data= sf_model.push_route(xml)
        # pr_sn = yield repair_model.get_exp_order_sn(orderid)
        if not data:
            sf_log.error('call back sf route error', exc_info=True)
            raise gen.Return(self.error(u'数据格式有误', com.ERROR_PARAM))
        for i in data:
            i['com'] = '顺丰'
            # i['pr_sn'] = pr_sn
            if com.safeInt(i.get('opcode', 0)) in [50,54,43,46]:
                i['status'] = 1
            elif com.safeInt(i.get('opcode', 0)) in [30,31,130,123,607,36,77]:
                i['status'] = 2
            elif com.safeInt(i.get('opcode', 0)) in [44,204,125,47,126,658,657,630,664,34]:
                i['status'] = 3
            elif com.safeInt(i.get('opcode', 0)) in [80,8000]:
                i['status'] = 4
            else:
                i['status'] = 0
            yield repair_model.store_exp_route(i)
            sf_log.info('callback sf route:')
            sf_log.info(xml)
        raise gen.Return(self.success(True))


class CB_WX_Pay(RequestHandler):
    @gen.coroutine
    def post(self):
        # 微信支付通知参数
        xml = self.request.body
        wxpay_log.info('callback come data='+xml)
        # 判断参数是否正确
        notify = wxpay_model.notify(xml)
        if not notify:
            # 返回微信端   -- 通知失败
            self.write(wxpay_model.notify_return(notify))
        else:
            # 验证通过
            # 通知中如果是支付成功
            # 获取返回信息
            notify_data = wxpay_model.get_notify_data(xml)
            wxpay_log.info('notify_data:' + json.dumps(notify_data))
            pay_com = 1
            if notify_data and notify_data.get('result_code') == 'SUCCESS' and notify_data.get('out_trade_no'):
                # 查询支付订单数据库信息
                pay_order = yield repair_model.pay_order_data(notify_data['out_trade_no'], pay_com)

                if pay_order and pay_order.get('trade_state') != 'SUCCESS' and pay_order.get('pr_sn') and pay_order.get('appid') and pay_order.get('readboy_sn'):
                    wxpay_log.info('pay_order:')
                    wxpay_log.info(pay_order)
                    # 查询寄修单数据库信息
                    order = yield repair_model.order_data(pay_order['pr_sn'], ['status', 'pay_amount'])
                    # 为500
                    if order and order.get('status') == config.ORDER_CHECK:
                        # 核对微信订单信息 调用微信支付查询接口
                        wxpay_order = yield wxpay_model.query_pay(pay_order.get('appid'), pay_order.get('readboy_sn'))
                        if wxpay_order.get('return_code') == 'SUCCESS' and wxpay_order.get('result_code') == 'SUCCESS' and wxpay_order.get('trade_state') == 'SUCCESS' and wxpay_order.get('total_fee') == str(long(order.get('pay_amount')*100)):
                            # 修改寄修单和支付订单状态
                            change_data = {
                                'pr_sn': pay_order.get('pr_sn'),
                                'pay_com': pay_order.get('com'),
                                'rb_pay_sn': pay_order.get('readboy_sn'),
                                'is_paid': 1,
                                'status': config.ORDER_PAY,
                                'pay_sn': notify_data.get('transaction_id')
                            }
                            change_state = yield repair_model.order_pay_sure(change_data)
                            wxpay_log.info('change_state:')
                            wxpay_log.info(change_state)
                            if change_state:
                                ### 关闭其他支付方式的订单
                                ### 插入通知记录
                                pay_log = dict()
                                pay_log['pay_com'] = pay_com
                                pay_log['readboy_sn'] = notify_data.get('out_trade_no')
                                pay_log['pay_sn'] = notify_data.get('transaction_id')
                                pay_log['detail'] = com.prettyJson(notify_data)
                                wxpay_log.info('pay_log:' + com.prettyJson(pay_log))
                                yield repair_model.save_pay_log(pay_log)
                                self.write(wxpay_model.notify_return(notify))
                                raise gen.Return(self.finish())
                        else:
                            warning_amount_str = '微信支付回调问题\n寄修订单支付金额 或 支付回调状态 检查不对\n寄修订单号：{order_sn}\n当前订单状态：{order_status}\n回调金额：{total_fee}\n需付金额：{pay_amount}'.format(
                                order_sn=pay_order['pr_sn'], order_status=order.get('status'), total_fee=wxpay_order.get('total_fee'), pay_amount=str(long(order.get('pay_amount')*100)))
                            work_wechat_model.send_message(work_wechat_model.warning_reminder_id_list, warning_amount_str)
                    else:
                        warning_status_str = '微信支付回调问题\n寄修订单状态不为 待支付\n寄修订单号：{order_sn}\n当前订单状态：{order_status}'.format(
                            order_sn=pay_order['pr_sn'], order_status=order.get('status'))
                        work_wechat_model.send_message(work_wechat_model.warning_reminder_id_list, warning_status_str)
                ### 插入通知记录
                pay_log = dict()
                pay_log['pay_com'] = pay_com
                pay_log['readboy_sn'] = notify_data.get('out_trade_no')
                pay_log['pay_sn'] = notify_data.get('transaction_id')
                pay_log['detail'] = com.prettyJson(notify_data)
                wxpay_log.info('pay_log:'+com.prettyJson(pay_log))
                yield repair_model.save_pay_log(pay_log)
                self.write(wxpay_model.notify_return(notify))
                raise gen.Return(self.finish())
            self.write(wxpay_model.notify_return(False))
        raise gen.Return(self.finish())


class CB_AR_WX_Pay(RequestHandler):
    @gen.coroutine
    def post(self):
        # 微信支付通知参数
        xml = self.request.body
        wxpay_log.info('callback come data='+xml)
        # 判断参数是否正确
        notify = wxpay_model.notify(xml)
        if not notify:
            # 返回微信端   -- 通知失败
            self.write(wxpay_model.notify_return(notify))
        else:
            # 验证通过
            # 通知中如果是支付成功
            # 获取返回信息
            notify_data = wxpay_model.get_notify_data(xml)
            wxpay_log.info('notify_data:' + json.dumps(notify_data))
            pay_com = 1
            if notify_data and notify_data.get('result_code') == 'SUCCESS' and notify_data.get('out_trade_no'):
                # 查询支付订单数据库信息
                pay_order = yield repair_model.pay_order_data(notify_data['out_trade_no'], pay_com)

                if pay_order and pay_order.get('trade_state') != 'SUCCESS' and pay_order.get('pr_sn') and pay_order.get('appid') and pay_order.get('readboy_sn'):
                    wxpay_log.info('pay_order:')
                    wxpay_log.info(pay_order)
                    # 查询寄修单数据库信息
                    order = yield repair_model.order_data(pay_order['pr_sn'], ['status', 'staff_cast', 'amount_in_ar'])
                    wxpay_log.info('order:')
                    wxpay_log.info(order)
                    # 为500
                    if order and order.get('status') == config.ORDER_CHECK:
                        # 核对微信订单信息 调用微信支付查询接口
                        wxpay_order = yield wxpay_model.query_pay(pay_order.get('appid'), pay_order.get('readboy_sn'))
                        wxpay_log.info('wxpay_order:')
                        wxpay_log.info(wxpay_order)
                        if wxpay_order.get('return_code') == 'SUCCESS' and wxpay_order.get('result_code') == 'SUCCESS' \
                                and wxpay_order.get('trade_state') == 'SUCCESS' \
                                and wxpay_order.get('total_fee') == str(long(order.get('amount_in_ar')*100)):
                            # 修改寄修单和支付订单状态
                            change_data = {
                                'pr_sn': pay_order.get('pr_sn'),
                                'ar_pay_com': pay_order.get('com'),
                                'ar_rb_pay_sn': pay_order.get('readboy_sn'),
                                'ar_is_paid': 1,
                                'status': config.ORDER_PAY,
                                'ar_pay_sn': notify_data.get('transaction_id'),
                                'connect': 3,
                                'accessory_amount': 0,
                                'accessory_cast': 0,
                                'amount': order.get('amount_in_ar'),
                                'pay_amount': order.get('amount_in_ar'),
                            }
                            wxpay_log.info('change_data:')
                            wxpay_log.info(change_data)
                            change_state = yield repair_model.ar_order_pay_sure(change_data)
                            wxpay_log.info('change_state:')
                            wxpay_log.info(change_state)
                            if change_state:
                                # 关闭其他支付方式的订单
                                # 插入通知记录
                                pay_log = dict()
                                pay_log['pay_com'] = pay_com
                                pay_log['readboy_sn'] = notify_data.get('out_trade_no')
                                pay_log['pay_sn'] = notify_data.get('transaction_id')
                                pay_log['detail'] = com.prettyJson(notify_data)
                                wxpay_log.info('pay_log:' + com.prettyJson(pay_log))
                                yield repair_model.save_pay_log(pay_log)
                                self.write(wxpay_model.notify_return(notify))
                                raise gen.Return(self.finish())
                # 插入通知记录
                pay_log = dict()
                pay_log['pay_com'] = pay_com
                pay_log['readboy_sn'] = notify_data.get('out_trade_no')
                pay_log['pay_sn'] = notify_data.get('transaction_id')
                pay_log['detail'] = com.prettyJson(notify_data)
                wxpay_log.info('pay_log:'+com.prettyJson(pay_log))
                yield repair_model.save_pay_log(pay_log)
                self.write(wxpay_model.notify_return(notify))
                raise gen.Return(self.finish())
            self.write(wxpay_model.notify_return(False))
        raise gen.Return(self.finish())


class CB_WX_Refund(RequestHandler):
    @gen.coroutine
    def post(self):
        # 微信退款通知参数
        xml = self.request.body
        wxpay_log.info('refund  callback come data='+xml)
        # 判断参数是否正确

        notify_data = wxpay_model.get_notify_data(xml)
        # 判断参数是否正确
        refund_com = 1
        # 结果通知通信成功
        if notify_data and notify_data.get('return_code') == 'SUCCESS':
            # 解密
            req_info = notify_data.get('req_info')

            str_info = base64.b64decode(req_info)

            key = hashlib.md5(KEY).hexdigest()
            aes = AES.new(key, AES.MODE_ECB)
            msg_dec = aes.decrypt(str_info).decode('utf-8')
            wxpay_log.info('data=' + json.dumps(msg_dec))
            msg_dec = json.loads(json.dumps(msg_dec).replace('\u0006', ''))
            array_data = {}
            root = ET.fromstring(msg_dec, ET.XMLParser(resolve_entities=False))
            for child in root:
                value = child.text
                array_data[child.tag] = value
            wxpay_log.info('array_data=' + json.dumps(array_data))
            # 查询退款单
            refund_order = yield repair_model.refund_order_data(array_data.get('out_refund_no'), refund_com)
            # 判断未通知到
            if refund_order and refund_order.get('refund_state') != 'SUCCESS' and refund_order.get('pr_sn') and refund_order.get('appid') and refund_order.get('readboy_sn'):
                    wxpay_log.info('refund_order:')
                    wxpay_log.info(refund_order)

                    # 查询寄修单数据库信息
                    order = yield repair_model.order_data(refund_order['pr_sn'], ['status', 'pay_amount'])
                    # 为500
                    if order and order.get('status') in [600, 700, 800, -800, -900]:
                        # 核对微信订单信息 调用微信支付查询接口
                        wxrefund_order = yield wxpay_model.query_refund(refund_order.get('appid'), refund_order.get('readboy_sn'))
                        wxpay_log.info('wxrefund_order:')
                        wxpay_log.info(wxrefund_order)
                        if wxrefund_order.get('return_code') == 'SUCCESS' \
                                and wxrefund_order.get('result_code') == 'SUCCESS' \
                                and wxrefund_order.get('refund_status_0') == 'SUCCESS' \
                                and wxrefund_order.get('refund_fee_0') == str(long(refund_order.get('refund_amount')*100)):
        # 修改寄修单和支付订单状态
                            change_data = {
                                'pr_sn': refund_order.get('pr_sn'),
                                'refund_com': refund_order.get('com'),
                                'rb_refund_sn': refund_order.get('readboy_sn'),
                                'is_refund': 1,
                                'refund_state': wxrefund_order.get('refund_status_0'),
                                'refund_sn': wxrefund_order.get('refund_id_0'),    # 退款单号
                                'pay_amount': order.get('pay_amount'),
                                'refund_fee': refund_order.get('refund_amount')
                            }
                            change_state = yield repair_model.order_refund_sure(change_data)
                            wxpay_log.info('change_state:')
                            wxpay_log.info(change_state)
                            if change_state:
            #                     ### 关闭其他支付方式的订单
            #                     ### 插入通知记录
                                refund_log = dict()
                                refund_log['refund_com'] = refund_com
                                refund_log['readboy_sn'] = array_data.get('out_trade_no')
                                refund_log['refund_sn'] = array_data.get('refund_id')
                                refund_log['detail'] = com.prettyJson(array_data)
                                wxpay_log.info('pay_log:' + com.prettyJson(refund_log))
                                yield repair_model.save_refund_log(refund_log)
                                self.write(wxpay_model.notify_return(True))
                                raise gen.Return(self.finish())
            ### 插入通知记录
            # 已有记录
            refund_log = dict()
            refund_log['refund_com'] = refund_com
            refund_log['readboy_sn'] = array_data.get('out_trade_no')
            refund_log['refund_sn'] = array_data.get('refund_id')
            refund_log['detail'] = com.prettyJson(array_data)
            wxpay_log.info('pay_log:' + com.prettyJson(refund_log))
            yield repair_model.save_refund_log(refund_log)
            self.write(wxpay_model.notify_return(True))
            raise gen.Return(self.finish())
        self.write(wxpay_model.notify_return(False))
        raise gen.Return(self.finish())


class CB_ALI_Pay(RequestHandler):
    @gen.coroutine
    def post(self):
        post_data = self.request.body_arguments
        charset = self.get_argument('charset', 'utf-8')
        notify_data = {x: post_data.get(x)[0].encode(charset) for x in post_data.keys()}
        alipay_log.info(notify_data)
        notify = alipay_model.notify(notify_data)
        if not notify:
            self.write(alipay_model.notify_return(notify))
        else:
            ### 验证通过
            ### 通知中如果是支付成功
            pay_com = 2
            if notify_data and notify_data.get('trade_status') == 'TRADE_SUCCESS' and notify_data.get('out_trade_no'):
                ### 查询支付订单数据库信息
                pay_order = yield repair_model.pay_order_data(notify_data['out_trade_no'], pay_com)
                alipay_log.info('pay_order:')
                alipay_log.info(pay_order)
                if pay_order and pay_order.get('trade_state') != 'SUCCESS' and pay_order.get('pr_sn') and pay_order.get('appid') and pay_order.get('readboy_sn'):
                    ### 查询寄修单数据库信息
                    order = yield repair_model.order_data(pay_order['pr_sn'], ['status', 'pay_amount'])
                    alipay_log.info('order:')
                    alipay_log.info(order)
                    if order and order.get('status') == config.ORDER_CHECK:
                        ### 核对支付宝订单信息
                        query_order = yield alipay_model.query_pay(pay_order.get('appid'), pay_order.get('readboy_sn'))
                        alipay_log.info('query_order:')
                        alipay_log.info(query_order)
                        if query_order.get('code') == '10000' and query_order.get('trade_status') == 'TRADE_SUCCESS' and query_order.get('total_amount') == str(order.get('pay_amount')):
                            ### 修改寄修单和支付订单状态
                            change_data = {
                                'pr_sn': pay_order.get('pr_sn'),
                                'pay_com': pay_order.get('com'),
                                'rb_pay_sn': pay_order.get('readboy_sn'),
                                'is_paid': 1,
                                'status': config.ORDER_PAY,
                                'pay_sn': notify_data.get('trade_no')
                            }
                            change_state = yield repair_model.order_pay_sure(change_data)
                            alipay_log.info('change_state:')
                            alipay_log.info(change_state)
                            if change_state:
                                ### 关闭其他支付方式的订单
                                ### 插入通知记录
                                pay_log = dict()
                                pay_log['pay_com'] = pay_com
                                pay_log['readboy_sn'] = notify_data.get('out_trade_no')
                                pay_log['pay_sn'] = notify_data.get('trade_no')
                                pay_log['detail'] = com.prettyJson(notify_data)
                                alipay_log.info('pay_log:' + com.prettyJson(pay_log))
                                yield repair_model.save_pay_log(pay_log)
                                self.write(alipay_model.notify_return(notify))
                                raise gen.Return(self.finish())
                ### 插入通知记录
                pay_log = dict()
                pay_log['pay_com'] = pay_com
                pay_log['readboy_sn'] = notify_data.get('out_trade_no')
                pay_log['pay_sn'] = notify_data.get('trade_no')
                pay_log['detail'] = com.prettyJson(notify_data)
                alipay_log.info('pay_log:'+com.prettyJson(pay_log))
                yield repair_model.save_pay_log(pay_log)
                self.write(alipay_model.notify_return(notify))
                raise gen.Return(self.finish())
            self.write(alipay_model.notify_return(False))
        raise gen.Return(self.finish())


class CB_AR_ALI_Pay(RequestHandler):
    @gen.coroutine
    def post(self):
        post_data = self.request.body_arguments
        charset = self.get_argument('charset', 'utf-8')
        notify_data = {x: post_data.get(x)[0].encode(charset) for x in post_data.keys()}
        alipay_log.info(notify_data)
        notify = alipay_model.notify(notify_data)
        if not notify:
            self.write(alipay_model.notify_return(notify))
        else:
            # 验证通过
            # 通知中如果是支付成功
            pay_com = 2
            if notify_data and notify_data.get('trade_status') == 'TRADE_SUCCESS' and notify_data.get('out_trade_no'):
                # 查询支付订单数据库信息
                pay_order = yield repair_model.pay_order_data(notify_data['out_trade_no'], pay_com)
                alipay_log.info('pay_order:')
                alipay_log.info(pay_order)
                if pay_order and pay_order.get('trade_state') != 'SUCCESS' and pay_order.get('pr_sn') \
                        and pay_order.get('appid') and pay_order.get('readboy_sn'):
                    # 查询寄修单数据库信息
                    order = yield repair_model.order_data(pay_order['pr_sn'], ['status', 'staff_cast', 'amount_in_ar'])
                    alipay_log.info('order:')
                    alipay_log.info(order)
                    if order and order.get('status') == config.ORDER_CHECK:
                        # 核对支付宝订单信息
                        query_order = yield alipay_model.query_pay(pay_order.get('appid'), pay_order.get('readboy_sn'))
                        alipay_log.info('query_order:')
                        alipay_log.info(query_order)
                        if query_order.get('code') == '10000' and query_order.get('trade_status') == 'TRADE_SUCCESS' \
                                and query_order.get('total_amount') == str(order.get('amount_in_ar')):
                            # 修改寄修单和支付订单状态
                            change_data = {
                                'pr_sn': pay_order.get('pr_sn'),
                                'ar_pay_com': pay_order.get('com'),
                                'ar_rb_pay_sn': pay_order.get('readboy_sn'),
                                'ar_is_paid': 1,
                                'status': config.ORDER_PAY,
                                'ar_pay_sn': notify_data.get('trade_no'),
                                'connect': 3,
                                'accessory_amount': 0,
                                'accessory_cast': 0,
                                'amount': order.get('amount_in_ar'),
                                'pay_amount': order.get('amount_in_ar'),
                            }
                            change_state = yield repair_model.ar_order_pay_sure(change_data)
                            alipay_log.info('change_state:')
                            alipay_log.info(change_state)
                            if change_state:
                                # 关闭其他支付方式的订单
                                # 插入通知记录
                                pay_log = dict()
                                pay_log['pay_com'] = pay_com
                                pay_log['readboy_sn'] = notify_data.get('out_trade_no')
                                pay_log['pay_sn'] = notify_data.get('trade_no')
                                pay_log['detail'] = com.prettyJson(notify_data)
                                alipay_log.info('pay_log:' + com.prettyJson(pay_log))
                                yield repair_model.save_pay_log(pay_log)
                                self.write(alipay_model.notify_return(notify))
                                raise gen.Return(self.finish())
                # 插入通知记录
                pay_log = dict()
                pay_log['pay_com'] = pay_com
                pay_log['readboy_sn'] = notify_data.get('out_trade_no')
                pay_log['pay_sn'] = notify_data.get('trade_no')
                pay_log['detail'] = com.prettyJson(notify_data)
                alipay_log.info('pay_log:'+com.prettyJson(pay_log))
                yield repair_model.save_pay_log(pay_log)
                self.write(alipay_model.notify_return(notify))
                raise gen.Return(self.finish())
            self.write(alipay_model.notify_return(False))
        raise gen.Return(self.finish())

# 云客服通话记录推送
class YKF_Push(BaseHandler):
    @gen.coroutine
    def get(self):

        # 自定义字段
        DialoutStrVar = self.get_argument('DialoutStrVar', '')
        bill_id = ''

        if DialoutStrVar:
            try:
                customParam = json.loads(DialoutStrVar)
            except:
                raise gen.Return(self.error(u'数据有误1', com.ERROR_EMPTY_DATA))
            bill_id = customParam['bill_id']
        else:
            raise gen.Return(self.dumpJson(200))

        data = {
            'call_no': self.get_argument('CallNo', ''),
            'called_no' : self.get_argument('CalledNo', ''),
            'call_sheet_id' : self.get_argument('CallSheetID', ''),
            'call_type' : self.get_argument('CallType', ''),
            'begin' : self.get_argument('Begin', ''),
            'end' : self.get_argument('End', ''),
            'agent' : self.get_argument('Agent', ''),
            'state' : self.get_argument('State', ''),
            'call_state' : self.get_argument('CallState', ''),
            'record_file' : self.get_argument('RecordFile', ''),
            'file_server' : self.get_argument('FileServer', ''),
            'province' : self.get_argument('Province', ''),
            'district' : self.get_argument('District', ''),
            'bill_id' : bill_id
        }

        result = yield call_log_model.add_call_log(data)
        raise gen.Return(self.dumpJson(200) if result else self.error(u'数据有误2', com.ERROR_EMPTY_DATA))


           


