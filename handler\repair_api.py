# encoding=utf-8
# __author__ = 'lch'
import datetime
from decimal import Decimal
from tornado import gen
from conf import config
import util.com as com
from handler import <PERSON>piHand<PERSON>, tokenauth
import json
from store import optional_accessory_model, repair_model, warranty_model, region_model, sf_model, wxpay_model, agent_model, alipay_model, user_model
from log import sf_log, wxpay_log, alipay_log


class HelloWorld(ApiHandler):
    def get(self):
        self.success(None, msg=u'welcome')

# 申请寄修(正常寄修)
class PR_AddRepair(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        if not content.get('barcode') or not content.get('serial'):
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        if not content.get('phone'):
            raise gen.Return(self.error(u'手机号不能为空', com.ERROR_PARAM))
        if not com.is_hone(content['phone']):
            raise gen.Return(self.error(u'手机号格式出错', com.ERROR_PARAM))
        # 验证数据是否正确安全
        data = yield repair_model.ban_repair()
        if data:
            raise gen.Return(self.error(data, com.ERROR_EMPTY_DATA))

        ### 验证手机是否在黑名单中
        check_black_phone = yield repair_model.check_black_phone(content['phone'])
        if check_black_phone:
            raise gen.Return(self.error(u'无法寄修，有疑问请联系400客服人员', com.ERROR_PARAM))

        ### 验证条码是否在黑名单中
        check_black_list = yield repair_model.check_black_list(content['barcode'])
        if check_black_list:
            raise gen.Return(self.error(u'无法寄修，有疑问请联系400客服人员', com.ERROR_PARAM))

        code = content['barcode']
        machine = yield warranty_model.checkMachineByWarranty(None, code, None)
        if not machine:
            machine = yield warranty_model.checkMESV1(None, code, None)
        if not machine or not machine.get('model') or not machine.get('barcode'):
            raise gen.Return(self.error(u'数据有误, 请判断条码是否正确', com.ERROR_PARAM))
        barcode = machine['barcode']
        number = machine['number']
        imei = machine['imei1']
        model_name = machine['model']
        # model_info = yield repair_model.check_model_id(model_name)
        model_id = machine['model_id']
        model_info = yield repair_model.check_model(model_id)
        if not model_info or not model_info.get('model_id'):
            raise gen.Return(self.error(u'寄修服务未支持该机型', com.ERROR_EMPTY_DATA))
        model_id = model_info['model_id']
        category_id = yield repair_model.check_category_id(model_id)
        ### 检查机器是否正在维修中
        check = yield repair_model.check_barcode_order(barcode)
        if check:
            raise gen.Return(self.error(u'该设备寄修中', com.ERROR_PARAM))
        color = machine.get('color')
        warranty = yield warranty_model.getByBarcode(barcode)
        in_period = repair_model.in_period(warranty)
        ### 手表服务器验证
        if not warranty and machine.get('imei1') and category_id in [3]:
            pass
            # warranty = yield warranty_model.checkMachineWear(machine['imei1'])
            # if isinstance(warranty, dict):
            #     if warranty.get('buy_date') == '0000-00-00 00:00:00':
            #         warranty['buy_date'] = None
            #     else:
            #         warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'], '%Y-%m-%d %H:%M:%S').strftime('%Y.%m.%d') if warranty.get('buy_date') else None
            #     in_period = repair_model.in_period(warranty)
            #     warranty = None
        # 判断是否是特批，特批默认保外，没有保卡也默认保外
        is_special = yield repair_model.check_special(barcode)
        repeat = yield repair_model.barcode_repeat(barcode)
        ar_repeat = yield repair_model.ar_repeat(barcode)
        screen_insurance = yield repair_model.get_broken_screen_insurance(barcode)
        in_si_period, used_screen_insurance = yield repair_model.in_si_period(screen_insurance)
        content['in_si_period'] = in_si_period
        content['has_screen_insurance'] = 1 if screen_insurance else 0
        content['barcode'] = barcode
        content['serial'] = number
        content['imei'] = imei or u''
        content['model_name'] = model_name
        content['model_id'] = model_id
        content['color'] = color
        content['in_period'] = 2 if is_special or not warranty else in_period
        content['reason'] = 2
        content['has_warranty'] = 1 if warranty else 0
        content['repeat_order'] = repeat
        content['ar_repeat'] = ar_repeat
        ### 插入数据
        add = None
        for i in range(0, 10):
            add = yield repair_model.add_repair(uid, content)
            if add:
                break
        raise gen.Return(self.success(True) if add else self.error(u'寄修错误', com.ERROR_PARAM) )


# 申请寄修(正常寄修) 直接复制了V1，用于H5，改了返回格式
class PR_AddRepair_V2(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        if not content.get('barcode') or not content.get('serial'):
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        if not content.get('phone'):
            raise gen.Return(self.error(u'手机号不能为空', com.ERROR_PARAM))
        if not com.is_hone(content['phone']):
            raise gen.Return(self.error(u'手机号格式出错', com.ERROR_PARAM))
        # 验证数据是否正确安全
        data = yield repair_model.ban_repair()
        if data:
            raise gen.Return(self.error(data, com.ERROR_EMPTY_DATA))

        ### 验证手机是否在黑名单中
        check_black_phone = yield repair_model.check_black_phone(content['phone'])
        if check_black_phone:
            raise gen.Return(self.error(u'无法寄修，有疑问请联系400客服人员', com.ERROR_PARAM))

        ### 验证条码是否在黑名单中
        check_black_list = yield repair_model.check_black_list(content['barcode'])
        if check_black_list:
            raise gen.Return(self.error(u'无法寄修，有疑问请联系400客服人员', com.ERROR_PARAM))

        barcode = content['barcode']
        number = ''
        imei = ''
        pseudo_code = yield agent_model.check_model_id_pseudo_code(barcode)
        # 判断是否是伪码
        machine = {}
        if pseudo_code:
            if pseudo_code['status'] == 1:
                raise gen.Return(self.error(u'此伪码已被使用', com.ERROR_PARAM))
            model_name = pseudo_code['model_name']
            model_id = pseudo_code['model_id']
        else:
            machine = yield warranty_model.checkMachineByWarranty(None, barcode, None)
            if not machine:
                machine = yield warranty_model.checkMESV1(None, barcode, None)
            if not machine or not machine.get('model') or not machine.get('barcode'):
                raise gen.Return(self.error(u'数据有误, 请判断条码是否正确', com.ERROR_PARAM))
            barcode = machine['barcode']
            number = machine['number']
            imei = machine['imei1']
            model_name = machine['model']
            model_id = machine['model_id']
        model_info = yield repair_model.check_model(model_id)
        if not model_info or not model_info.get('model_id'):
            raise gen.Return(self.error(u'寄修服务未支持该机型', com.ERROR_EMPTY_DATA))
        model_id = model_info['model_id']
        category_id = yield repair_model.check_category_id(model_id)
        ### 检查机器是否正在维修中
        check = yield repair_model.check_barcode_order(barcode)
        if check:
            raise gen.Return(self.error(u'该设备寄修中', com.ERROR_PARAM))
        color = machine.get('color')
        warranty = yield warranty_model.getByBarcode(barcode)
        in_period = repair_model.in_period(warranty)
        ### 手表服务器验证
        if not pseudo_code and not warranty and machine.get('imei1') and category_id in [3]:
            pass
            # warranty = yield warranty_model.checkMachineWear(machine['imei1'])
            # if isinstance(warranty, dict):
            #     if warranty.get('buy_date') == '0000-00-00 00:00:00':
            #         warranty['buy_date'] = None
            #     else:
            #         warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'], '%Y-%m-%d %H:%M:%S').strftime('%Y.%m.%d') if warranty.get('buy_date') else None
            #     in_period = repair_model.in_period(warranty)
            #     warranty = None
        # 判断是否是特批，特批默认保外，没有保卡也默认保外
        is_special = yield repair_model.check_special(barcode)
        repeat = yield repair_model.barcode_repeat(barcode)
        ar_repeat = yield repair_model.ar_repeat(barcode)
        screen_insurance = yield repair_model.get_broken_screen_insurance(barcode)
        in_si_period, used_screen_insurance = yield repair_model.in_si_period(screen_insurance)
        content['in_si_period'] = in_si_period
        content['has_screen_insurance'] = 1 if screen_insurance else 0
        content['barcode'] = barcode
        content['serial'] = number
        content['imei'] = imei or u''
        content['model_name'] = model_name
        content['model_id'] = model_id
        content['color'] = color
        content['in_period'] = 2 if is_special or not warranty else in_period
        content['reason'] = 2
        content['has_warranty'] = 1 if warranty else 0
        content['repeat_order'] = repeat
        content['ar_repeat'] = ar_repeat
        ### 插入数据
        add = None
        for i in range(0, 10):
            add = yield repair_model.add_repair(uid, content)
            if add:
                break
        raise gen.Return(self.success(add.get('sn', '')) if add else self.error(u'寄修错误', com.ERROR_PARAM) )


class PR_Change(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', None)
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        validate = yield repair_model.check_order(uid, sn)
        if validate not in [100, 200, -300]:
            raise gen.Return(self.error(u'订单状态不允许修改寄修', com.ERROR_PARAM))
        data = yield repair_model.change_order(sn, content)
        raise gen.Return(self.success(data) if data else self.error(u'修改寄修失败', com.ERROR_PARAM))


class PR_Finish(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', None)
        validate = yield repair_model.check_order(uid, sn)
        if validate not in [config.ORDER_GO_SUCCESS]:
            raise gen.Return(self.error(u'无权操作', com.ERROR_PARAM))
        data = yield repair_model.finish_order(sn)
        raise gen.Return(self.success(data) if data else self.error(u'数据有误', com.ERROR_PARAM))


class PR_Appraise(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', None)
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        order = yield repair_model.order_data(sn, ['appraise', 'status'])
        if not order or order.get('status') != config.ORDER_FINISH:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        content['uid'] = uid
        data = yield repair_model.appraise_order(sn, content)
        raise gen.Return(self.success(data) if data else self.error(u'数据有误', com.ERROR_PARAM))


class PR_Period(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        code = self.get_argument('code', None)
        if not code:
            raise gen.Return(self.error(u'缺少条码', com.ERROR_EMPTY_DATA))
        machine = yield warranty_model.checkMES(code)
        if not machine or not machine.get('barcode'):
            raise gen.Return(self.error(u'未找到条码', com.ERROR_EMPTY_DATA))
        barcode = machine['barcode']
        model_name = machine['model']
        model_info = yield repair_model.check_model_id(model_name)
        if not model_info or not model_info.get('model_id'):
            raise gen.Return(self.error(u'寄修服务未支持该机型', com.ERROR_BAD_REQUEST))
        model_id = model_info['model_id']
        category_id = yield repair_model.check_category_id(model_id)
        ### 检查机器是否正在维修中
        check = yield repair_model.check_barcode_order(barcode)
        if check:
            check = yield repair_model.check_barcode_order(barcode, uid)
            if check:
                data = dict()
                data['can'] = False
                data['history'] = check
                raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
            raise gen.Return(self.error(u'机器正在寄修中', com.ERROR_ALREADY_EXISTS))
        warranty = yield warranty_model.getByBarcode(barcode)
        in_period = repair_model.in_period(warranty)
        ### 手表服务器验证
        if not warranty and machine.get('imei1') and category_id in [3]:
            pass
            # warranty = yield warranty_model.checkMachineWear(machine['imei1'])
            # if isinstance(warranty, dict):
            #     if warranty.get('buy_date') == '0000-00-00 00:00:00':
            #         warranty['buy_date'] = None
            #     else:
            #         warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'], '%Y-%m-%d %H:%M:%S').strftime('%Y.%m.%d') if warranty.get('buy_date') else None
            #     in_period = repair_model.in_period(warranty)
        data = dict()
        data['can'] = True
        data['model_name'] = model_name
        data['model_id'] = model_id
        data['barcode'] = barcode
        data['in_period'] = in_period
        data['has_warranty'] = 1 if warranty else 0
        data['damage'] = yield repair_model.damage(category_id)
        data['endpoint'] = yield repair_model.endpoints()
        default_contact = yield repair_model.get_default_contact(uid)
        data['contact'] = yield repair_model.contact(default_contact)
        data['express_fee_description'] = yield repair_model.explain('express_fee_description')
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_PeriodV1(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        # code = self.get_argument('code', None)
        number = self.get_argument('number', None)
        barcode = self.get_argument('barcode', None)
        imei = self.get_argument('imei', None)
        if not barcode and not number and not imei:
            raise gen.Return(self.error(u'参数错误', com.ERROR_EMPTY_DATA))
        machine = {}
        pseudo_code = yield agent_model.check_model_id_pseudo_code(barcode)
        # 判断是否是伪码
        if pseudo_code:
            if pseudo_code['status'] == 1:
                raise gen.Return(self.error(u'此伪码已被使用', com.ERROR_PARAM))
            model_name = pseudo_code['model_name']
            model_id = pseudo_code['model_id']
        else:
            machine = yield warranty_model.checkMachineByWarranty(number, barcode, imei)
            if not machine:
                machine = yield warranty_model.checkMESV1(number, barcode, imei)
            if not machine or not machine.get('barcode'):
                raise gen.Return(self.error(u'未找到条码', com.ERROR_EMPTY_DATA))
            barcode = machine['barcode']
            model_name = machine['model']
            model_id = machine['model_id']
        model_info = yield repair_model.check_model(model_id)
        if not model_info or not model_info.get('model_id'):
            raise gen.Return(self.error(u'寄修服务未支持该机型', com.ERROR_BAD_REQUEST))
        model_id = model_info['model_id']
        category_id = yield repair_model.check_category_id(model_id)
        # 检查机器是否正在维修中
        check = yield repair_model.check_barcode_order(barcode)
        if check:
            check = yield repair_model.check_barcode_order(barcode, uid)
            if check:
                data = dict()
                data['can'] = False
                data['history'] = check
                data['prompt_enable'] = model_info['prompt_enable']
                data['prompt_info'] = model_info['prompt_info']
                raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
            raise gen.Return(self.error(u'机器正在寄修中', com.ERROR_ALREADY_EXISTS))
        warranty = yield warranty_model.getByBarcode(barcode)
        in_period = repair_model.in_period(warranty)
        ### 手表服务器验证
        if not pseudo_code and not warranty and machine.get('imei1') and category_id in [3]:
            pass
            # warranty = yield warranty_model.checkMachineWear(machine['imei1'])
            # if isinstance(warranty, dict):
            #     if warranty.get('buy_date') == '0000-00-00 00:00:00':
            #         warranty['buy_date'] = None
            #     else:
            #         warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'], '%Y-%m-%d %H:%M:%S').strftime('%Y.%m.%d') if warranty.get('buy_date') else None
            #     in_period = repair_model.in_period(warranty)
        screen_insurance = yield repair_model.get_broken_screen_insurance(barcode)
        in_si_period, used_screen_insurance = yield repair_model.in_si_period(screen_insurance)
        data = dict()
        data['can'] = True
        data['model_name'] = model_name
        data['model_id'] = model_id
        data['barcode'] = barcode
        data['in_period'] = in_period
        data['has_warranty'] = 1 if warranty and warranty['status'] == 1 else 0
        data['in_si_period'] = in_si_period
        data['used_screen_insurance'] = used_screen_insurance
        data['has_screen_insurance'] = 1 if screen_insurance else 0
        data['category_name'] = yield repair_model.get_machine_category_name(category_id)
        data['endpoint'] = yield repair_model.endpoints()
        data['prompt_enable'] = model_info['prompt_enable']
        data['prompt_info'] = model_info['prompt_info']
        data['image_tip'] = "若实物故障与图片不符，因此导致的维修时间延迟或维修不当，由用户自行承担"
        data['external_fault_tip'] = "您的机器是否出现刮痕、裂痕？"
        data['internal_fault_tip'] = "您的机器是否曾受液体浸入？"
        default_contact = yield repair_model.get_default_contact(uid)
        contact = yield repair_model.contact(default_contact)
        if not contact:
            contact = yield repair_model.contact_one(uid)
            if contact:
                yield repair_model.set_default_contact(uid, contact['id'])
        data['contact'] = contact
        data['express_fee_description'] = yield repair_model.explain('express_fee_description')
        damage_data = yield repair_model.damage(category_id)
        if damage_data:
            for key in damage_data:
                image = []
                if key.get('image_front'):
                    key['image_front'] = 'https://dt.readboy.com/' + key['image_front']
                    image.append(key['image_front'])
                if key.get('image_back'):
                    key['image_back'] = 'https://dt.readboy.com/' + key['image_back']
                    image.append(key['image_back'])
                del key['image_front']
                del key['image_back']
                key['image'] = image
        data['damage'] = damage_data
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_MyHistory(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        date = self.get_argument('date', None)
        number = com.safeInt(self.get_argument('number', 10))
        barcode = self.get_argument('barcode', None)
        subSn = self.get_argument('subSn', None)
        phone = yield user_model.get_user_phone(uid)
        check_order = yield repair_model.check_order_by_phone(uid, phone)
        data = yield repair_model.repair_history(uid, barcode, subSn, date, number)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_Detail(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', None)
        validate = yield repair_model.check_order(uid, sn)
        if not validate:
            raise gen.Return(self.error(u'无权操作订单', com.ERROR_PARAM))
        # 待支付状态下有支付单的需要查询支付状态
        yield repair_model.order_pay_check(sn)

        ret = dict()
        ret['info'] = yield repair_model.order_info(sn)
        ret['log'] = yield repair_model.order_log(sn)
        ret['malfunction'] = yield repair_model.malfunction(sn)
        if ret['info']['in_period'] == 1 and ret['info']['pay_amount'] == 0:
            ret['accessory'] = []
        else:
            ret['accessory'] = yield repair_model.accessory(sn)
        ret['optional_accessory'] = yield optional_accessory_model.pr_oa_list(sn)
        ret['endpoint'] = yield repair_model.endpoint(ret['info'].get('repair_endpoint'))
        pseudo_code = yield agent_model.check_pseudo_code(ret['info'].get('barcode'), uid)
        ret['is_pseudo_code'] = 1 if pseudo_code else 0
        raise gen.Return(self.success(ret) if ret else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_Endpoint(ApiHandler):
    @gen.coroutine
    def get(self):
        data = yield repair_model.endpoints()
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_Contacts(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint_id = self.get_argument('endpoint_id', '0')
        endpoint_id = int(endpoint_id)
        data = dict()
        data['contacts'] = yield repair_model.contacts(uid, endpoint_id)
        data['default'] = yield repair_model.get_default_contact(uid, endpoint_id)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_AddContact(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint_id = self.get_argument('endpoint_id', '0')
        endpoint_id = int(endpoint_id)
        content = self.get_argument('content', '')
        set_default = com.safeInt(self.get_argument('set_default', 0))
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        data = yield repair_model.add_contact(uid, endpoint_id, content, set_default)
        raise gen.Return(self.success(data) if data else self.error(u'数据有误', com.ERROR_PARAM))


class PR_UpdateContact(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        content = self.get_argument('content', '')
        contact_id = com.safeInt(self.get_argument('id', 0))
        set_default = com.safeInt(self.get_argument('set_default', 0))
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        data = yield repair_model.update_contact(contact_id, content)
        if set_default:
            yield repair_model.set_default_contact(uid, contact_id)
        raise gen.Return(self.success(data) if data else self.error(u'数据有误', com.ERROR_PARAM))


class PR_DefaultContact(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        contact_id = com.safeInt(self.get_argument('id', 0))
        data = yield repair_model.set_default_contact(uid, contact_id)
        raise gen.Return(self.success(data) if data else self.error(u'数据有误', com.ERROR_PARAM))


class PR_DropContact(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint_id = self.get_argument('endpoint_id', '0')
        endpoint_id = int(endpoint_id)
        contact_id = com.safeInt(self.get_argument('id', 0))
        data = yield repair_model.drop_contact(uid, contact_id, endpoint_id)
        if data:
            default = yield repair_model.get_default_contact(uid, endpoint_id)
            if default == contact_id:
                # contacts = yield repair_model.contacts(uid)
                # contacts_id = [i.get('id') for i in contacts]
                # if contacts_id:
                #     yield repair_model.set_default_contact(uid, contacts_id[0])
                # else:
                #     yield repair_model.set_default_contact(uid, 0)
                yield repair_model.set_default_contact(uid, 0)
        raise gen.Return(self.success(data) if data else self.error(u'数据有误', com.ERROR_PARAM))


class PR_Region(ApiHandler):
    @gen.coroutine
    def get(self):
        pid = com.safeInt(self.get_argument('pid', None), -1)
        level = com.safeInt(self.get_argument('level', 0))
        if pid >= 0:
            dists = yield region_model.subDistricts(pid)
        else:
            dists = yield region_model.batchDistricts(level)
        raise gen.Return(self.success(dists) if dists else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_Explain(ApiHandler):
    @gen.coroutine
    def get(self):
        key = self.get_argument('key', '')
        if not key:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        data = yield repair_model.explain(key)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_Machine(ApiHandler):
    @gen.coroutine
    def get(self):
        name = self.get_argument('name', '')
        category_id = self.get_argument('machine_category_id', '')
        data = yield repair_model.machine(name, category_id)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_Accessory(ApiHandler):
    @gen.coroutine
    def get(self):
        model_id = com.safeInt(self.get_argument('model_id', 0))
        if not model_id:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        data = yield repair_model.machine_accessory(model_id)
        raise gen.Return(self.success(data) if data else self.error(u'暂无该设备配件价格数据', com.ERROR_EMPTY_DATA))


class PR_ExpressRoute(ApiHandler):
    @gen.coroutine
    def get(self):
        sn = self.get_argument('exp_sn', '')
        if not sn:
            raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
        ### 只访问数据库
        # data = yield repair_model.exp_route(sn)
        data = None
        ### 直接访问顺丰接口
        if not data:
            info = {
                'tracking_number': sn
            }
            data = yield sf_model.mail_route(info)
            if not data:
                raise gen.Return(self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
            for i in data:
                i['com'] = '顺丰'
                if com.safeInt(i.get('opcode', 0)) in [50,54,43,46]:
                    i['status'] = 1
                elif com.safeInt(i.get('opcode', 0)) in [30,31,130,123,607,36,77]:
                    i['status'] = 2
                elif com.safeInt(i.get('opcode', 0)) in [44,204,125,47,126,658,657,630,664,34]:
                    i['status'] = 3
                elif com.safeInt(i.get('opcode', 0)) in [80,8000]:
                    i['status'] = 4
                else:
                    i['status'] = 0
                yield repair_model.store_exp_route(i)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_ExpressFilter(ApiHandler):
    @gen.coroutine
    def get(self):
        key = [
            'province',
            'city',
            'district',
            'address',
            'd_province',
            'd_city',
            'd_district',
            'd_address',
        ]
        content = {i: self.get_argument(i, '') for i in key}
        ret = dict()
        ret['self'] = True
        data = yield sf_model.filter_order(content)
        if data == 1:
            ret['visit'] = True
        elif data == -1:
            ret['visit'] = False
        else:
            raise gen.Return(self.error(u'参数有误', com.ERROR_PARAM))
        refuse = [u'新疆', u'西藏', u'香港', u'澳门', u'台湾']
        for i in refuse:
            if content['province'].find(i) >= 0:
                ret['self'] = False
                ret['visit'] = False
                break
        # allow = ['上海', '浙江', '福建', '江西', '广东', '湖南', '贵州', '云南', '海南', '湖北', '四川', '江苏']
        # is_allow = False
        # for i in allow:
        #     if content['province'].find(i) >= 0:
        #         is_allow = True
        #         break
        # if not is_allow:
        #     ret['self'] = False
        #     ret['visit'] = False
        raise gen.Return(self.success(ret))


class PR_WXPay(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', '')
        order = yield repair_model.order_info(sn)
        appid = self.appauth.get('appid')
        # print appid
        trade_type = self.get_argument('trade_type', 'APP')
        openid = self.get_argument('openid', '')
        if trade_type == 'JSAPI' and not openid:
            raise gen.Return(self.error(u'JSAPI支付时，openid参数为空', com.ERROR_PARAM))
        if not appid:
            raise gen.Return(self.error(u'应用不可支付', com.ERROR_BAD_STATE))
        if not order or order.get('status') != 500:
            raise gen.Return(self.error(u'订单状态不可支付', com.ERROR_BAD_STATE))
        if order.get('type') == 2:
            raise gen.Return(self.error(u'代理商寄修无法支付', com.ERROR_BAD_STATE))
        order['appid'] = appid
        # print appid
        data = yield repair_model.wxpay(order, trade_type, openid)
        raise gen.Return(self.success(data) if data else self.error(u'支付错误,订单状态不可支付', com.ERROR_BAD_STATE))


class AR_WXPay(ApiHandler):
    @gen.coroutine
    def post(self):
        """
        用户弃修支付
        :return:
        """
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', '')
        order = yield repair_model.order_info(sn)
        appid = self.appauth.get('appid')
        # print appid
        trade_type = self.get_argument('trade_type', 'APP')
        openid = self.get_argument('openid', '')
        if trade_type == 'JSAPI' and not openid:
            raise gen.Return(self.error(u'JSAPI支付时，openid参数为空', com.ERROR_PARAM))
        if not appid:
            raise gen.Return(self.error(u'应用不可支付', com.ERROR_BAD_STATE))
        if not order or order.get('status') != 500:
            raise gen.Return(self.error(u'订单状态不可支付', com.ERROR_BAD_STATE))
        if order['amount_in_ar'] == 0 and order['staff_cast'] == 0:
            raise gen.Return(self.error(u'弃修费用为零, 无需支付', com.ERROR_PARAM))
        order['appid'] = appid
        # print appid
        data = yield repair_model.AR_wxpay(order, trade_type, openid)
        raise gen.Return(self.success(data) if data else self.error(u'支付错误,订单状态不可支付', com.ERROR_BAD_STATE))


class PR_ALIPay(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', '')
        order = yield repair_model.order_info(sn)
        appid = self.appauth.get('appid')
        if not appid:
            raise gen.Return(self.error(u'应用不可支付', com.ERROR_BAD_STATE))
        if not order or order.get('status') != 500:
            raise gen.Return(self.error(u'订单状态不可支付', com.ERROR_BAD_STATE))
        if order.get('type') == 2:
            raise gen.Return(self.error(u'代理商寄修无法支付', com.ERROR_BAD_STATE))
        order['appid'] = appid
        data = yield repair_model.alipay(order)
        raise gen.Return(self.success(data) if data else self.error(u'订单状态不可支付', com.ERROR_BAD_STATE))


class AR_ALIPay(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', '')
        order = yield repair_model.order_info(sn)
        appid = self.appauth.get('appid')
        if not appid:
            raise gen.Return(self.error(u'应用不可支付', com.ERROR_BAD_STATE))
        if not order or order.get('status') != 500:
            raise gen.Return(self.error(u'订单状态不可支付', com.ERROR_BAD_STATE))
        if order['amount_in_ar'] == 0 and order['staff_cast'] == 0:
            raise gen.Return(self.error(u'弃修费用为零, 无需支付', com.ERROR_PARAM))
        order['appid'] = appid
        data = yield repair_model.AR_alipay(order)
        raise gen.Return(self.success(data) if data else self.error(u'订单状态不可支付', com.ERROR_BAD_STATE))


class PR_MachineCategory(ApiHandler):
    @gen.coroutine
    def get(self):
        data = yield repair_model.get_machine_category()
        for key in data:
            if key.get('image'):
                key['image'] = 'https://dt.readboy.com/' + key['image']
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_SelfTestList(ApiHandler):
    @gen.coroutine
    def get(self):
        machine_category_id = com.safeInt(self.get_argument('machine_category_id', 0))
        damage_id = com.safeInt(self.get_argument('damage_id', 0))
        page = com.safeInt(self.get_argument('page', 1))
        count = com.safeInt(self.get_argument('count', 10))
        data = yield repair_model.get_self_test(machine_category_id, damage_id, page, count)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_BAD_STATE))


class PR_SelfTestDamge(ApiHandler):
    @gen.coroutine
    def get(self):
        machine_category_id = com.safeInt(self.get_argument('machine_category_id', 0))
        data = yield repair_model.self_test_damage(machine_category_id)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_BAD_STATE))


class PR_SelfTestDetail(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        st_id = com.safeInt(self.get_argument('id', 0))
        data = dict()
        data['detail'] = yield repair_model.get_self_test_content(st_id)
        if uid:
            check = yield repair_model.check_self_test_appraise(st_id, uid)
            data['appraised'] = True if check else False
        raise gen.Return(self.success(data))


class PR_SelfTestAppraise(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        st_id = com.safeInt(self.get_argument('id', 0))
        helpful = com.safeInt(self.get_argument('helpful', 0))
        appraise = (self.get_argument('appraise', ''))
        data = yield repair_model.self_test_appraise(uid, st_id, helpful, appraise)
        raise gen.Return(self.success(data))

class Ban(ApiHandler):
    @gen.coroutine
    def get(self):
        # uid = yield self.check_uid()
        # if not uid:
        #     raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        data = yield repair_model.ban_repair()
        # 为空则为禁止寄修状态    不为空返回true  即允许寄修
        if not data:
            raise gen.Return(self.success(True))
        else:
            raise gen.Return(self.error(data, com.ERROR_BAD_STATE))


class PR_CancelRepair(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 6
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', None)
        if not sn:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        validate, come_exp_type = yield repair_model.check_order_v1(uid, sn)
        if validate == -1:
            raise gen.Return(self.error(u'权限不足，无法操作', com.ERROR_BAD_STATE))
        if validate not in [100, 200, -300]:
            raise gen.Return(self.error(u'状态不符，无法取消订单', com.ERROR_BAD_STATE))
        if validate == 200 and come_exp_type != 2:
            raise gen.Return(self.error(u'状态不符，无法取消订单', com.ERROR_BAD_STATE))
        data = yield repair_model.cancel_repair(uid, sn)
        raise gen.Return(self.success(True) if data else self.error(u'状态不符，无法取消订单', com.ERROR_BAD_STATE))


class PR_ChangeAddress(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 6
        # print uid
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', '')
        if not sn:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))

        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        refuse = [u'新疆', u'西藏', u'香港', u'澳门', u'台湾']
        if content.get('province') in refuse:
            raise gen.Return(self.error(u'此地区不在寄修范围内，无法修改订单', com.ERROR_BAD_STATE))
        validate = yield repair_model.check_order(uid, sn)
        # print validate
        if validate not in [300, 400, 410, 480, 490, 500, 600, 700]:
            raise gen.Return(self.error(u'状态不符，无法修改订单', com.ERROR_BAD_STATE))
        data = yield repair_model.change_address(sn, content)
        raise gen.Return(self.success(data) if data else self.error(u'系统内部错误，无法修改订单', com.ERROR_BAD_STATE))


class PR_FaultEquipmentInfo(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        # print content['imei'][0]
        array = []
        for i in range(0, len(content['imei'])):
            code = content['imei'][i]
            # machine = yield warranty_model.checkMESV1(code, None, None)
            # # 同步家长助手的绑定设备列表到寄修的设备列表
            # if machine:
            #     check_bind_equip = yield repair_model.check_bind_equip(uid, machine['barcode'])
            #     if not check_bind_equip:
            #         ret = yield repair_model.bind_equip(uid, machine)
            # if not machine or not machine.get('barcode'):
            #     data = dict()
            #     data['imei'] = code
            #     data['has_info'] = False
            #     array.append(data)
            #     continue
            # barcode = machine['barcode']
            # 检查机器是否正在维修中

            # warranty = yield warranty_model.getByBarcode(barcode)
            warranty = yield warranty_model.getByNumber(code)
            barcode = warranty['barcode'] if warranty else None
            in_period = repair_model.in_period(warranty)
            if not warranty:
                machine = yield warranty_model.checkMachineByWarranty(None, barcode, None)
                if not machine:
                    machine = yield warranty_model.checkMESV1(code, None, None)
                if not machine or not machine.get('barcode'):
                    data = dict()
                    data['imei'] = code
                    data['has_info'] = False
                    array.append(data)
                    continue
                warranty = yield warranty_model.checkMachineWear(machine['imei1'])
                if not warranty:
                    data = dict()
                    data['imei'] = code
                    data['has_info'] = False
                    array.append(data)
                    continue
                barcode = warranty['barcode']
                if isinstance(warranty, dict):
                    if warranty.get('buy_date') == '0000-00-00 00:00:00':
                        warranty['buy_date'] = None
                    else:
                        warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'],
                                                                          '%Y-%m-%d %H:%M:%S').strftime(
                            '%Y.%m.%d') if warranty.get('buy_date') else None
                    in_period = repair_model.in_period(warranty)
            # 手表服务器验证
            else:

                if warranty.get('status') == 0:
                    warranty = yield warranty_model.checkMachineWear(code)
                    if isinstance(warranty, dict):
                        if warranty.get('buy_date') == '0000-00-00 00:00:00':
                            warranty['buy_date'] = None
                        else:
                            warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'], '%Y-%m-%d %H:%M:%S').strftime('%Y.%m.%d') if warranty.get('buy_date') else None
                        in_period = repair_model.in_period(warranty)
            check = yield repair_model.check_barcode_order(barcode)
            data = dict()
            data['is_repair'] = 1 if check else 0
            data['barcode'] = barcode
            data['in_period'] = in_period
            if warranty and warranty['buy_date']:
                time = datetime.datetime.strptime(warranty['buy_date'], '%Y.%m.%d').date()+datetime.timedelta(days=365)
                data['period'] = time.strftime('%Y/%m/%d')
            else:
                data['period'] = ''
            data['has_warranty'] = 1 if warranty else 0
            data['imei'] = code
            data['has_info'] = True
            array.append(data)
        raise gen.Return(self.success(array) if array else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_WXOrderQuery(ApiHandler):
    @gen.coroutine
    def get(self):
        # transaction_id = self.get_argument('transaction_id', '')
        out_trade_no = self.get_argument('out_trade_no', '')
        if not out_trade_no:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        appid = self.appauth.get('appid')
        # print appid
        # order_query = yield wxpay_model.query_pay(appid, out_trade_no)
        # print order_query
        # 先查支付订单信息
        pay_order = yield repair_model.pay_order_data(out_trade_no, 1)
        # 支付订单未支付
        if pay_order and pay_order.get('trade_state') != 'SUCCESS' and pay_order.get('pr_sn') and pay_order.get('appid') \
                and pay_order.get('readboy_sn'):
            # 再查寄修单order寄修信息
            order = yield repair_model.order_data(pay_order['pr_sn'], ['status', 'pay_amount'])
            # 如果状态等于500--已检测状态
            if order and order.get('status') == config.ORDER_CHECK:
                # 再查微信支付记录
                order_query = yield wxpay_model.query_pay(appid, out_trade_no)
                # 维修查询已经支付
                if order_query.get('return_code') == 'SUCCESS' and order_query.get('result_code') == 'SUCCESS' and order_query.get(
                        'trade_state') == 'SUCCESS' and order_query.get('total_fee') == str(long(order.get('pay_amount') * 100)):
                    # 修改寄修单和支付订单状态
                    change_data = {
                        'pr_sn': pay_order.get('pr_sn'),
                        'pay_com': pay_order.get('com'),
                        'rb_pay_sn': pay_order.get('readboy_sn'),
                        'is_paid': 1,
                        'status': config.ORDER_PAY,
                        'pay_sn': order_query.get('transaction_id')
                    }
                    # 更新支付订单表和订单表
                    change_state = yield repair_model.order_pay_sure(change_data)

                    raise gen.Return(self.success(change_state) if change_state else self.error(u'查询失败', com.ERROR_SYSTEM))
                else:
                    raise gen.Return(self.error(order_query.get('trade_state_desc'), com.ERROR_PARAM))

            raise gen.Return(self.error(u'订单已支付', com.ERROR_PARAM))
        raise gen.Return(self.error(u'订单已支付或订单号已作废', com.ERROR_PARAM))


# 申请退款
class PR_WXRefund(ApiHandler):
    @gen.coroutine
    def post(self):
        # uid = yield self.check_uid()
        # if not uid:
        #     raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))

        # 获取订单详情
        appid = self.appauth.get('appid')
        sn = self.get_argument('order_sn', '')
        # refund_type = self.get_argument('refund_type', 1)
        refund_fee = Decimal(self.get_argument('refund_fee', 0))

        # 查询订单表
        order = yield repair_model.order_info(sn)
        if refund_fee > order.get('pay_amount'):
            raise gen.Return(self.error(u'退款金额大于支付金额，请确认金额', com.ERROR_PARAM))
        if not appid:
            raise gen.Return(self.error(u'订单不可退款', com.ERROR_BAD_STATE))
        if not order or order.get('status') not in [600, 700, 800, 900, -800, -900]:
            raise gen.Return(self.error(u'订单状态不可退款', com.ERROR_BAD_STATE))
        order['appid'] = appid
        # print appid
        data = yield repair_model.wxrefund(order, refund_fee)
        raise gen.Return(self.success(data) if data else self.error(u'退款错误,订单状态不可退款', com.ERROR_BAD_STATE))


# 申请退款
class PR_ALIRefund(ApiHandler):
    @gen.coroutine
    def post(self):
        # uid = yield self.check_uid()
        # if not uid:
        #     raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))

        # 获取订单详情
        appid = self.appauth.get('appid')
        sn = self.get_argument('order_sn', '')

        refund_fee = com.safeFloat(self.get_argument('refund_fee', 0), 0)

        # 查询订单表
        order = yield repair_model.order_info(sn)

        if refund_fee > order.get('pay_amount'):
            raise gen.Return(self.error(u'退款金额大于支付金额，请确认金额', com.ERROR_PARAM))

        if not appid:
            raise gen.Return(self.error(u'订单不可退款', com.ERROR_BAD_STATE))
        if not order or order.get('status') not in [600, 700, 800, 900, -800, -900]:
            raise gen.Return(self.error(u'订单状态不可退款', com.ERROR_BAD_STATE))
        order['appid'] = appid
        # print appid
        # refund_type = 1 全部退款  2为部分退款
        if refund_fee == order.get('pay_amount'):
            refund_type = 1
        data = yield repair_model.alirefund(order, refund_fee)
        raise gen.Return(self.success(data) if data else self.error(u'退款错误,订单状态不可退款', com.ERROR_BAD_STATE))


class PR_WXOrderRefundQuery(ApiHandler):
    @gen.coroutine
    def get(self):
        # transaction_id = self.get_argument('transaction_id', '')
        order_sn = self.get_argument('order_sn', '')

        out_trade_no = self.get_argument('out_trade_no', '')
        if not out_trade_no:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        appid = self.appauth.get('appid')
        refund_com = 1
        order = yield repair_model.order_data(order_sn, ['status', 'pay_amount'])
        refund_order = yield repair_model.refund_order_data(out_trade_no, refund_com)

        if refund_order and refund_order.get('refund_state') != 'SUCCESS' and refund_order.get(
                'pr_sn') and refund_order.get('appid') and refund_order.get('readboy_sn'):
            wxpay_log.info('refund_order:')
            wxpay_log.info(refund_order)

            wxrefund_order = yield wxpay_model.query_refund(appid, out_trade_no)
            wxpay_log.info('wxrefund_order:')
            wxpay_log.info(wxrefund_order)

            if wxrefund_order.get('return_code') == 'SUCCESS' \
                    and wxrefund_order.get('result_code') == 'SUCCESS' \
                    and wxrefund_order.get('refund_status_0') == 'SUCCESS' \
                    and wxrefund_order.get('refund_fee_0') == str(long(refund_order.get('refund_amount') * 100)):
                # 修改寄修单和支付订单状态
                change_data = {
                    'pr_sn': refund_order.get('pr_sn'),
                    'refund_com': refund_order.get('com'),
                    'rb_refund_sn': refund_order.get('readboy_sn'),
                    'is_refund': 1,
                    'refund_state': wxrefund_order.get('refund_status_0'),
                    'refund_sn': wxrefund_order.get('refund_id_0'),  # 退款单号
                    'pay_amount': order.get('pay_amount'),
                    'refund_fee': refund_order.get('refund_amount')
                }
                change_state = yield repair_model.order_refund_sure(change_data)
                wxpay_log.info('change_state:')
                wxpay_log.info(change_state)
                raise gen.Return(self.success(change_data))
            raise gen.Return(self.error(u'查询失败，请重新查询', com.ERROR_PARAM))
        else:
            change_data = {
                'pr_sn': refund_order.get('pr_sn'),
                'refund_com': refund_order.get('com'),
                'rb_refund_sn': refund_order.get('readboy_sn'),
                'is_refund': 1,
                'refund_state': refund_order.get('refund_state'),
                'refund_sn': refund_order.get('refund_sn'),  # 退款单号
                'pay_amount': order.get('pay_amount'),
                'refund_fee': refund_order.get('refund_amount')
            }
            raise gen.Return(self.success(change_data))


class PR_AliOrderRefundQuery(ApiHandler):
    @gen.coroutine
    def get(self):
        # transaction_id = self.get_argument('transaction_id', '')
        order_sn = self.get_argument('order_sn', '')

        out_trade_no = self.get_argument('out_trade_no', '')
        if not out_trade_no:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        appid = self.appauth.get('appid')
        # alirefund_order = yield alipay_model.query_refund(appid, out_trade_no)

        refund_com = 2
        order = yield repair_model.order_data(order_sn, ['rb_pay_sn', 'status', 'pay_amount'])

        refund_order = yield repair_model.refund_order_data(out_trade_no, refund_com)
        #
        if refund_order and refund_order.get('refund_state') != 'SUCCESS' and refund_order.get(
                'pr_sn') and refund_order.get('appid') and refund_order.get('readboy_sn'):
            alipay_log.info('refund_order:')
            alipay_log.info(refund_order)

            alirefund_order = yield alipay_model.query_refund(appid, order.get('rb_pay_sn'), out_trade_no)
            wxpay_log.info('alirefund_order:')
            wxpay_log.info(alirefund_order)

            if alirefund_order.get('code') == '10000' \
                    and alirefund_order.get('msg') == 'Success' \
                    and alirefund_order.get('refund_amount') == str(refund_order.get('refund_amount')):
                # 修改寄修单和支付订单状态
                change_data = {
                    'pr_sn': refund_order.get('pr_sn'),
                    'refund_com': refund_order.get('com'),
                    'rb_refund_sn': refund_order.get('readboy_sn'),
                    'is_refund': 1,
                    'refund_state': 'SUCCESS',
                    'refund_sn': '',  # 退款单号
                    'pay_amount': order.get('pay_amount'),
                    'refund_fee': refund_order.get('refund_amount')
                }
                change_state = yield repair_model.order_refund_sure(change_data)
                wxpay_log.info('change_state:')
                wxpay_log.info(change_state)
                raise gen.Return(self.success(change_data))
            raise gen.Return(self.error(u'查询失败，请重新查询', com.ERROR_PARAM))
        else:
            change_data = {
                'pr_sn': refund_order.get('pr_sn'),
                'refund_com': refund_order.get('com'),
                'rb_refund_sn': refund_order.get('readboy_sn'),
                'is_refund': 1,
                'refund_state': refund_order.get('refund_state'),
                'refund_sn': refund_order.get('refund_sn'),  # 退款单号
                'pay_amount': order.get('pay_amount'),
                'refund_fee': refund_order.get('refund_amount')
            }
            raise gen.Return(self.success(change_data))

class WXScan(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        url = self.get_argument('url', '')
        if not url:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        ticket = yield repair_model.wx_ticket()
        data = yield repair_model.wx_scan(url, ticket)
        raise gen.Return(self.success(data) if data else self.error(u'签名获取失败', com.ERROR_SYSTEM))


class ALIQuery(ApiHandler):
    @gen.coroutine
    def get(self):
        data = yield alipay_model.query_pay('postrepair_h5', 123)
        # data = json.dumps(data)
        raise gen.Return(self.success(data))


class BindEquip(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        barcode = self.get_argument('barcode', '')
        if not barcode:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        machine = yield warranty_model.checkMachineByWarranty(None, barcode, None)
        if not machine:
            machine = yield warranty_model.checkMESV1(None, barcode, None)
        # print machine
        if not machine:
            raise gen.Return(self.error(u'条码错误，无法绑定设备', com.ERROR_PARAM))
        check_bind_equip = yield repair_model.check_bind_equip(uid, barcode)
        if check_bind_equip:
            raise gen.Return(self.error(u'用户已绑定此机器，无需再绑定', com.ERROR_PARAM))
        data = yield repair_model.bind_equip(uid, machine)
        raise gen.Return(self.success(data) if data else self.error(u'绑定设备失败', com.ERROR_SYSTEM))


class UnbindEquip(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        barcode = self.get_argument('barcode', '')
        if not barcode:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        check_bind_equip = yield repair_model.check_bind_equip(uid, barcode)
        if not check_bind_equip:
            raise gen.Return(self.error(u'此条码未被绑定', com.ERROR_PARAM))
        data = yield repair_model.unbind_equip(uid, barcode)
        raise gen.Return(self.success(data) if data else self.error(u'绑定设备失败', com.ERROR_SYSTEM))


class BindEquipWearList(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        uidStr = self.get_argument('uid', '')
        if not uidStr:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        imeiDicts = yield repair_model.bind_equip_wear_list(uidStr)
        imeiList = []
        if imeiDicts and isinstance(imeiDicts, list) and len(imeiDicts) > 0:
            imeiList = [x.get('imei') for x in imeiDicts]
        datas = []
        if len(imeiList) > 0:
            for vi in imeiList:
                di = {
                    'is_repair': 0,
                    'in_period': 0,
                    'period': '',
                    'has_warranty': 0,
                    'buy_date': '',
                    'endpoint_name': '',
                    'customer_phone': '',
                    'in_si_period': 0,
                    'used_screen_insurance': 0,
                    'has_screen_insurance': 0,
                    'barcode': '',
                    'model': '',
                    'number': '',
                    'imei': vi
                }
                machine = yield warranty_model.checkMachineByWarranty(None, None, vi)
                if not machine:
                    machine = yield warranty_model.checkMESV1(None, None, vi)
                check = yield repair_model.check_imei_order(vi)
                barcode = machine.get('barcode', '') if machine and isinstance(machine, dict) else ''
                model_name = machine.get('model', '') if machine and isinstance(machine, dict) else ''
                if barcode:
                    warranty = yield warranty_model.getByBarcode(barcode)
                    in_period = repair_model.in_period(warranty)
                    period = repair_model.period(warranty)
                    screen_insurance = yield repair_model.get_broken_screen_insurance(barcode)
                    in_si_period, used_screen_insurance = yield repair_model.in_si_period(screen_insurance)
                    di = {
                        'is_repair': 1 if check else 0,
                        'in_period': in_period,
                        'period': period,
                        'has_warranty': warranty,
                        'buy_date': warranty.get('buy_date') if warranty else '',
                        'endpoint_name': warranty.get('endpoint_name') if warranty else '',
                        'customer_phone': warranty.get('customer_phone') if warranty else '',
                        'in_si_period': in_si_period,
                        'used_screen_insurance': used_screen_insurance,
                        'has_screen_insurance': 1 if screen_insurance else 0,
                        'barcode': barcode,
                        'model': warranty.get('model', model_name) if warranty else model_name,
                        'number': warranty.get('number') if warranty else '',
                        'imei': vi
                    }
                datas.append(di)
        ret = {
            'uid': uid,
            'uidStr': uidStr,
            'imeiDicts': imeiDicts,
            'imeiList': imeiList,
            'datas': datas
        }
        raise gen.Return(self.success(datas))


class BindEquipList(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        datas = yield repair_model.bind_equip_list(uid)
        for i in datas:
            # 检查机器是否正在维修中
            barcode = i['barcode']
            check = yield repair_model.check_barcode_order(barcode)
            warranty = yield warranty_model.getByBarcode(barcode)
            in_period = repair_model.in_period(warranty)
            period = repair_model.period(warranty)
            data = dict()
            i['is_repair'] = 1 if check else 0
            # data['barcode'] = barcode
            i['in_period'] = in_period
            i['period'] = period
            i['has_warranty'] = 1 if warranty else 0
            i['buy_date'] = warranty.get('buy_date') if warranty else ''
            i['endpoint_name'] = warranty.get('endpoint_name') if warranty else ''
            i['customer_phone'] = warranty.get('customer_phone') if warranty else ''

            screen_insurance = yield repair_model.get_broken_screen_insurance(barcode)
            in_si_period, used_screen_insurance = yield repair_model.in_si_period(screen_insurance)
            i['in_si_period'] = in_si_period
            i['used_screen_insurance'] = used_screen_insurance
            i['has_screen_insurance'] = 1 if screen_insurance else 0
        raise gen.Return(self.success(datas))


class PR_OrderAbandon(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('order_sn', None)
        if not sn:
            raise gen.Return(self.error(u'订单流水号不能为空', com.ERROR_PARAM))
        order = yield repair_model.order_info(sn)
        if not order:
            raise gen.Return(self.error('未找到订单信息', com.ERROR_PARAM))
        if order['status'] != 500:
            raise gen.Return(self.error('当前状态无法弃修', com.ERROR_PARAM))
        if order['amount_in_ar'] > 0 or order['staff_cast'] > 0:
            raise gen.Return(self.error('请支付弃修费用', com.ERROR_PARAM))

        data = yield repair_model.order_abandon(uid, sn)
        raise gen.Return(self.success(data) if data else self.error('系統出錯', com.ERROR_PARAM))


class PR_Estimate(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        damage_id = self.get_argument("damage_id", None)
        model_id = self.get_argument('model_id', None)
        if not damage_id:
            raise gen.Return(self.error("请选择受损类型", com.ERROR_PARAM))
        if not model_id:
            raise gen.Return(self.error('机型不能为空', com.ERROR_PARAM))
        data = yield repair_model.post_repair_estimate(damage_id, model_id)
        raise gen.Return(self.success(data))
