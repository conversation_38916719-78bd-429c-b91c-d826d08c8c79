# coding=utf-8

from conf.config import WXPAY_APPID, WXPAY_APPNAME, WXPAY_NOTIFY_URL, WXREFUND_NOTIFY_URL, AR_WXPAY_NOTIFY_URL
import tornado
from tornado import gen, httpclient
from tornado.log import app_log
from log import wxpay_log
import traceback
from conf.config import SF_HEAD, SF_CHECKCODE, SF_CUSTID, SF_VERIFYCODE
import datetime
from store import DB_PR, DB
import xmltodict
import urllib
from util import com
import hashlib, base64
import json
import time
import random
import urllib2
import threading
from urllib import quote
# import xml.etree.ElementTree as ET
import lxml.etree as ET
import sys
reload(sys)
sys.setdefaultencoding( "utf-8" )

HTTP_TIMEOUT = 5
MAX_CLIENTS = 1000


httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)

"""
Created on 2014-11-24
@author: http://blog.csdn.net/yueguanghaidao
 * 微信支付帮助库
 * ====================================================
 * 接口分三种类型：
 * 【请求型接口】--Wxpay_client_
 *     统一支付接口类--UnifiedOrder
 *     订单查询接口--OrderQuery
 *     退款申请接口--Refund
 *     退款查询接口--RefundQuery
 *     对账单接口--DownloadBill
 *     短链接转换接口--ShortUrl
 * 【响应型接口】--Wxpay_server_
 *     通用通知接口--Notify
 *     Native支付——请求商家获取商品信息接口--NativeCall
 * 【其他】
 *     静态链接二维码--NativeLink
 *     JSAPI支付--JsApi
 * =====================================================
 * 【CommonUtil】常用工具：
 *     trimString()，设置参数时需要用到的字符处理函数
 *     createNoncestr()，产生随机字符串，不长于32位
 *     formatBizQueryParaMap(),格式化参数，签名过程需要用到
 *     getSign(),生成签名
 *     arrayToXml(),array转xml
 *     xmlToArray(),xml转 array
 *     postXmlCurl(),以post方式提交xml到对应的接口url
 *     postXmlSSLCurl(),使用证书，以post方式提交xml到对应的接口url
"""

create_resp = {'trade_type': 'APP', 'prepay_id': 'wx1410443278819875b4ef43ff1933158800', 'nonce_str': 'IAgvHKOH1UfIaWDY', 'return_code': 'SUCCESS', 'return_msg': 'OK', 'sign': '8FBB765EC9D7C02E5484E0BD1619DCE3', 'mch_id': '**********', 'appid': 'wx1ddb8aacc25dcf08', 'result_code': 'SUCCESS'}
query_resp = {'trade_state': 'NOTPAY', 'nonce_str': 'O0p2fHuV5hQZfouD', 'device_info': None, 'return_msg': 'OK', 'return_code': 'SUCCESS', 'mch_id': '**********', 'out_trade_no': '*********', 'trade_state_desc': u'\u8ba2\u5355\u672a\u652f\u4ed8', 'total_fee': '1', 'appid': 'wx1ddb8aacc25dcf08', 'sign': 'BC11D5594AF65835B76805AED21D3F2D', 'result_code': 'SUCCESS'}
notify_input = """
<xml>
  <appid><![CDATA[wx2421b1c4370ec43b]]></appid>
  <attach><![CDATA[支付测试]]></attach>
  <bank_type><![CDATA[CFT]]></bank_type>
  <fee_type><![CDATA[CNY]]></fee_type>
  <is_subscribe><![CDATA[Y]]></is_subscribe>
  <mch_id><![CDATA[********]]></mch_id>
  <nonce_str><![CDATA[5d2b6c2a8db53831f7eda20af46e531c]]></nonce_str>
  <openid><![CDATA[oUpF8uMEb4qRXf22hE3X68TekukE]]></openid>
  <out_trade_no><![CDATA[**********]]></out_trade_no>
  <result_code><![CDATA[SUCCESS]]></result_code>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <sign><![CDATA[B552ED6B279343CB493C5DD0D78AB241]]></sign>
  <sub_mch_id><![CDATA[********]]></sub_mch_id>
  <time_end><![CDATA[**************]]></time_end>
  <total_fee>1</total_fee><coupon_fee><![CDATA[10]]></coupon_fee>
<coupon_count><![CDATA[1]]></coupon_count>
<coupon_type><![CDATA[CASH]]></coupon_type>
<coupon_id><![CDATA[10000]]></coupon_id>
<coupon_fee_0><![CDATA[100]]></coupon_fee_0>
  <trade_type><![CDATA[JSAPI]]></trade_type>
  <transaction_id><![CDATA[1004400740201409030005092168]]></transaction_id>
</xml>
"""


class WxPayConf_pub(object):
    """配置账号信息"""
    #=======【基本信息设置】=====================================
    #微信公众号身份的唯一标识。审核通过后，在微信发送的邮件中查看
    APPID = "wx1ddb8aacc25dcf08"
    #JSAPI接口中获取openid，审核后在公众平台开启开发模式后可查看
    APPSECRET = "5e4bf84a9c2b4a0471b5679712031c4c"
    #受理商ID，身份标识
    MCHID = "**********"
    #商户支付密钥Key。审核通过后，在微信发送的邮件中查看
    KEY = "xHZKBP2EwiBtdjjkVSOvPei23vPAdsvf"
    #=======【异步通知url设置】===================================
    #异步通知url，商户根据实际开发过程设定
    NOTIFY_URL = "https://pay-repair-hub.readboy.com/callback/wxpay"
    #=======【JSAPI路径设置】===================================
    #获取access_token过程中的跳转uri，通过跳转将code传入jsapi支付页面
    JS_API_CALL_URL = "http://******.com/pay/?showwxpaytitle=1"
    #=======【证书路径设置】=====================================
    #证书路径,注意应该填写绝对路径
    # SSLCERT_PATH = "D:/pythonProject/post_repair/cert/apiclient_cert.pem"
    # SSLKEY_PATH = "D:/pythonProject/post_repair/cert/apiclient_key.pem"
    SSLCERT_PATH = "/data/www/post_repair/cert/apiclient_cert.pem"
    SSLKEY_PATH = "/data/www/post_repair/cert/apiclient_key.pem"
    #=======【curl超时设置】===================================
    CURL_TIMEOUT = 30
    #=======【HTTP客户端设置】===================================
    HTTP_CLIENT = "CURL" # ("URLLIB", "CURL")

class Common_util_pub(object):
    """所有接口的基类"""
    def trimString(self, value):
        if value is not None and len(value) == 0:
            value = None
        return value
    def createNoncestr(self, length = 32):
        """产生随机字符串，不长于32位"""
        chars = "abcdefghijklmnopqrstuvwxyz0*********"
        strs = []
        for x in range(length):
            strs.append(chars[random.randrange(0, len(chars))])
        return "".join(strs)
    def formatBizQueryParaMap(self, paraMap, urlencode):
        """格式化参数，签名过程需要使用"""
        slist = sorted(paraMap)
        buff = []
        for k in slist:
            v = quote(paraMap[k]) if urlencode else paraMap[k]
            buff.append("{0}={1}".format(k, v))
        return "&".join(buff)
    def getSign(self, obj):
        """生成签名"""
        #签名步骤一：按字典序排序参数,formatBizQueryParaMap已做
        String = self.formatBizQueryParaMap(obj, False)
        #签名步骤二：在string后加入KEY
        String = "{0}&key={1}".format(String,WxPayConf_pub.KEY)
        #签名步骤三：MD5加密
        String = hashlib.md5(String).hexdigest()
        #签名步骤四：所有字符转为大写
        result_ = String.upper()
        return result_
    def arrayToXml(self, arr):
        """array转xml"""
        xml = ["<xml>"]
        for k, v in arr.iteritems():
            if v.isdigit():
                xml.append("<{0}>{1}</{0}>".format(k, v))
            else:
                xml.append("<{0}><![CDATA[{1}]]></{0}>".format(k, v))
        xml.append("</xml>")
        return "".join(xml)
    def xmlToArray(self, xml):
        """将xml转为array"""
        array_data = {}
        root = ET.fromstring(xml, ET.XMLParser(resolve_entities=False))
        for child in root:
            value = child.text
            array_data[child.tag] = value
        return array_data

class Wxpay_client_pub(object):
    """请求型接口的基类"""
    response = None #微信返回的响应
    url = None #接口链接
    curl_timeout = None #curl超时时间
    def __init__(self):
        self.parameters = {} #请求参数，类型为关联数组
        self.result = {}     #返回参数，类型为关联数组
    def setParameter(self, parameter, parameterValue):
        """设置请求参数"""
        self.parameters[self.trimString(parameter)] = self.trimString(parameterValue)
    def createXml(self):
        """设置标配的请求参数，生成签名，生成接口参数xml"""
        # self.parameters["appid"] = WxPayConf_pub.APPID  #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID  #商户号
        self.parameters["nonce_str"] = self.createNoncestr()  #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters)  #签名
        return self.arrayToXml(self.parameters)

    @gen.coroutine
    def postXml(self):
        """post请求xml"""
        ret = None
        xml = self.createXml()
        request = httpclient.HTTPRequest(self.url, method='POST', connect_timeout=HTTP_TIMEOUT, body=xml,
                                         request_timeout=HTTP_TIMEOUT, validate_cert=False)
        client = httpclient.AsyncHTTPClient()
        try:
            data = yield client.fetch(request)
            wxpay_log.info("fetch:%s" % data.body)
            ret = self.xmlToArray(data.body)
            self.response = data.body
            self.result = ret
        except:
            app_log.error('error', exc_info=True)
            wxpay_log.error('post url error: url=%s' % self.url)
        raise gen.Return(ret)

    @gen.coroutine
    def postXmlSSL(self):
        """使用证书post请求xml"""
        ret = None
        xml = self.createXml()
        # print xml
        request = httpclient.HTTPRequest(self.url, method='POST', connect_timeout=HTTP_TIMEOUT, body=xml,
                                         request_timeout=HTTP_TIMEOUT, validate_cert=False,
                                         client_key=WxPayConf_pub.SSLKEY_PATH, client_cert=WxPayConf_pub.SSLCERT_PATH)
        client = httpclient.AsyncHTTPClient()
        try:
            data = yield client.fetch(request)
            wxpay_log.info("fetch:%s" % data.body)
            ret = self.xmlToArray(data.body)
            self.response = data.body
            self.result = ret
            # print self.result
            # print ret
        except:
            wxpay_log.error('post url error: url=%s' % self.url)
            app_log.error('error', exc_info=True)
        raise gen.Return(ret)

    @gen.coroutine
    def getResult(self):
        """获取结果，默认不使用证书"""
        yield self.postXml()
        raise gen.Return(self.result)

    def trimString(self, value):
        if value is not None and len(value) == 0:
            value = None
        return value
    def createNoncestr(self, length = 32):
        """产生随机字符串，不长于32位"""
        chars = "abcdefghijklmnopqrstuvwxyz0*********"
        strs = []
        for x in range(length):
            strs.append(chars[random.randrange(0, len(chars))])
        return "".join(strs)
    def formatBizQueryParaMap(self, paraMap, urlencode):
        """格式化参数，签名过程需要使用"""
        slist = sorted(paraMap)
        buff = []
        for k in slist:
            v = quote(paraMap[k]) if urlencode else paraMap[k]
            buff.append("{0}={1}".format(k, v))
        return "&".join(buff)
    def getSign(self, obj):
        """生成签名"""
        #签名步骤一：按字典序排序参数,formatBizQueryParaMap已做
        String = self.formatBizQueryParaMap(obj, False)
        #签名步骤二：在string后加入KEY
        String = "{0}&key={1}".format(String,WxPayConf_pub.KEY)
        #签名步骤三：MD5加密
        String = hashlib.md5(String).hexdigest()
        #签名步骤四：所有字符转为大写
        result_ = String.upper()
        return result_
    def arrayToXml(self, arr):
        """array转xml"""
        xml = ["<xml>"]
        for k, v in arr.iteritems():
            if v.isdigit():
                xml.append("<{0}>{1}</{0}>".format(k, v))
            else:
                xml.append("<{0}><![CDATA[{1}]]></{0}>".format(k, v))
        xml.append("</xml>")
        return "".join(xml)
    def xmlToArray(self, xml):
        """将xml转为array"""
        array_data = {}
        root = ET.fromstring(xml, ET.XMLParser(resolve_entities=False))
        for child in root:
            value = child.text
            array_data[child.tag] = value
        return array_data

class UnifiedOrder_pub(Wxpay_client_pub):
    """统一支付接口类"""
    def __init__(self, timeout=WxPayConf_pub.CURL_TIMEOUT):
        #设置接口链接
        self.url = "https://api.mch.weixin.qq.com/pay/unifiedorder"
        #设置curl超时时间
        self.curl_timeout = timeout
        super(UnifiedOrder_pub, self).__init__()
    def createXml(self):
        """生成接口参数xml"""
        #检测必填参数
        if any(self.parameters[key] is None for key in ("out_trade_no", "body", "total_fee", "notify_url", "trade_type", 'appid')):
            raise ValueError("missing parameter")
        if self.parameters["trade_type"] == "JSAPI" and self.parameters["openid"] is None:
            raise ValueError("JSAPI need openid parameters")
        # self.parameters["appid"] = WxPayConf_pub.APPID #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID #商户号
        self.parameters["spbill_create_ip"] = "127.0.0.1" #终端ip
        self.parameters["nonce_str"] = self.createNoncestr() #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters) #签名
        return self.arrayToXml(self.parameters)

    @gen.coroutine
    def getPrepayId(self):
        """获取prepay_id"""
        yield self.postXml()
        prepay_id = self.result.get("prepay_id")
        raise gen.Return(prepay_id)

    @gen.coroutine
    def getPrepayIdAndUrl(self):
        """获取prepay_id"""
        yield self.postXml()
        prepay_id = self.result.get("prepay_id")
        mweb_url = self.result.get("mweb_url")
        raise gen.Return([prepay_id, mweb_url])

    @gen.coroutine
    def getPrepayIdAndCodeUrl(self):
        """获取prepay_id和二维码code_url"""
        yield self.postXml()
        prepay_id = self.result.get("prepay_id")
        code_url = self.result.get("code_url")
        raise gen.Return([prepay_id, code_url])

class OrderQuery_pub(Wxpay_client_pub):
    """订单查询接口"""
    def __init__(self, timeout=WxPayConf_pub.CURL_TIMEOUT):
        #设置接口链接
        self.url = "https://api.mch.weixin.qq.com/pay/orderquery"
        #设置curl超时时间
        self.curl_timeout = timeout
        super(OrderQuery_pub, self).__init__()
    def createXml(self):
        """生成接口参数xml"""
        #检测必填参数
        if any(self.parameters[key] is None for key in ("out_trade_no", "appid")):
            raise ValueError("missing parameter")
        # self.parameters["appid"] = WxPayConf_pub.APPID #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID #商户号
        self.parameters["nonce_str"] = self.createNoncestr() #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters) #签名
        return self.arrayToXml(self.parameters)
class CloseOrder_pub(Wxpay_client_pub):
    """关闭订单"""
    def __init__(self, timeout=WxPayConf_pub.CURL_TIMEOUT):
        #设置接口链接
        self.url = "https://api.mch.weixin.qq.com/pay/closeorder"
        #设置curl超时时间
        self.curl_timeout = timeout
        super(CloseOrder_pub, self).__init__()
    def createXml(self):
        """生成接口参数xml"""
        #检测必填参数
        if any(self.parameters[key] is None for key in ("out_trade_no", "appid")):
            raise ValueError("missing parameter")
        # self.parameters["appid"] = WxPayConf_pub.APPID #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID #商户号
        self.parameters["nonce_str"] = self.createNoncestr() #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters) #签名
        return self.arrayToXml(self.parameters)
class Refund_pub(Wxpay_client_pub):
    """退款申请接口"""
    def __init__(self, timeout=WxPayConf_pub.CURL_TIMEOUT):
        #设置接口链接
        self.url = "https://api.mch.weixin.qq.com/secapi/pay/refund"
        #设置curl超时时间
        self.curl_timeout = timeout
        super(Refund_pub, self).__init__()
    def createXml(self):
        """生成接口参数xml"""
        if any(self.parameters[key] is None for key in ("out_trade_no", "out_refund_no", "total_fee", "refund_fee")):
            raise ValueError("missing parameter")
        self.parameters["appid"] = WxPayConf_pub.APPID #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID #商户号
        self.parameters["nonce_str"] = self.createNoncestr() #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters) #签名
        return self.arrayToXml(self.parameters)

    @gen.coroutine
    def getResult(self):
        """ 获取结果，使用证书通信(需要双向证书)"""
        yield self.postXmlSSL()
        # print ret.get('result_code')
        # print self.result
        raise gen.Return(self.result)

class RefundQuery_pub(Wxpay_client_pub):
    """退款查询接口"""
    def __init__(self, timeout=WxPayConf_pub.CURL_TIMEOUT):
        #设置接口链接
        self.url = "https://api.mch.weixin.qq.com/pay/refundquery"
        #设置curl超时时间
        self.curl_timeout = timeout
        super(RefundQuery_pub, self).__init__()
    def createXml(self):
        """生成接口参数xml"""
        if any(self.parameters[key] is None for key in ("out_refund_no",)):
            raise ValueError("missing parameter")
        self.parameters["appid"] = WxPayConf_pub.APPID #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID #商户号
        self.parameters["nonce_str"] = self.createNoncestr() #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters) #签名
        return self.arrayToXml(self.parameters)
    @gen.coroutine
    def getResult(self):
        """ 获取结果，使用证书通信(需要双向证书)"""
        yield self.postXmlSSL()
        # self.result = self.xmlToArray(self.response)
        raise gen.Return(self.result)
class DownloadBill_pub(Wxpay_client_pub):
    """对账单接口"""
    def __init__(self, timeout=WxPayConf_pub.CURL_TIMEOUT):
        #设置接口链接
        self.url = "https://api.mch.weixin.qq.com/pay/downloadbill"
        #设置curl超时时间
        self.curl_timeout = timeout
        super(DownloadBill_pub, self).__init__()
    def createXml(self):
        """生成接口参数xml"""
        if any(self.parameters[key] is None for key in ("bill_date", )):
            raise ValueError("missing parameter")
        self.parameters["appid"] = WxPayConf_pub.APPID #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID #商户号
        self.parameters["nonce_str"] = self.createNoncestr() #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters) #签名
        return self.arrayToXml(self.parameters)
    def getResult(self):
        """获取结果，默认不使用证书"""
        self.postXml()
        self.result = self.xmlToArray(self.response)
        return self.result
class ShortUrl_pub(Wxpay_client_pub):
    """短链接转换接口"""
    def __init__(self, timeout=WxPayConf_pub.CURL_TIMEOUT):
        #设置接口链接
        self.url = "https://api.mch.weixin.qq.com/tools/shorturl"
        #设置curl超时时间
        self.curl_timeout = timeout
        super(ShortUrl_pub, self).__init__()
    def createXml(self):
        """生成接口参数xml"""
        if any(self.parameters[key] is None for key in ("long_url", )):
            raise ValueError("missing parameter")
        self.parameters["appid"] = WxPayConf_pub.APPID #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID #商户号
        self.parameters["nonce_str"] = self.createNoncestr() #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters) #签名
        return self.arrayToXml(self.parameters)
    def getShortUrl(self):
        """获取prepay_id"""
        self.postXml()
        prepay_id = self.result["short_url"]
        return prepay_id
class Wxpay_server_pub(object):
    """响应型接口基类"""
    SUCCESS, FAIL = "SUCCESS", "FAIL"
    def __init__(self):
        self.data = {} #接收到的数据，类型为关联数组
        self.returnParameters = {} #返回参数，类型为关联数组
    def saveData(self, xml):
        """将微信的请求xml转换成关联数组，以方便数据处理"""
        self.data = self.xmlToArray(xml)
    def checkSign(self):
        """校验签名"""
        tmpData = dict(self.data) #make a copy to save sign
        del tmpData['sign']
        sign = self.getSign(tmpData) #本地签名
        if self.data['sign'] == sign:
            return True
        return False
    def getData(self):
        """获取微信的请求数据"""
        return self.data
    def setReturnParameter(self, parameter, parameterValue):
        """设置返回微信的xml数据"""
        self.returnParameters[self.trimString(parameter)] = self.trimString(parameterValue)
    def createXml(self):
        """生成接口参数xml"""
        return self.arrayToXml(self.returnParameters)
    def returnXml(self):
        """将xml数据返回微信"""
        returnXml = self.createXml()
        return returnXml
    def trimString(self, value):
        if value is not None and len(value) == 0:
            value = None
        return value
    def createNoncestr(self, length = 32):
        """产生随机字符串，不长于32位"""
        chars = "abcdefghijklmnopqrstuvwxyz0*********"
        strs = []
        for x in range(length):
            strs.append(chars[random.randrange(0, len(chars))])
        return "".join(strs)
    def formatBizQueryParaMap(self, paraMap, urlencode):
        """格式化参数，签名过程需要使用"""
        slist = sorted(paraMap)
        buff = []
        for k in slist:
            v = quote(paraMap[k]) if urlencode else paraMap[k]
            buff.append("{0}={1}".format(k, v))
        return "&".join(buff)
    def getSign(self, obj):
        """生成签名"""
        #签名步骤一：按字典序排序参数,formatBizQueryParaMap已做
        String = self.formatBizQueryParaMap(obj, False)
        #签名步骤二：在string后加入KEY
        String = "{0}&key={1}".format(String,WxPayConf_pub.KEY)
        #签名步骤三：MD5加密
        String = hashlib.md5(String).hexdigest()
        #签名步骤四：所有字符转为大写
        result_ = String.upper()
        return result_
    def arrayToXml(self, arr):
        """array转xml"""
        xml = ["<xml>"]
        for k, v in arr.iteritems():
            if v.isdigit():
                xml.append("<{0}>{1}</{0}>".format(k, v))
            else:
                xml.append("<{0}><![CDATA[{1}]]></{0}>".format(k, v))
        xml.append("</xml>")
        return "".join(xml)
    def xmlToArray(self, xml):
        """将xml转为array"""
        array_data = {}
        root = ET.fromstring(xml, ET.XMLParser(resolve_entities=False))
        for child in root:
            value = child.text
            array_data[child.tag] = value
        return array_data
class Notify_pub(Wxpay_server_pub):
    """通用通知接口"""
class NativeCall_pub(Wxpay_server_pub):
    """请求商家获取商品信息接口"""
    def createXml(self):
        """生成接口参数xml"""
        if self.returnParameters["return_code"] == self.SUCCESS:
            self.returnParameters["appid"] = WxPayConf_pub.APPID #公众账号ID
            self.returnParameters["mch_id"] = WxPayConf_pub.MCHID #商户号
            self.returnParameters["nonce_str"] = self.createNoncestr() #随机字符串
            self.returnParameters["sign"] = self.getSign(self.returnParameters) #签名
        return self.arrayToXml(self.returnParameters)
    def getProductId(self):
        """获取product_id"""
        product_id = self.data["product_id"]
        return product_id
class NativeLink_pub(Common_util_pub):
    """静态链接二维码"""
    url = None #静态链接
    def __init__(self):
        self.parameters = {} #静态链接参数
    def setParameter(self, parameter, parameterValue):
        """设置参数"""
        self.parameters[self.trimString(parameter)] = self.trimString(parameterValue)
    def createLink(self):
        if any(self.parameters[key] is None for key in ("product_id", )):
            raise ValueError("missing parameter")
        self.parameters["appid"] = WxPayConf_pub.APPID #公众账号ID
        self.parameters["mch_id"] = WxPayConf_pub.MCHID #商户号
        time_stamp = int(time.time())
        self.parameters["time_stamp"] = "{0}".format(time_stamp) #时间戳
        self.parameters["nonce_str"] = self.createNoncestr() #随机字符串
        self.parameters["sign"] = self.getSign(self.parameters) #签名
        bizString = self.formatBizQueryParaMap(self.parameters, False)
        self.url = "weixin://wxpay/bizpayurl?"+bizString
    def getUrl(self):
        """返回链接"""
        self.createLink()
        return self.url


# def test():
#     order = UnifiedOrder_pub()
#     order.setParameter('out_trade_no', '*********')
#     order.setParameter('body', '家长助手-寄修费用')
#     order.setParameter('total_fee', '1')
#     order.setParameter('trade_type', 'APP')
#     order.setParameter('notify_url', 'https://pay-repair-hub.readboy.com')
#     print order.getPrepayId()


@gen.coroutine
def create_pay(appid, out_trade_no, total_fee, trade_type, openid, notify_url_type):
    order = UnifiedOrder_pub()
    order.setParameter('appid', WXPAY_APPID.get(appid, ''))
    order.setParameter('out_trade_no', str(out_trade_no))
    order.setParameter('body', '-'.join([WXPAY_APPNAME.get(appid, '读书郎寄修服务中心'), "寄修费用"]))
    order.setParameter('total_fee', str(com.safeInt(total_fee*100)))
    order.setParameter('trade_type', trade_type)
    order.parameters["mch_id"] = WxPayConf_pub.MCHID
    # print order.parameters["appid"]
    mweb_url = None
    if trade_type == 'JSAPI':
        order.setParameter('openid', openid)

    if trade_type == 'MWEB':
        order.setParameter('notify_url', "h5-repair-hub.readboy.com")
        prepayid, mweb_url = yield order.getPrepayIdAndUrl()
    else:
        if notify_url_type == 1:
            order.setParameter('notify_url', AR_WXPAY_NOTIFY_URL)
        else:
            order.setParameter('notify_url', WXPAY_NOTIFY_URL)
        if trade_type in ['JSAPI', 'APP']:
            prepayid = yield order.getPrepayId()
        elif trade_type == 'NATIVE':
            order.setParameter('product_id', str(out_trade_no))
            prepayid, code_url = yield order.getPrepayIdAndCodeUrl()

    if not prepayid:
        wxpay_log.error('create pay error data=%s' % order.result)
        raise gen.Return(None)
    data = {
        'appid': WXPAY_APPID.get(appid, ''),
        'partnerid': WxPayConf_pub.MCHID,
        'prepayid': prepayid,
        'package': 'Sign=WXPay',
        'noncestr': Common_util_pub().createNoncestr(),
        'timestamp': int(time.time()),
    }
    sign = Common_util_pub().getSign(data)
    # print sign
    data['sign'] = sign
    if trade_type == 'MWEB':
        data['mweb_url'] = mweb_url
    elif trade_type == 'NATIVE':
        data['code_url'] = code_url
    raise gen.Return(data)

@gen.coroutine
def create_refund(appid, out_trade_no, out_refund_no, total_fee, refund_fee):
    order = Refund_pub()
    order.setParameter('appid', WXPAY_APPID.get(appid, ''))
    order.setParameter('out_trade_no', str(out_trade_no))   # 订单号
    order.setParameter('out_refund_no', str(out_refund_no))  # 退款单号
    order.setParameter('refund_desc', '-'.join([WXPAY_APPNAME.get(appid, '读书郎寄修服务中心'), "寄修退款"]))
    order.setParameter('total_fee', str(com.safeInt(total_fee*100)))    # 支付金额
    order.setParameter('refund_fee', str(com.safeInt(refund_fee * 100)))    # 退款金额
    order.parameters["mch_id"] = WxPayConf_pub.MCHID
    # print order.parameters["appid"]

    order.setParameter('notify_url', WXREFUND_NOTIFY_URL)
    refund_ret = yield order.getResult()
    # print refund_ret
    if not refund_ret:
        wxpay_log.error('create refund error data=%s' % order.result)
        raise gen.Return(None)
    if refund_ret.get('return_code') == 'SUCCESS' and refund_ret.get('result_code') == 'SUCCESS':
        raise gen.Return(refund_ret.get('refund_id'))
    raise gen.Return(None)

@gen.coroutine
def query_pay(appid, readboy_sn):
    order = OrderQuery_pub()
    order.setParameter('appid', WXPAY_APPID.get(appid, ''))
    order.setParameter('out_trade_no', readboy_sn)
    result = yield order.getResult()
    raise gen.Return(result)

@gen.coroutine
def query_refund(appid, readboy_sn):
    order = RefundQuery_pub()
    order.setParameter('appid', WXPAY_APPID.get(appid, ''))
    order.setParameter('out_refund_no', readboy_sn)
    result = yield order.getResult()
    raise gen.Return(result)


@gen.coroutine
def close_pay(appid, readboy_sn):
    order = CloseOrder_pub()
    order.setParameter('appid', WXPAY_APPID.get(appid, ''))
    order.setParameter('out_trade_no', readboy_sn)
    result = yield order.getResult()
    raise gen.Return(result)


def notify(xml):
    # xml = notify_input
    parser = Notify_pub()
    parser.saveData(xml)
    wxpay_log.info(parser.getData().get('sign'))
    wxpay_log.info(parser.getSign(parser.getData()))
    if parser.checkSign():
        ret = True
    else:
        ret = False
    return ret

def notify_return(status):
    """
    推送接口返回xml
    :param status: 成功还是失败
    :return:
    """
    if status:
        parser = Wxpay_server_pub()
        parser.setReturnParameter('return_code', parser.SUCCESS)
        parser.setReturnParameter('return_msg', 'OK')
        ret =  parser.returnXml()
    else:
        parser = Wxpay_server_pub()
        parser.setReturnParameter('return_code', parser.FAIL)
        parser.setReturnParameter('return_msg', 'OK')
        ret =  parser.returnXml()
    return ret


def get_notify_data(xml):
    """
    获取推送通知xml中的数据
    :param xml: xml字符串
    :return: json object
    """
    parser = Notify_pub()
    parser.saveData(xml)
    ret = parser.getData()
    return ret


def main():
    # create_pay()
    # query_pay()
    # close_pay()
    notify()


if __name__ == "__main__":
    t = time.time()
    # test()
    main()
    print time.time() - t