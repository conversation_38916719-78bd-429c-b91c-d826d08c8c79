# coding=utf-8

from tornado import gen

from store import DB


@gen.coroutine
def subDistricts(pid=0):
    """
    获取地区  但是是获取rbcare的
    :param pid:
    :return:
    """
    cur = yield DB.execute('Select region_id, parent_id, region_name, shortname, region_type, sort from region where parent_id=%s and deleted_at is NULL order by sort asc', pid)
    dists = cur.fetchall()
    raise gen.Return(dists)


@gen.coroutine
def batchDistricts(level=0):
    """
    获取地区  但是是获取rbcare的
    :param level:
    :return:
    """
    cur = yield DB.execute('Select region_id, parent_id, region_name, shortname, region_type, sort from region where region_type<=%s and deleted_at is NULL', level)
    dists = cur.fetchall()
    raise gen.Return(dists)


@gen.coroutine
def regions():
    cur = yield DB.execute('select id, name from partition')
    regions = cur.fetchall()
    raise gen.Return(regions)


@gen.coroutine
def agencies():
    cur = yield DB.execute('select id, name, pid, `level`, `partition`, `order` from agency')
    data = cur.fetchall()
    raise gen.Return(data)