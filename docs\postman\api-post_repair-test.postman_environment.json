{"id": "4dff01c5-fa81-4152-b856-0a34877f1480", "name": "api-post_repair-test", "values": [{"key": "protocol", "value": "https://", "type": "default", "enabled": true}, {"key": "urlhead", "value": "api-repair-test.readboy.com", "type": "default", "enabled": true}, {"key": "app_secret", "value": "cdea87bca1f9496c0c4c9f65ba88a988", "type": "default", "enabled": true}, {"key": "ua", "value": "1/PostmanRuntime/desktop/postrepair_h5/1.0/{{$guid}}", "type": "default", "enabled": true}, {"key": "timestamp-last", "value": "", "type": "any", "enabled": true}, {"key": "sn", "value": "", "type": "any", "enabled": true}, {"key": "access_token", "value": "testtoken", "type": "default", "enabled": true}, {"key": "k3cloud_url_base", "value": "http://192.168.16.251/K3Cloud/index.php", "type": "default", "enabled": true}, {"key": "k3cloud_key", "value": "K3CloudJX", "type": "default", "enabled": true}, {"key": "k3cloud_secret", "value": "M4S8tU45OBBvIUN7", "type": "default", "enabled": true}, {"key": "k3cloud_auth_key", "value": "", "type": "any", "enabled": true}, {"key": "mes_key", "value": "Web", "type": "default", "enabled": true}, {"key": "mes_secret", "value": "M4S8tUB8OBBvIUN7", "type": "default", "enabled": true}, {"key": "repair-order_sn-last", "value": "", "type": "default", "enabled": true}, {"key": "repair-barcode-last", "value": "", "type": "default", "enabled": true}, {"key": "repair-order-last", "value": "", "type": "default", "enabled": true}, {"key": "repair-phone", "value": "", "type": "default", "enabled": true}, {"key": "repair-endpoint-id", "value": "2", "type": "default", "enabled": true}, {"key": "repair-endpoint-agent_order_sn", "value": "", "type": "default", "enabled": true}, {"key": "repair-endpoint-order_sn", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2022-06-12T01:54:13.824Z", "_postman_exported_using": "Postman/9.19.0"}