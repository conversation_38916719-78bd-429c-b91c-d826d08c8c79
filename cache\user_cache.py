# coding=utf-8
# __author__=lch

try:
    import simplejson as json
except:
    import json
import time
import util.com as com

from cache import Cache
from conf import config


TOKEN_EXPIRE = 'pr_token_expire'
TOKEN_UID = 'pr_token_uid'


def setAuth(uid, token, expire):
    tokens = Cache.zrangebyscore(TOKEN_UID, uid, uid)
    if tokens:
        delAuthTokenBatch(tokens)
    pl = Cache.pipeline()
    # redis 2.10.5 zadd 方法是要两个值,新版才是传mapping
    pl.zadd(TOKEN_EXPIRE, expire, token)
    pl.zadd(TOKEN_UID, uid, token)
    # mapping = {
    #     token: expire
    # }
    # mapping2 = {
    #     token: uid
    # }
    # pl.zadd(TOKEN_EXPIRE, mapping)
    # pl.zadd(TOKEN_UID, mapping2)
    pl.execute()

def getAuth(uid):
    tokens = Cache.zrangebyscore(TOKEN_UID, uid, uid)
    if tokens:
        token = tokens[0]
        expire = Cache.zscore(TOKEN_EXPIRE, token)
        return {'access_token': token, 'access_expire': expire}
    return None


def checkAuth(token):
    if config.DEBUG and token=='testtoken':
        return {'uid': 1, 'access_token': 'testtoken', 'access_expire': com.safeInt(time.time()) + 3600}
    pl = Cache.pipeline()
    pl.zscore(TOKEN_UID, token)
    pl.zscore(TOKEN_EXPIRE, token)
    uid, expire = pl.execute()
    if uid and expire:
        return {'uid': int(uid), 'access_token': token, 'access_expire': int(expire)}
        # if expire > time.time():
        #     return {'uid': int(uid), 'access_token': token, 'access_expire': int(expire)}
        # else:
        #     delAuthToken(token)
    return None



def delAuthToken(token):
    pl = Cache.pipeline()
    pl.zrem(TOKEN_EXPIRE, token)
    pl.zrem(TOKEN_UID, token)
    pl.execute()

def delAuthTokenBatch(tokens):
    pl = Cache.pipeline()
    for token in tokens:
        pl.zrem(TOKEN_EXPIRE, token)
        pl.zrem(TOKEN_UID, token)
    pl.execute()

def delAuthUid(uid):
    tokens = Cache.zrangebyscore(TOKEN_UID, uid, uid)
    if tokens:
        token = tokens[0]
        delAuthToken(token)





