# encoding=utf-8
import json
import zlib

from datetime import datetime
from cache import Cache
import decimal


class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return int(o)
        super(DecimalEncoder, self).default(o)


def week_report_key(time):
    return 'week_report:%s' % time


def month_report_key(time):
    return 'month_report:%s' % time


def setReport(tp, datas, start, end, machine_category_id, model_id):
    # time = datetime.now().strftime('%Y-%m-%d')
    if model_id:
        start = 'model_%s:%s--%s' % (model_id, start, end)
    elif machine_category_id:
        start = 'category_%s:%s--%s' % (machine_category_id, start, end)
    else:
        start = 'all:%s' % start
    if tp == 1:
        k = week_report_key(start)
    else:
        k = month_report_key(start)
    # print datas
    v = json.dumps(datas, ensure_ascii=True, cls=DecimalEncoder)
    v = zlib.compress(v.encode('utf-8'))
    Cache.set(k, v, 3600)
    # setWeatherTTL(area_id)


def getReport(tp, start, end, machine_category_id, model_id):
    # time = datetime.now().strftime('%Y-%m-%d')
    if model_id:
        start = 'model_%s:%s--%s' % (model_id, start, end)
    elif machine_category_id:
        start = 'category_%s:%s--%s' % (machine_category_id, start, end)
    else:
        start = 'all:%s--%s' % (start, end)
    if tp == 1:
        k = week_report_key(start)
    else:
        k = month_report_key(start)
    v = Cache.get(k)
    v = zlib.decompress(v) if v else None
    v = json.loads(v) if v else None
    return v


def setReportRegion(tp, datas, start, end, machine_category_id, model_id, region_type):
    # time = datetime.now().strftime('%Y-%m-%d')
    if region_type == 1:
        folder = 'agency_region'
    else:
        folder = 'administration_region'
    if model_id:
        start = folder + ':model_%s:%s--%s' % (model_id, start, end)
    elif machine_category_id:
        start = folder + ':category_%s:%s--%s' % (machine_category_id, start, end)
    else:
        start = folder + ':all:%s--%s' % (start, end)
    if tp == 1:
        k = week_report_key(start)
    else:
        k = month_report_key(start)
    # print datas
    v = json.dumps(datas, ensure_ascii=True, cls=DecimalEncoder)
    v = zlib.compress(v.encode('utf-8'))
    Cache.set(k, v, 3600)
    # setWeatherTTL(area_id)


def getReportRegion(tp, start, end, machine_category_id, model_id, region_type):
    # time = datetime.now().strftime('%Y-%m-%d')
    if region_type == 1:
        folder = 'agency_region'
    else:
        folder = 'administration_region'
    if model_id:
        start = folder + ':model_%s:%s--%s' % (model_id, start, end)
    elif machine_category_id:
        start = folder + ':category_%s:%s--%s' % (machine_category_id, start, end)
    else:
        start = folder + ':all:%s--%s' % (start, end)
    if tp == 1:
        k = week_report_key(start)
    else:
        k = month_report_key(start)
    v = Cache.get(k)
    v = zlib.decompress(v) if v else None
    v = json.loads(v) if v else None
    return v


def setReportProportion(tp, datas, start, end, machine_category_id, model_id, proportion_type):
    # time = datetime.now().strftime('%Y-%m-%d')
    if proportion_type == 1:
        folder = 'category_proportion'
    else:
        folder = 'model_proportion'
    if model_id:
        start = folder + ':model_%s:%s--%s' % (model_id, start, end)
    elif machine_category_id:
        start = folder + ':category_%s:%s--%s' % (machine_category_id, start, end)
    else:
        start = folder + ':all:%s--%s' % (start, end)
    if tp == 1:
        k = week_report_key(start)
    else:
        k = month_report_key(start)
    # print datas
    v = json.dumps(datas, ensure_ascii=True, cls=DecimalEncoder)
    v = zlib.compress(v.encode('utf-8'))
    Cache.set(k, v, 3600)
    # setWeatherTTL(area_id)


def getReportProportion(tp, start, end, machine_category_id, model_id, proportion_type):
    # time = datetime.now().strftime('%Y-%m-%d')
    if proportion_type == 1:
        folder = 'category_proportion'
    else:
        folder = 'model_proportion'
    if model_id:
        start = folder + ':model_%s:%s--%s' % (model_id, start, end)
    elif machine_category_id:
        start = folder + ':category_%s:%s--%s' % (machine_category_id, start, end)
    else:
        start = folder + ':all:%s--%s' % (start, end)
    if tp == 1:
        k = week_report_key(start)
    else:
        k = month_report_key(start)
    v = Cache.get(k)
    v = zlib.decompress(v) if v else None
    v = json.loads(v) if v else None
    return v


def setReportRepeatOrderProportion(tp, datas, start, end, machine_category_id, model_id, proportion_type):
    # time = datetime.now().strftime('%Y-%m-%d')
    if proportion_type == 1:
        folder = 'repair_order:model'
    elif proportion_type == 2:
        folder = 'repair_order:repair_man'
    else:
        folder = 'repair_order:damage'
    if model_id:
        start = folder + ':model_%s:%s--%s' % (model_id, start, end)
    elif machine_category_id:
        start = folder + ':category_%s:%s--%s' % (machine_category_id, start, end)
    else:
        start = folder + ':all:%s--%s' % (start, end)
    if tp == 1:
        k = week_report_key(start)
    else:
        k = month_report_key(start)
    # print datas
    v = json.dumps(datas, ensure_ascii=True, cls=DecimalEncoder)
    v = zlib.compress(v.encode('utf-8'))
    Cache.set(k, v, 3600)
    # setWeatherTTL(area_id)


def getReportRepeatOrderProportion(tp, start, end, machine_category_id, model_id, proportion_type):
    # time = datetime.now().strftime('%Y-%m-%d')
    if proportion_type == 1:
        folder = 'repair_order:model'
    elif proportion_type == 2:
        folder = 'repair_order:repair_man'
    else:
        folder = 'repair_order:damage'
    if model_id:
        start = folder + ':model_%s:%s--%s' % (model_id, start, end)
    elif machine_category_id:
        start = folder + ':category_%s:%s--%s' % (machine_category_id, start, end)
    else:
        start = folder + ':all:%s--%s' % (start, end)
    if tp == 1:
        k = week_report_key(start)
    else:
        k = month_report_key(start)
    v = Cache.get(k)
    v = zlib.decompress(v) if v else None
    v = json.loads(v) if v else None
    return v
