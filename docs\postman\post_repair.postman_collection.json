{"info": {"_postman_id": "f5cb64c3-5014-4a0c-a291-40848eb6f0ec", "name": "post_repair", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "repair", "item": [{"name": "endpoint", "item": [{"name": "getEndpointDetail", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "order_sn", "value": "{{repair-endpoint-order_sn}}", "type": "default"}]}, "url": {"raw": "{{protocol}}{{urlhead}}/repair/endpoint/detail", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "endpoint", "detail"]}}, "response": []}, {"name": "getEndpointList", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/endpoint/list?endpoint_id={{repair-endpoint-id}}", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "endpoint", "list"], "query": [{"key": "endpoint_id", "value": "{{repair-endpoint-id}}"}, {"key": "phone", "value": "{{repair-phone}}", "disabled": true}, {"key": "barcode", "value": "{{repair-barcode-last}}", "disabled": true}]}}, "response": []}, {"name": "getEndpointHistory", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/endpoint/history?phone={{repair-phone}}", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "endpoint", "history"], "query": [{"key": "phone", "value": "{{repair-phone}}"}]}}, "response": []}, {"name": "addEndpointCache", "event": [{"listen": "prerequest", "script": {"exec": ["var order_last_str = pm.environment.get(\"repair-order-last\");\r", "var order_last = JSON.parse(order_last_str);\r", "\r", "var content = {\r", "  barcode: order_last.barcode,\r", "  serial: order_last.serial,\r", "  damage: order_last.damage,\r", "  period_file: order_last.period_file,\r", "  upload_file: order_last.upload_file,\r", "  video_file: order_last.video_file,\r", "  description: order_last.description,\r", "  name: order_last.name,\r", "  phone: order_last.phone,\r", "  province: order_last.province,\r", "  city: order_last.city,\r", "  district: order_last.district,\r", "  address: order_last.address,\r", "  come_exp_type: order_last.come_exp_type,\r", "  repair_endpoint: order_last.repair_endpoint,\r", "  endpoint: order_last.endpoint,\r", "  is_user_address: 0\r", "};\r", "var content_json = JSON.stringify(content);\r", "var content_jv = pm.variables.replaceIn(content_json);\r", "pm.variables.set(\"content\", content_jv);\r", ""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();\r", "if (jsonData.ok && jsonData.ok === 1) {\r", "  var data = jsonData?.data;\r", "  if (data && data.agent_order_sn) {\r", "    pm.environment.set(\"repair-endpoint-agent_order_sn\", data.agent_order_sn);\r", "  }\r", "  if (data && data.order_sn) {\r", "    pm.environment.set(\"repair-endpoint-order_sn\", data.order_sn);\r", "  }\r", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "endpoint_id", "value": "{{repair-endpoint-id}}", "type": "default"}, {"key": "content", "value": "{{content}}", "type": "default"}]}, "url": {"raw": "{{protocol}}{{urlhead}}/repair/endpoint/save_cache", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "endpoint", "save_cache"]}}, "response": []}, {"name": "addEndpointOrder", "event": [{"listen": "prerequest", "script": {"exec": ["var order_last_str = pm.environment.get(\"repair-order-last\");\r", "var order_last = JSON.parse(order_last_str);\r", "\r", "var content = {\r", "  name: order_last.name,\r", "  phone: order_last.phone,\r", "  province: order_last.province,\r", "  city: order_last.city,\r", "  district: order_last.district,\r", "  address: order_last.address,\r", "  come_exp_type: order_last.come_exp_type,\r", "  agent_order_sn: '{{repair-endpoint-agent_order_sn}}',\r", "  agency: 1,\r", "  is_tell: 0\r", "};\r", "var content_json = JSON.stringify(content);\r", "var content_jv = pm.variables.replaceIn(content_json);\r", "pm.variables.set(\"content\", content_jv);\r", ""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Response successed\", function () {\r", "  var jsonData = pm.response.json();\r", "  pm.expect(jsonData.ok).to.eql(1);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "endpoint_id", "value": "{{repair-endpoint-id}}", "type": "default"}, {"key": "content", "value": "{{content}}", "type": "default"}]}, "url": {"raw": "{{protocol}}{{urlhead}}/repair/endpoint/add", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "endpoint", "add"]}}, "response": []}]}, {"name": "agent", "item": [{"name": "getAgentOrderList", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/agent/order_list", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "agent", "order_list"], "query": [{"key": "barcode", "value": "", "disabled": true}]}}, "response": []}]}, {"name": "getRepairHistory", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();\r", "if (jsonData.ok && jsonData.ok === 1) {\r", "  var list = jsonData.data?.data;\r", "  if (list?.length > 0) {\r", "    var last = list[0];\r", "    if (last && last.sn) {\r", "      pm.environment.set(\"repair-order_sn-last\", last.sn);\r", "    }\r", "  }\r", "}\r", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/history", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "history"]}}, "response": []}, {"name": "getOrderDetail", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();\r", "if (jsonData.ok && jsonData.ok === 1) {\r", "  var info = jsonData?.data?.info;\r", "  var info_json = JSON.stringify(info);\r", "  pm.environment.set(\"repair-order-last\", info_json);\r", "}\r", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/detail?order_sn={{repair-order_sn-last}}", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "detail"], "query": [{"key": "order_sn", "value": "{{repair-order_sn-last}}"}]}}, "response": []}, {"name": "addOrder", "event": [{"listen": "prerequest", "script": {"exec": ["var order_last_str = pm.environment.get(\"repair-order-last\");\r", "var order_last = JSON.parse(order_last_str);\r", "\r", "var content = {\r", "  barcode: order_last.barcode,\r", "  serial: order_last.serial,\r", "  phone: order_last.phone,\r", "  damage: order_last.damage,\r", "  period_file: order_last.period_file,\r", "  upload_file: order_last.upload_file,\r", "  video_file: order_last.video_file,\r", "  description: order_last.description,\r", "  name: order_last.name,\r", "  phone: order_last.phone,\r", "  province: order_last.province,\r", "  city: order_last.city,\r", "  district: order_last.district,\r", "  address: order_last.address,\r", "  come_exp_type: order_last.come_exp_type,\r", "  repair_endpoint: order_last.repair_endpoint\r", "};\r", "var content_json = JSON.stringify(content);\r", "var content_jv = pm.variables.replaceIn(content_json);\r", "pm.variables.set(\"content\", content_jv);\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "content", "value": "{{content}}", "type": "default"}]}, "url": {"raw": "{{protocol}}{{urlhead}}/repair/add", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "add"]}}, "response": []}, {"name": "addOrder_v2", "event": [{"listen": "prerequest", "script": {"exec": ["var order_last_str = pm.environment.get(\"repair-order-last\");\r", "var order_last = JSON.parse(order_last_str);\r", "\r", "var content = {\r", "  barcode: order_last.barcode,\r", "  serial: order_last.serial,\r", "  phone: order_last.phone,\r", "  damage: order_last.damage,\r", "  period_file: order_last.period_file,\r", "  upload_file: order_last.upload_file,\r", "  video_file: order_last.video_file,\r", "  description: order_last.description,\r", "  name: order_last.name,\r", "  phone: order_last.phone,\r", "  province: order_last.province,\r", "  city: order_last.city,\r", "  district: order_last.district,\r", "  address: order_last.address,\r", "  come_exp_type: order_last.come_exp_type,\r", "  repair_endpoint: order_last.repair_endpoint\r", "};\r", "var content_json = JSON.stringify(content);\r", "var content_jv = pm.variables.replaceIn(content_json);\r", "pm.variables.set(\"content\", content_jv);\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "content", "value": "{{content}}", "type": "default"}]}, "url": {"raw": "{{protocol}}{{urlhead}}/repair/add_v2", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "add_v2"]}}, "response": []}, {"name": "orderAbandon", "request": {"method": "POST", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/order_abandon?order_sn={{repair-order_sn-last}}", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "order_abandon"], "query": [{"key": "order_sn", "value": "{{repair-order_sn-last}}"}]}}, "response": []}, {"name": "uploadFile", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "images", "type": "file", "src": []}]}, "url": {"raw": "{{protocol}}{{urlhead}}/repair/upload_file", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "upload_file"]}}, "response": []}, {"name": "periodFile", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "images", "type": "file", "src": []}]}, "url": {"raw": "{{protocol}}{{urlhead}}/repair/period_file", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "period_file"]}}, "response": []}, {"name": "ban", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/ban", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "ban"]}}, "response": []}, {"name": "bind_equip_list", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/bind_equip_list", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "bind_equip_list"]}}, "response": []}, {"name": "bind_equip_wear_list", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/repair/bind_equip_wear_list?uid=UA5B931D973B15A3", "host": ["{{protocol}}{{urlhead}}"], "path": ["repair", "bind_equip_wear_list"], "query": [{"key": "uid", "value": "UA5B931D973B15A3"}]}}, "response": []}]}, {"name": "broken_screen_insurance", "item": [{"name": "checkStandard", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/broken_screen_insurance/check_standard?barcode={{repair-barcode-last}}", "host": ["{{protocol}}{{urlhead}}"], "path": ["broken_screen_insurance", "check_standard"], "query": [{"key": "barcode", "value": "{{repair-barcode-last}}"}]}}, "response": []}, {"name": "getStandard", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/broken_screen_insurance/standard?model_id=297", "host": ["{{protocol}}{{urlhead}}"], "path": ["broken_screen_insurance", "standard"], "query": [{"key": "model_id", "value": "297", "description": "C30"}, {"key": "model_id", "value": "343", "description": "C50", "disabled": true}]}}, "response": []}, {"name": "getSupportModels", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/broken_screen_insurance/support_models", "host": ["{{protocol}}{{urlhead}}"], "path": ["broken_screen_insurance", "support_models"]}}, "response": []}, {"name": "add", "event": [{"listen": "prerequest", "script": {"exec": ["content = {\r", "  endpoint: 70,\r", "  endpoint_name: '终端名称',\r", "  barcode: '6309172108978',\r", "  name: 'test_name',\r", "  phone: '400-8325-888',\r", "  standard: 49, // 投保标准id\r", "  type: 1, // 碎屏保类型属于购买还是赠送 1购买 2赠送 默认1\r", "  image_path: [],\r", "};\r", "\r", "var content_json = JSON.stringify(content);\r", "var content_jv = pm.variables.replaceIn(content_json);\r", "pm.variables.set(\"content\", content_jv);\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "content", "value": "{{content}}", "type": "default"}, {"key": "resubmit", "value": "1", "type": "default", "disabled": true}, {"key": "order_sn", "value": "", "type": "default", "disabled": true}]}, "url": {"raw": "{{protocol}}{{urlhead}}/broken_screen_insurance/add", "host": ["{{protocol}}{{urlhead}}"], "path": ["broken_screen_insurance", "add"]}}, "response": []}]}, {"name": "optional_accessory", "item": [{"name": "getOptionalAccessoryCategoryList", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/optional_accessory/category/list", "host": ["{{protocol}}{{urlhead}}"], "path": ["optional_accessory", "category", "list"], "query": [{"key": "only_have", "value": "false", "disabled": true}]}}, "response": []}, {"name": "getOptionalAccessoryList", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/optional_accessory/list?page=1&count=10&category_id=1", "host": ["{{protocol}}{{urlhead}}"], "path": ["optional_accessory", "list"], "query": [{"key": "page", "value": "1"}, {"key": "count", "value": "10"}, {"key": "category_id", "value": "1"}]}}, "response": []}, {"name": "cancelAll", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "order_sn", "value": "{{repair-order_sn-last}}", "type": "default"}]}, "url": {"raw": "{{protocol}}{{urlhead}}/optional_accessory/cancel_all", "host": ["{{protocol}}{{urlhead}}"], "path": ["optional_accessory", "cancel_all"]}}, "response": []}, {"name": "add", "event": [{"listen": "prerequest", "script": {"exec": ["content = [\r", "  {\r", "    oa_id: 1,\r", "    count: 1,\r", "  },\r", "  {\r", "    oa_id: 2,\r", "    count: 1,\r", "  },\r", "];\r", "// content = [];\r", "\r", "var content_json = JSON.stringify(content);\r", "var content_jv = pm.variables.replaceIn(content_json);\r", "pm.variables.set(\"content\", content_jv);\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "content", "value": "{{content}}", "type": "default"}, {"key": "order_sn", "value": "{{repair-order_sn-last}}", "type": "default"}, {"key": "endpoint_id", "value": "1", "type": "default", "disabled": true}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{protocol}}{{urlhead}}/optional_accessory/add", "host": ["{{protocol}}{{urlhead}}"], "path": ["optional_accessory", "add"]}}, "response": []}]}, {"name": "Test", "item": [{"name": "mes", "event": [{"listen": "prerequest", "script": {"exec": ["var content = {\r", "  barcode: \"9611222164076\"\r", "};\r", "var content_json = JSON.stringify(content);\r", "pm.variables.set(\"content\", content_json);\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "content", "value": "{{content}}", "type": "default"}]}, "url": {"raw": "{{protocol}}{{urlhead}}/test/mes", "host": ["{{protocol}}{{urlhead}}"], "path": ["test", "mes"]}}, "response": []}, {"name": "ar_repeat", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/test/ar_repeat?barcode={{repair-barcode-last}}", "host": ["{{protocol}}{{urlhead}}"], "path": ["test", "ar_repeat"], "query": [{"key": "barcode", "value": "{{repair-barcode-last}}"}]}}, "response": []}]}, {"name": "K3Cloud", "item": [{"name": "BdMaterial", "request": {"method": "GET", "header": [], "url": {"raw": "{{k3cloud_url_base}}?s=/KingDee/BdMaterial/index.html", "host": ["{{k3cloud_url_base}}"], "query": [{"key": "s", "value": "/KingDee/BdMaterial/index.html"}]}}, "response": []}, {"name": "BdMaterialGroup", "request": {"method": "GET", "header": [], "url": {"raw": "{{k3cloud_url_base}}?s=/KingDee/BdMaterialGroup/index.html", "host": ["{{k3cloud_url_base}}"], "query": [{"key": "s", "value": "/KingDee/BdMaterialGroup/index.html"}]}}, "response": []}, {"name": "StkInventory-stock04.02", "request": {"method": "GET", "header": [], "url": {"raw": "{{k3cloud_url_base}}?s=/KingDee/StkInventory/index.html&FStockFNumber=04.02", "host": ["{{k3cloud_url_base}}"], "query": [{"key": "s", "value": "/KingDee/StkInventory/index.html"}, {"key": "FStockFNumber", "value": "04.02"}]}}, "response": []}, {"name": "SalSaleOrder", "request": {"method": "GET", "header": [], "url": {"raw": "{{k3cloud_url_base}}?s=/KingDee/SalSaleOrder/index.html", "host": ["{{k3cloud_url_base}}"], "query": [{"key": "s", "value": "/KingDee/SalSaleOrder/index.html"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var snParams = [\"ua\", \"t\", \"sn\"];", "pm.request.url.removeQueryParams(snParams);", "", "var t = new Date();", "var ts = Math.round(t.getTime() / 1000);", "pm.environment.set(\"timestamp-last\", ts);", "", "var k3key = pm.environment.get(\"k3cloud_key\");", "var k3secret = pm.environment.get(\"k3cloud_secret\");", "", "var sn = CryptoJS.MD5(k3key + '-' + ts + '-' + k3secret).toString();", "var auth_key = k3key + '-' + ts + '-' + sn;", "pm.environment.set(\"k3cloud_auth_key\", auth_key);", "", "pm.request.addQueryParams(\"authKey=\" + auth_key);", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "MES", "item": [{"name": "ApiWai/Material", "item": [{"name": "type", "request": {"method": "GET", "header": [], "url": {"raw": "{{mes_url_base_v3}}?s=/ApiWai/Material/type.html", "host": ["{{mes_url_base_v3}}"], "query": [{"key": "s", "value": "/ApiWai/Material/type.html"}]}}, "response": []}, {"name": "lists", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();\r", "if (jsonData?.data && jsonData.data.length > 0) {\r", "  data = jsonData.data;\r", "  data_len = data.length;\r", "  data_last = data[data_len - 1];\r", "  console.log({data, data_len, data_last});\r", "}\r", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{mes_url_base_v3}}?s=/ApiWai/Material/lists.html&rows=20000&page=1", "host": ["{{mes_url_base_v3}}"], "query": [{"key": "s", "value": "/ApiWai/Material/lists.html"}, {"key": "rows", "value": "20000"}, {"key": "page", "value": "1"}, {"key": "page", "value": "2", "disabled": true}, {"key": "page", "value": "3", "disabled": true}, {"key": "page", "value": "4", "disabled": true}, {"key": "page", "value": "5", "disabled": true}]}}, "response": []}, {"name": "info", "request": {"method": "GET", "header": [], "url": {"raw": "{{mes_url_base_v3}}?s=/ApiWai/Material/info.html&code=97004.00050", "host": ["{{mes_url_base_v3}}"], "query": [{"key": "s", "value": "/ApiWai/Material/info.html"}, {"key": "code", "value": "97004.00050"}]}}, "response": []}]}, {"name": "ApiTool/ApiBase", "item": [{"name": "test", "request": {"method": "GET", "header": [], "url": {"raw": "{{mes_url_base_v3}}?s=/ApiTool/ApiBase/test.html", "host": ["{{mes_url_base_v3}}"], "query": [{"key": "s", "value": "/ApiTool/ApiBase/test.html"}]}}, "response": []}]}, {"name": "getByBarcode", "request": {"method": "GET", "header": [], "url": {"raw": "{{mes_url_base}}?s=/Api/Barcode/all.html&barcode=9611222164076", "host": ["{{mes_url_base}}"], "query": [{"key": "s", "value": "/Api/Barcode/all.html"}, {"key": "barcode", "value": "9611222164076"}]}}, "response": []}, {"name": "getDeviceTypeLast", "request": {"method": "GET", "header": [], "url": {"raw": "{{mes_url_base}}?s=/Api/DeviceType/last/authKey/{{mes_auth_key}}/lastid/$lastId", "host": ["{{mes_url_base}}"], "query": [{"key": "s", "value": "/Api/DeviceType/last/authKey/{{mes_auth_key}}/lastid/$lastId"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var snParams = [\"ua\", \"t\", \"sn\"];", "pm.request.url.removeQueryParams(snParams);", "", "var ts = pm.environment.get(\"timestamp-last\");", "var key = pm.environment.get(\"mes_key\");", "var secret = pm.environment.get(\"mes_secret\");", "", "var sn = CryptoJS.MD5(key + '-' + ts + '-' + secret).toString();", "var auth_key = key + '-' + ts + '-' + sn;", "", "pm.request.addQueryParams(\"authKey=\" + auth_key);", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "daily-statistics", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json().data;\r", "var to_check_num = jsonData.to_check_num;\r", "var repair_man_stat = jsonData.repair_man_stat;\r", "var repair_man_to_check_sum = repair_man_stat.reduce((p, c) => p + c.to_check_num, 0);\r", "console.log({to_check_num, repair_man_to_check_sum});\r", "pm.test(\"to_check_num\", function () {\r", "  pm.expect(to_check_num).to.eql(repair_man_to_check_sum);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/daily-statistics", "host": ["{{protocol}}{{urlhead}}"], "path": ["daily-statistics"]}}, "response": []}, {"name": "daily-statistics_repair-man", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/daily-statistics/repair_man?month=2022-09", "host": ["{{protocol}}{{urlhead}}"], "path": ["daily-statistics", "repair_man"], "query": [{"key": "month", "value": "2022-10", "disabled": true}, {"key": "month", "value": "2022-09"}, {"key": "month", "value": "2022-08", "disabled": true}, {"key": "repair_man", "value": "罗世孔", "disabled": true}]}}, "response": []}, {"name": "report", "request": {"method": "GET", "header": [], "url": {"raw": "{{protocol}}{{urlhead}}/report?start=2022-08-01&end=2022-08-31", "host": ["{{protocol}}{{urlhead}}"], "path": ["report"], "query": [{"key": "start", "value": "2022-08-01"}, {"key": "end", "value": "2022-08-31"}, {"key": "machine_category_id", "value": "", "disabled": true}, {"key": "model_id", "value": "329", "disabled": true}, {"key": "type", "value": "", "disabled": true}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var t = new Date();", "var ts = Math.round(t.getTime() / 1000);", "pm.environment.set(\"timestamp-last\", ts);", "", "var ua = pm.environment.get(\"ua\");", "ua = pm.variables.replaceIn(ua);", "", "var appSecret = pm.environment.get(\"app_secret\");", "", "var sn = CryptoJS.MD5(ua + appSecret + ts).toString();", "pm.environment.set(\"sn\", sn);", "ua = encodeURIComponent(ua);", "", "var accessToken = pm.environment.get(\"access_token\");", "", "pm.request.addQueryParams(\"access_token=\" + accessToken);", "pm.request.addQueryParams(\"ua=\" + ua);", "pm.request.addQueryParams(\"t=\" + ts);", "pm.request.addQueryParams(\"sn=\" + sn);", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}