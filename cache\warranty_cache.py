# coding=utf-8
# __author__=lch

try:
    import simplejson as json
except:
    import json
import time
import arrow

from . import Cache
from conf import config


TOKEN_EXPIRE = 'token_expire'
TOKEN_UID = 'token_uid'


def _today_remain_senconds():
    now = arrow.now()
    delta = now.ceil('day')-now
    return int(delta.total_seconds())+10


def _key_sale_star(frm, to, top_agency, limit):
    return 'salestar:'+str(top_agency)+'_'+frm+'_'+to+'_'+str(limit)


def setSaleStar(frm, to, top_agency, limit, stars):
    k = _key_sale_star(frm, to, top_agency, limit)
    s = json.dumps(stars)
    Cache.set(k, s)
    Cache.expire(k, _today_remain_senconds())


def getSaleStar(frm, to, top_agency=0, limit=3):
    k = _key_sale_star(frm, to, top_agency, limit)
    stars = None
    s = Cache.get(k)
    if s:
        stars = json.loads(s)
    return stars






