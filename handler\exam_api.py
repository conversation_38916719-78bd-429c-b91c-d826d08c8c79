#coding=utf-8
#encoding=utf-8
#__author__="QZL"

from handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, tokenauth
from tornado import gen
from store import endpoint_model
from store import user_model
from util import com
from store import exam_model
import json

class ExamSalesman(ApiHandler):
    @gen.coroutine
    @tokenauth
    def get(self):
        """
        导购员列表
        :return:
        """
        endpoint = yield endpoint_model.userEndpoint(self.auth['uid'])
        if not endpoint.get('id'):
            raise gen.Return(self.error(u'未知终端', com.ERROR_PARAM))
        data = yield exam_model.salseman_list(endpoint['id'])
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class ExamPhoneCode(ApiHandler):
    @gen.coroutine
    @tokenauth
    def get(self):
        """
        发送手机验证码
        :return:
        """
        phone = self.get_argument('phone', '')
        if not phone:
            raise gen.Return(self.error(u'手机号有误', com.ERROR_PARAM))
        data = yield exam_model.phone_code(phone)
        raise gen.Return(self.success(data) if data else self.error(u'请勿频繁发送', com.ERROR_EMPTY_DATA))


class ExamNewSalesman(ApiHandler):
    @gen.coroutine
    @tokenauth
    def post(self):
        info = self.get_argument('data', '')
        try:
            info = json.loads(info)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        endpoint = yield endpoint_model.userEndpoint(self.auth['uid'])
        if not endpoint.get('id'):
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        info['endpoint_id'] = endpoint['id']
        ret = yield exam_model.create_salesman(info)
        if ret == 1:
            data = yield exam_model.salseman_list(endpoint['id'])
            raise gen.Return(self.success(data))
        elif ret == -1:
            raise gen.Return(self.error(u'用户已注册', com.ERROR_ALREADY_EXISTS))
        elif ret == -2:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        else :
            raise gen.Return(self.error(u'验证码错误', com.ERROR_EMPTY_DATA))


class ExamSetSalesman(ApiHandler):
    @gen.coroutine
    @tokenauth
    def post(self):
        """
        启用禁用导购员账号
        :return:
        """
        status = com.safeInt(self.get_argument('status', 0))
        salesman = com.safeInt(self.get_argument('salesman', 0))
        username = self.get_argument('username', '')
        password = self.get_argument('password', '')
        if not username or not password or not salesman:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        user = yield user_model.passwordLogin(username, password)
        endpoint = yield endpoint_model.userEndpoint(self.auth['uid'])
        if not user or user.get('uid') != self.auth['uid'] or not endpoint.get('id'):
            raise gen.Return(self.error(u'权限不足', com.ERROR_NOT_PERMIT))
        data = yield exam_model.set_salesman(endpoint['id'], salesman, status)
        if data:
            ret = yield exam_model.salseman_list(endpoint['id'])
            raise gen.Return(self.success(ret))
        else:
            raise gen.Return(self.error(u'更新出错', com.ERROR_UNKNOWN))


class ExamGetPaper(ApiHandler):
    @gen.coroutine
    @tokenauth
    def get(self):
        """
        获取试卷题目
        :return:
        """
        paper = com.safeInt(self.get_argument('paper', 0))
        salesman = com.safeInt(self.get_argument('salesman', 0))
        code = self.get_argument('code', '')
        check = yield exam_model.check_salesman(salesman, code)
        if not check:
            raise gen.Return(self.error(u'答卷码错误', com.ERROR_PARAM))
        data = yield exam_model.paper(paper)
        if not data:
            raise gen.Return(self.error(u'未找到试卷', com.ERROR_EMPTY_DATA))
        raise gen.Return(self.success(data))


class ExamHistory(ApiHandler):
    @gen.coroutine
    @tokenauth
    def get(self):
        """
        获取导购的作答历史，包含可作答未作答试卷
        :return:
        """
        salesman = com.safeInt(self.get_argument('salesman', 0))
        data = yield exam_model.history(salesman)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class ExamResponse(ApiHandler):
    @gen.coroutine
    @tokenauth
    def post(self):
        """
        作答试卷
        :return:
        """
        data = self.get_argument('data', '')
        try:
            data = json.loads(data)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        endpoint = yield endpoint_model.userEndpoint(self.auth['uid'])
        if not endpoint or not endpoint.get('id'):
            raise gen.Return(self.error(u'权限有误', com.ERROR_PARAM))
        endpoint_id = endpoint['id']
        data = yield exam_model.response(endpoint_id, data)
        raise gen.Return(self.success(data) if data else self.error(u'交卷失败', com.ERROR_UNKNOWN))