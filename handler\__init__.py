# encoding=utf-8
# __author__ = 'lch'
import functools
import time
from hashlib import md5
import datetime
try:
    import simplejson as json
except:
    import json
from tornado.web import RequestHandler
from tornado import gen
from tornado.log import app_log

import conf.config as config
# import conf.apps as apps
import util.com as com
# from util.enc import encRC4, encB64
import store.appauth_model as appauth_model
import cache.user_cache as auth_cache
from store import user_model
from log import api_log

'''定义ApiHandler基类'''


# Api请求处理基类
class ApiHandler(RequestHandler):
    appauth = None
    user_auth = None
    roles_data = None

    def initialize(self):
        self.appauth = None
        self.user_auth = None
        self.roles_data = None

    @gen.coroutine
    def _getAppAuth(self, appid):
        authMap = getattr(self.application, 'authMap', {})
        auth = authMap.get(appid, {})
        authTime = auth.get('authTime', 0)
        now = int(time.time())
        if now - authTime > 24 * 3600:
            auth = yield appauth_model.getAppAuth(appid)
            if auth:
                auth['authTime'] = now
        if not auth:
            raise gen.Return(None)

        d = datetime.datetime.fromtimestamp(now)
        accesshour = auth.get('hour', -1)
        accessCnt = auth.get('accessCount', 0)
        if d.hour != accesshour:
            auth['hour'] = d.hour
            auth['accessCount'] = 1
        else:
            auth['accessCount'] = accessCnt + 1

        authMap[appid] = auth
        self.application.authMap = authMap

        raise gen.Return(auth.copy())

    @staticmethod
    def _check_sn(device_id, appkey, t, sn):
        s = device_id+appkey+str(t)
        sn_calc = md5(s).hexdigest()
        return sn == sn_calc

    @staticmethod
    def _check_model(model=''):
        # m = model.lower()
        # if m.startswith('dream_') or m.startswith('jiaoxueji') or m.startswith('readboy_') \
        #         or m.startswith('classone_') or m.startswith('dsl'):
        #     return True
        # return False
        return True

    def _check_device(self, device_id):
        info = device_id.split('/', 6)
        if len(info) != 6:
            return None
        model = info[2]
        appid = info[3]
        return appid if self._check_model(model) else None

    @gen.coroutine
    def prepare(self):
        # appid = self.get_argument('appid', default='')
        sn = self.get_argument('sn', default='')
        t = self.get_argument('t', default=0)
        device_id = self.get_argument('ua', default='')
        # 测试或工具入口
        if sn == 'caretest':
            self.appauth = {'appid': 'test', 'appkey': 'test'}
            raise gen.Return()
        appid = self._check_device(device_id)
        if not appid:
            self.error(u'未知应用', com.FETAL_UNAUTH)
            self.finish()
            raise gen.Return()
        # 客户端入口
        appAuth = yield self._getAppAuth(appid)
        if not appAuth:
            self.error(u'应用未授权', com.FETAL_UNAUTH)
            self.finish()
            raise gen.Return()
        if appAuth.get('status', 1):
            self.error(u'应用授权已停用', com.FETAL_UNAUTH)
            self.finish()
            raise gen.Return()
        accessCnt = appAuth.get('accessCount', 0)
        if accessCnt >= 10000000:
            self.error(u'应用流量受限', com.FETAL_UNAUTH)
            self.finish()
            raise gen.Return()
        appkey = appAuth['appkey']
        if not self._check_sn(device_id, appkey, t, sn):
            self.error(u'授权无效', com.FETAL_UNAUTH)
            self.finish()
            raise gen.Return()
        self.appauth = appAuth
        raise gen.Return()

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.dumpJson(ret)
        # app_log.info(s)

    def dumpJson(self, ret):
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET,POST')
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            s = json.dumps(ret, ensure_ascii=False, indent=4)
        else:
            s = json.dumps(ret, ensure_ascii=False)
        self.log_info(s)
        self.write(s)
        return s

    def log_info(self, data):
        data = json.loads(data)
        method = self.request.method
        ip = self.request.remote_ip
        path = self.request.path
        charset = self.get_argument('charset', 'utf-8')
        query = self.request.query_arguments
        if query:
            query = {i: query[i][0].encode(charset) for i in query}
        body = self.request.body_arguments
        if body:
            body = {i: body[i][0].encode(charset) for i in body}
        query.update(body)
        now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_info = {'method': method, 'ip': ip, 'path': path, 'input': query, 'output': data, 'time': now,
                    'user_id': ''}
        if self.user_auth:
            log_info['user_id'] = self.user_auth['uid']
        log_info = json.dumps(log_info, ensure_ascii=False)
        api_log.info(log_info)

    @gen.coroutine
    def check_uid(self):
        token = self.get_argument('access_token', '')
        _auth = auth_cache.checkAuth(token)
        # app_log.info(json.dumps(_auth))
        ### 没令牌远程验证
        if not _auth or _auth.get('access_expire') < com.safeInt(time.time()):
            oauth = yield user_model.checkToken(token)
            # app_log.info(json.dumps(oauth))
            if not oauth:
                raise gen.Return(None)
            _auth = yield user_model.storeUser(oauth, token)
        if _auth and _auth.get('uid'):
            self.roles_data = yield user_model.check_roles(_auth['uid'])
            self.auth = _auth
            self.user_auth = _auth
            raise gen.Return(_auth['uid'])
        raise gen.Return(None)



def tokenauth(method):
    """Decorate methods with this to require that the user be logged in.

    If the user is not logged in, they will be redirected to the configured
    `login url <RequestHandler.get_login_url>`.

    If you configure a login url with a query parameter, Tornado will
    assume you know what you're doing and use it as-is.  If not, it
    will add a `next` parameter so the login page knows where to send
    you once you're logged in.
    """
    def wrapper(self, *args, **kwargs):
        token = self.get_argument('access_token', '')
        _auth = auth_cache.checkAuth(token)
        if not config.DEBUG and not _auth:
            raise gen.Return(self.error(u'未登录或登录失效', com.ERROR_BAD_TOKEN))
        self.auth = _auth
        return method(self, *args, **kwargs)
    return wrapper