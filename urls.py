# encoding=utf-8
# __author__ = 'lch'

from handler.appauth_api import *
from handler.repair_api import *
from handler.upload_api import *
from handler.test_api import *
from handler.callback_api import *
from handler.statistics_api import *
from handler.agent_api import *
from handler.broken_screen_insurance_api import *
from handler.endpoint_repair_api import *
from handler.warranty_api import *
from handler.optional_accessory_api import *
from handler.order_query_api import *

handlers = [
    (r'/', HelloWorld),
    (r'/appauth/list', AppAuthList),
    (r'/appauth/get', AppAuthGet),
    (r'/appauth/add', AppAuthAdd),
    (r'/appauth/del', AppAuthDel),
    (r'/appauth/test', AppAuthTest),

    # 查看保修期
    (r'/repair/period', PR_Period),
    (r'/repair/v1/period', PR_PeriodV1),
    # 查看寄修历史
    (r'/repair/history', PR_MyHistory),
    # 申请寄修
    (r'/repair/add', PR_AddRepair),
    (r'/repair/add_v2', PR_AddRepair_V2),
    # 增加寄修快递单号
    (r'/repair/change', PR_Change),
    # 确认收货
    (r'/repair/finish', PR_Finish),
    (r'/repair/appraise', PR_Appraise),
    (r'/repair/detail', PR_Detail),
    (r'/repair/endpoint', PR_Endpoint),
    (r'/repair/contacts', PR_Contacts),
    (r'/repair/add_contact', PR_AddContact),
    (r'/repair/update_contact', PR_UpdateContact),
    (r'/repair/default_contact', PR_DefaultContact),
    (r'/repair/drop_contact', PR_DropContact),
    (r'/repair/region', PR_Region),
    (r'/repair/period_file', PR_UploadPeriod),
    (r'/repair/upload_file', PR_UploadFile),
    (r'/repair/explain', PR_Explain),
    (r'/repair/machine', PR_Machine),
    (r'/repair/accessory', PR_Accessory),
    (r'/repair/exp_route', PR_ExpressRoute),
    # 查询地址是否可以寄件
    (r'/repair/exp_filter', PR_ExpressFilter),
    # 弃修
    (r'/repair/order_abandon', PR_OrderAbandon),
    # 微信支付
    (r'/repair/wxpay', PR_WXPay),
    # 弃修支付
    (r'/repair/ar_wxpay', AR_WXPay),
    # 支付宝支付
    (r'/repair/alipay', PR_ALIPay),
    (r'/repair/ar_alipay', AR_ALIPay),
    (r'/repair/machine_category', PR_MachineCategory),
    (r'/repair/self_test_list', PR_SelfTestList),
    (r'/repair/self_test_damage', PR_SelfTestDamge),
    (r'/repair/self_test_detail', PR_SelfTestDetail),
    (r'/repair/self_test_appraise', PR_SelfTestAppraise),
    (r'/repair/ban', Ban),
    (r'/repair/cancel', PR_CancelRepair),
    (r'/repair/change_address', PR_ChangeAddress),
    # 设备批量保卡信息查询
    (r'/repair/fault_equip_info', PR_FaultEquipmentInfo),
    # 设备绑定
    (r'/repair/bind_equip', BindEquip),
    # 设备解绑
    (r'/repair/unbind_equip', UnbindEquip),
    (r'/repair/bind_equip_list', BindEquipList),
    (r'/repair/bind_equip_wear_list', BindEquipWearList),
    # 查询付款记录
    (r'/repair/wx_order_query', PR_WXOrderQuery),
    (r'/repair/wx_scan', WXScan),
    # 微信退款
    (r'/repair/refund', PR_WXRefund),
    # 微信退款查询
    (r'/repair/wx_refund_query', PR_WXOrderRefundQuery),
    # 支付宝退款
    (r'/repair/alirefund', PR_ALIRefund),
    # 支付宝退款查询
    (r'/repair/ali_refund_query', PR_AliOrderRefundQuery),

    # 代理商寄修
    # 伪码获取
    (r'/pseudo_code', PseudoCode),
    # 查询是否可以维修
    (r'/repair/agent/period', PR_PeriodAgent),
    # 添加大单
    (r'/repair/agent/add_order', AddAgentOrder),
    # 添加小单缓存
    (r'/repair/agent/save_cache', PR_SaveAgentRepair),
    # 增加快递单号
    (r'/repair/agent/change_express', AgentChangeExpress),
    # 代理商订单列表
    (r'/repair/agent/order_list', AgentOrderList),
    # 代理商订单账单
    (r'/repair/agent/order_bill', AgentOrderBill),
    # 代理商订单账单详情
    (r'/repair/agent/order_bill_detail', AgentOrderBillDetail),
    # 订单缓存修改
    (r'/repair/agent/modify_cache', PR_ModifyAgentRepair),
    # 用户维修确认
    (r'/repair/agent/confirm_repair', ConfirmRepair),
    # 订单缓存删除
    (r'/repair/agent/cancel_cache', PR_CancelAgentRepair),
    # 获取缓存
    (r'/repair/agent/order_cache', AgentOrderCacheList),
    # 获取缓存详情
    (r'/repair/agent/order_cache_detail', AgentOrderCacheDetail),
    # 删除大单全部缓存数据
    (r'/repair/agent/order_cache_delete', AgentOrderCacheDelete),


    (r'/test/create_mail', TEST_CreateMail),
    (r'/test/mail_route', TEST_MailRoute),
    (r'/test/test', TEST_Test),
    (r'/test/mes', TEST_MES),
    (r'/test/ar_repeat', TEST_AR_REPEAT),

    (r'/callback/sf_route', CB_SF_Route),
    # 微信支付重定向
    (r'/callback/wxpay', CB_WX_Pay),
    # 弃修微信支付重定向
    (r'/callback/ar_wxpay', CB_AR_WX_Pay),
    # 微信退款重定向
    (r'/callback/wxrefund', CB_WX_Refund),
    # 支付宝支付重定向
    (r'/callback/alipay', CB_ALI_Pay),
    # 弃修支付宝支付重定向
    (r'/callback/ar_alipay', CB_AR_ALI_Pay),
    # 云客服系统通话记录推送
    (r'/callback/ykf_push', YKF_Push),

    # 周报
    # 通用
    (r'/report', Report),
    # 地区分布  -- 行政分布、代理地区分布
    (r'/report/region_distribute', ReportRegionDistribute),
    # 产品占比  -- 品类分布占比、机型分类占比
    (r'/report/product_proportion', ReportCategoryModelProportion),
    # 二次维修机型占比 -- 机型分布、维修人员分布、故障分布
    (r'/report/repeat_order', ReportRepeatOrder),
    # 配件关联故障原因和故障类型
    (r'/report/damage_accessory_detail', ReportDamageAccessoryDetail),
    # 每日看板
    (r'/daily-statistics',  DailyStatistics),
    (r'/daily-statistics/repair_man',  DailyStatisticsRepairMan),
    # 每日看板维修人员
    (r'/daily-statistics/repair-users', RepairDailyUsersRecord),

    # 碎屏保
    # 查看是否允许投保  并返回投保类型
    (r'/broken_screen_insurance/check_standard', BrokenScreenInsurance),
    # 新增投保
    (r'/broken_screen_insurance/add', BrokenScreenInsurance),
    # 后台导入碎屏保
    (r'/broken_screen_insurance/admin/add', AdminBrokenScreenInsurance),
    # 投保标准
    (r'/broken_screen_insurance/standard', BrokenScreenInsuranceStandard),
    # 支持投保产品型号
    (r'/broken_screen_insurance/support_models', SupportInsuranceModels),
    # 投保详情
    (r'/broken_screen_insurance/detail', BrokenScreenInsuranceDetail),
    # 上传文件
    (r'/broken_screen_insurance/upload_file', BSI_UploadFile),
    # 列表
    (r'/broken_screen_insurance/list', BrokenScreenInsuranceList),
    # 预支付下单
    (r'/broken_screen_insurance/trade_pre_order', TradePayPreOrder),
    # 碎屏保退款--服务器调用--营销服务器
    (r'/broken_screen_insurance/refund', BrokenScreenRefund),
    # 碎屏保退款--寄修后台调用
    (r'/broken_screen_insurance/refund2', BrokenScreenRefund2),

    # (r'/aliquery', ALIQuery),
    # 通知回调--支付--退款
    (r'/broken_screen_insurance/callback/pay', CallbackScreenInsurancePay),

    (r'/broken_screen_insurance/callback/refund', CallbackScreenInsuranceRefund),

    (r'/broken_screen_insurance/refund_query', ScreenInsuranceRefundQuery),
    # 终端服务调用-- 查看是否有碎屏保
    (r'/broken_screen_insurance/check', BrokenScreenCheck),
    # 终端服务调用-- 换机同时换碎屏保
    (r'/broken_screen_insurance/exchange', BrokenScreenExchange),
    # 终端服务调用-- 批量查询是否有保卡
    (r'/broken_screen_insurance/check_list', BrokenScreenCheckByBarcodeList),
    # 碎屏保统计
    (r'/broken_screen_insurance/stat', BrokenScreenInsuranceStat),

    # 终端代寄
    # 小单缓存
    (r'/repair/endpoint/save_cache', PR_SaveEndpointRepair),
    # 修改缓存
    (r'/repair/endpoint/modify_cache', PR_ModifyEndpointRepair),
    # 提交所有订单
    (r'/repair/endpoint/add', AddEndpointOrder),
    # 添加寄修单号
    (r'/repair/endpoint/change_express', EndpointChangeExpress),
    # 列表
    (r'/repair/endpoint/list', EndpointOrderList),
    # 用户代寄历史
    (r'/repair/endpoint/history', EndpointMyHistory),
    # 用户代寄订单详情
    (r'/repair/endpoint/detail', EndpointRepairDetail),
    # 订单缓存删除
    (r'/repair/endpoint/cancel_cache', PR_CancelEndpointRepair),
    # 预估价格
    (r'/repair/estimate', PR_Estimate),

    # 内部查询碎屏保支付情况
    (r'/repair/query', PayOrderQuery),

    # 云客服查询保卡
    (r'/repair/warranty', Warranty),

    # 根据关键字查询订单最新日志
    (r'/order/query', OrderQuery),

    # 自选配件
    (r'/optional_accessory/category/list', OAC_List),
    (r'/optional_accessory/list', OA_List),
    (r'/optional_accessory/cancel_all', PR_OA_Cancel_All),
    (r'/optional_accessory/add', PR_OA_Add),
]
