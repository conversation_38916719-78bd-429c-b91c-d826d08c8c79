# coding=utf-8
# encoding=utf-8
import datetime
import hashlib
import json
import random
import string
import time
import urllib
import urlparse
from collections import OrderedDict

import tornado
from tornado import gen, httpclient
from tornado.log import app_log
from store import DB_PR, work_wechat_model, sms_model
from conf import config
from dateutil.relativedelta import relativedelta


MAX_CLIENTS = 1000
httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)
HTTP_TIMEOUT = 30
PAY_HOST = 'https://api-pay-hub.readboy.com'


def createNoncestr(length=32):
    """产生随机字符串，不长于32位"""
    chars = "abcdefghijklmnopqrstuvwxyz0123456789"
    strs = []
    for x in range(length):
        strs.append(chars[random.randrange(0, len(chars))])
    return "".join(strs)


def formatBizQueryParaMap(paraMap, urlencode):
    """格式化参数，签名过程需要使用"""
    slist = sorted(paraMap)
    # print 'url_encode:', url_encoder(slist)
    # return url_encoder(slist)
    buff = []
    for k in slist:
        # print paraMap[k]
        v = urllib.quote_plus(str(paraMap[k]), safe='') if urlencode else paraMap[k]
        buff.append("{0}={1}".format(urllib.quote_plus(k, safe=''), v))
    return "&".join(buff)


def getSign(obj):
    """生成签名"""
    # 签名步骤一：按字典序排序参数,formatBizQueryParaMap已做
    # String = formatBizQueryParaMap(obj, True)
    od = OrderedDict(sorted(obj.items()))
    s = '&'.join([k+'='+v for k, v in od.iteritems()])

    # 签名步骤二：在string后加入KEY
    app_key = '&appsecret=' + config.APP_SECRET['screen_insurance_h5']
    StringA = "{0}{1}".format(s, app_key)
    # print String
    # 签名步骤三：加密
    sign = hashlib.md5(StringA).hexdigest().upper()
    # String = s + '&sign=' + sign
    return sign


def url_encoder(params):
    g_encode_params = {}

    def _encode_params(params, p_key=None):
        encode_params = {}
        if isinstance(params, dict):
            for key in params:
                encode_key = '{0}[{1}]'.format(p_key,key)
                encode_params[encode_key] = params[key]
        elif isinstance(params, (list, tuple)):
            for offset,value in enumerate(params):
                encode_key = '{0}[{1}]'.format(p_key, offset)
                encode_params[encode_key] = value
        else:
            g_encode_params[p_key] = params

        for key in encode_params:
            value = encode_params[key]
            _encode_params(value, key)

    if isinstance(params, dict):
        for key in params:
            _encode_params(params[key], key)

    return urllib.urlencode(g_encode_params)


def nonce_str():
    char = string.ascii_letters + string.digits
    return "".join(random.choice(char) for _ in range(32))


def make_data(param):
    default_param = {
        'appid': 'screen_insurance_h5',
        # 'ua': '',
        'nonce_str': nonce_str(),
    }
    param.update(default_param)
    param['sign'] = getSign(param)
    # print param
    return param


def make_request(url, param, method):
    # print param
    # query = urllib.unquote(url_encoder(param))  # 解码
    # print query
    # dic = dict([(k, v[0]) for k, v in urlparse.parse_qs(query).items()])  # 转成字典
    # print dic
    data = make_data(param)
    # print data
    data = urllib.urlencode(data)
    # data
    # print data
    header = {
        'content-type': 'application/x-www-form-urlencoded',
    }
    if method == 'POST':
        # HTTPRequest实例对象  构造请求
        request = httpclient.HTTPRequest(url, method=method, connect_timeout=HTTP_TIMEOUT, body=data, headers=header,
                                     request_timeout=HTTP_TIMEOUT, validate_cert=False)
    else:
        request = httpclient.HTTPRequest('%s?%s' % (url, data), method=method, connect_timeout=HTTP_TIMEOUT,
                                         request_timeout=HTTP_TIMEOUT, validate_cert=False)
    return request

def _unique_sn():
    """
    生成唯一流水号字符串
    :return:
    """
    ts = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    rand = random.randint(100000, 999999)
    s = ts + 'spb' + str(rand)
    return s


def _image_to_str(data, fields):
    """
    数据中的文件数组转换成字符串
    :param data: 数据
    :param fields: 要转换的字段
    :return:
    """
    for i in fields:
        if data.get(i) and isinstance(data[i], list):
            rets = list()
            for img in data[i]:
                if img.startswith(config.OSS_CNAME):
                    ret = img[len(config.OSS_CNAME):]
                    rets.append(ret)
            s = json.dumps(rets)
            data[i] = s
        else:
            data[i] = ''
    return data


def _image_to_json(data, fields):
    """
    数据中的文件字符串转换成json数组
    :param data: 数据
    :param fields: 要转换的字段
    :return:
    """
    if data:
        for i in fields:
            rets = []
            if data.get(i):
                imgs = json.loads(data[i])
                rets = [config.OSS_CNAME + img for img in imgs]
            data[i] = rets
    return data


def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def _changeTS2(data, fields):
    """
    转换时间戳
    :param data: 数据
    :param fields: 需要转换时间戳的字段
    :return:
    """
    for i in fields:
        if data.get(i):
            data[i] = data[i].strftime('%Y-%m-%d')
    return data

def _changeTS(data, fields):
    """
    转换时间戳
    :param data: 数据
    :param fields: 需要转换时间戳的字段
    :return:
    """
    for i in fields:
        if data.get(i):
            data[i] = data[i].strftime('%Y-%m-%d %H:%M:%S')
    return data

@gen.coroutine
def get_support_insurance_models():
    """
    获取支持投保产品型号
    :return:
    """
    sql = 'SELECT mt.model_id, mt.name as model_name , mc.name as category_name' \
        ' FROM broken_screen_insurance_standard  bsis ' \
        ' LEFT JOIN machine_type mt ON bsis.model_id = mt.model_id ' \
        ' LEFT JOIN machine_category mc ON bsis.category_id = mc.id ' \
        ' WHERE bsis.visible = 1 ' \
        ' AND mt.visibility = 1 ' \
        ' GROUP BY mt.model_id ' \
        ' ORDER BY mt.name ASC' 
    cur = yield DB_PR.execute(sql)
        
    data = cur.fetchall()
    raise gen.Return(data)

@gen.coroutine
def get_insurance_standard(model_id):
    """
    获取投保标准
    :param model_id:
    :return:
    """

    sql = 'SELECT id, model_id, `name`, amount, `month` ,activity_start_date, activity_end_date FROM broken_screen_insurance_standard ' \
        ' WHERE model_id = %s and visible =1 AND ( (activity_start_date <= current_date() and activity_end_date >= current_date()) OR standard_id != 5 ) '
    cur = yield DB_PR.execute(sql, model_id)
    data = cur.fetchall()
    if data:
        for i in data:
            i = _changeTS2(i, ['activity_start_date', 'activity_end_date'])
    raise gen.Return(data)


@gen.coroutine
def broken_screen_insurance_standard(modelId):
    """
    检查该机型投保标准
    """
    param = []
    sql = '''SELECT
                    id,
                    name,
                    pay_amount,
                    `month`,
                    amount,
                    insurance_times
                FROM
                    broken_screen_insurance_standard
                WHERE
                    model_id = %s
                    AND visible = 1
                ORDER BY
                    amount
                LIMIT 1 '''
    if modelId:
        param.append(modelId)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    if data:
        raise gen.Return(data)
    raise gen.Return(False)


@gen.coroutine
def get_insurance_standard_by_id(id):
    """
    获取投保类型
    :param id:
    :return:
    """

    sql = 'SELECT id, `name`, amount, `month`, pay_amount, insurance_times ' \
          'FROM broken_screen_insurance_standard ' \
          'WHERE id = %s and visible = 1 AND ' \
          '( (activity_start_date <= current_date() and activity_end_date >= current_date()) OR standard_id != 5 ) '
    cur = yield DB_PR.execute(sql, id)
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def add_insurance(uid, data):
    sn = _unique_sn()
    data = _image_to_str(data, ['image_path'])
    data['sn'] = sn
    data['uid'] = uid
    data['status'] = 100
    # 是电商直营直接生效
    if data['is_direct_sales'] == 1 and data['endpoint_channel'] == 'e_commerce':
        data['status'] = 300
    data['created_at'] = _now_date()

    keys = ['uid', 'endpoint', 'endpoint_name', 'top_agency', 'second_agency',
            'sn', 'barcode', 'name', 'phone', 'identity_card',
            'model_id', 'model_name', 'standard', 'month', 'image_path', 'created_at',
            'status', 'buy_date', 'pay_amount', 'amount',
            'is_direct_sales', 'type', 'endpoint_channel', 'insurance_times', 'insurance_times_remain']

    field = ','.join(keys)
    values = ','.join(['%s'] * len(keys))
    sql = 'insert into broken_screen_insurance (' + field + ') values (' + values + ')'
    ret = None
    try:
        param = tuple([data.get(i, '') for i in keys])
        yield DB_PR.execute(sql, param)
        ret = sn

        if data['is_direct_sales'] == 1 and data['endpoint_channel'] == 'e_commerce':
            param = OrderedDict()
            param['model'] = data['model_name']
            param['sn'] = data['barcode']
            param['money'] = str(data['amount']) + '元'
            param['expire'] = str(data['month']) + '个月'
            param = json.dumps(param)
            yield sms_model.broken_screen_sms('649437', data['phone'], param)
        else:
            send_bsi_message(sn)
    except:
        app_log.error('add insurance error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def save_broken_screen_insurance(bsisValues):
    """
    插入碎屏保
    """
    flag = 0
    try:
        fields = ', '.join('`{0}`'.format(k) for k in bsisValues.keys())
        values = ', '.join("'{0}'".format(v) if isinstance(v, basestring) else str(v) for v in bsisValues.values())
        app_log.info(fields, exc_info=True)
        app_log.info(values, exc_info=True)
        # 创建 SQL 插入语句
        sql = "INSERT INTO broken_screen_insurance ({0}) VALUES ({1})".format(fields, values)
        cur = yield DB_PR.execute(sql)
        flag = cur.lastrowid
    except:
        app_log.error('save broken_screen_insurance error', exc_info=True)
    raise gen.Return(flag)


def send_bsi_message(sn):
    # '0492',  # 陈国栋
    # '0134',  # 邹定维
    user = [
        {'work_num': '0292', 'name': '邱伟雄', 'user_id': 'de015c7843b6af1f2dcd1034fb27267a'},
        {'work_num': '0371', 'name': '廖锦麟', 'user_id': 'oai7233371299103d'},
        {'work_num': '0473', 'name': '张文涛', 'user_id': ''},
        {'work_num': '0419', 'name': '颜会会', 'user_id': ''},
        {'work_num': '0577', 'name': '杨会苗', 'user_id': ''},
        {'work_num': '1024', 'name': '苏金兰', 'user_id': ''},
        {'work_num': '0499', 'name': '黄知', 'user_id': ''},
        {'work_num': '0366', 'name': '何绮霞', 'user_id': ''},
        {'work_num': '1063', 'name': '聂榕玲', 'user_id': ''},
      # {'work_num': '0424', 'name': '钟月萍', 'user_id': 'oa102374'},
        {'work_num': '0712', 'name': '李娜', 'user_id': ''},
        {'work_num': '0505', 'name': '黄霞', 'user_id': ''},
        {'work_num': '1058', 'name': '梁妍茹', 'user_id': ''},
        {'work_num': '0270', 'name': '田婷', 'user_id': '416b8a601872ec408caeb3baf7477d5d'},
        {'work_num': '1311', 'name': '何双凤', 'user_id': 'oa103087'},
        {'work_num': '3040', 'name': '钱伟姗', 'user_id': ''},
        {'work_num': '0780', 'name': '黄健辉', 'user_id': ''},
        {'work_num': '0728', 'name': '陈颖虹', 'user_id': 'oa103194'},
        {'work_num': '0629', 'name': '欧阳诗婷', 'user_id': 'oa103216'},
        {'work_num': '0761', 'name': '谢燕琼', 'user_id': 'oa103240'},
        {'work_num': '0875', 'name': '陈泽钗', 'user_id': 'oa103236'},
        {'work_num': '0800', 'name': '黄华羽', 'user_id': 'oa103258'},
        {'work_num': '3068', 'name': '欧学涛', 'user_id': 'oa103345'},
        {'work_num': '3231', 'name': '杨倩', 'user_id': 'oa103322'},
        {'work_num': '0704', 'name': '卢彩蝶', 'user_id': 'oa103316'},
        {'work_num': '1062', 'name': '黄夏缘', 'user_id': 'oa102048'},
        {'work_num': '3165', 'name': '陈秋榕', 'user_id': 'oa105738'},
    ]
    # user = ['0431']  # 覃荣业
    s = '碎屏保订单（sn:{}）已提交，审核过期时长为1小时，请尽快审核。'.format(sn)
    # 发到小助手
    # user_work_num_list = [u.get('work_num') for u in user]
    # work_wechat_model.send_work_wechat_msg(user_work_num_list, s)
    # 发到认证中心
    user_id_list = filter(lambda x: x, [u.get('user_id') for u in user])
    work_wechat_model.send_message(user_id_list, s)


@gen.coroutine
def delete_insurance(endpoint, order_sn):
    sql = 'update broken_screen_insurance set `status` = %s where sn = %s and endpoint = %s'
    ret = None
    try:
        yield DB_PR.execute(sql, (-500, order_sn, endpoint))
        ret = True
    except:
        app_log.error('delete insurance error', exc_info=True)
    raise gen.Return(ret)



@gen.coroutine
def get_insurance_detail(uid, endpoint, order_sn):
    sql = 'SELECT bsi.id, bsi.`name`, bsi.barcode, bsi.phone, bsi.identity_card, bsi.model_id, bsi.model_name, ' \
          'bsi.amount, bsi.standard, bsis.`name` as standard_name, bsi.image_path, bsi.video_path, bsi.audited_at,' \
          ' bsi.audit_status, ' \
          'bsi.audit_opinion, bsi.`status`, bsis.`month`, bsi.created_at  FROM broken_screen_insurance bsi LEFT JOIN ' \
          'broken_screen_insurance_standard bsis ON bsi.standard = bsis.id WHERE endpoint = %s AND sn = %s '

    param = tuple([endpoint, order_sn])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    if data:
        data = _image_to_json(data, ['image_path', 'video_path'])
        data = _changeTS(data, ['created_at', 'audited_at'])
    raise gen.Return(data)


@gen.coroutine
def get_insurance_list(endpoint, standard, barcode, phone, identity_card, count, page, start, end, status):
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    field = ''
    if standard:
        field = field + ' and bsis.standard_id = %s ' % standard
    if barcode:
        field = field + ' and bsi.barcode = %s ' % barcode
    if phone:
        field = field + ' and bsi.phone = %s ' % phone
    if identity_card:
        field = field + ' and bsi.identity_card = %s ' % identity_card
    if start and end:
        field = field + ' and (bsi.created_at BETWEEN \'%s\' and \'%s\')' % (start, end)
    if status:
        field = field + ' and bsi.status = %s ' % status
    sql = 'SELECT bsi.sn, bsi.`name`, bsis.`name` as standard_name, bsi.amount, bsis.`month`, bsi.is_paid,  ' \
          'bsi.`status`, bsi.created_at, bsi.audited_at, bsi.buy_date, bsi.audit_opinion, bsi.is_supply, bsi.supply_at' \
          ' FROM broken_screen_insurance  bsi LEFT JOIN ' \
          'broken_screen_insurance_standard bsis ON bsi.standard = bsis.id WHERE endpoint = %s and status != %s ' \
          + field + ' order by bsi.created_at DESC limit %s,%s'
    param = tuple([endpoint, -500, offset, count + 1])
    cur = yield DB_PR.execute(sql, param)
    datas = cur.fetchall()
    if not datas:
        raise gen.Return(None)

    l = len(datas)
    is_End = l <= count
    size = l if is_End else count
    datas = datas[0:size]
    for data in datas:
        updated_at = _now_date()
        now = datetime.datetime.now()
        if data['status'] == -300:
            if (data.get('buy_date') + datetime.timedelta(days=7)).date() < now.date() and data.get('is_supply') == 0:
                yield DB_PR.execute('update broken_screen_insurance set status = -400 , updated_at = %s '
                                    'where sn = %s and endpoint = %s', (updated_at, data['sn'], endpoint))
                data['status'] = -400
            elif data.get('is_supply') == 1 and (data.get('supply_at') + datetime.timedelta(days=7)).date() < now.date() :
                yield DB_PR.execute('update broken_screen_insurance set status = -400 , updated_at = %s '
                                    'where sn = %s and endpoint = %s', (updated_at, data['sn'], endpoint))
        # 支付超时
        if data['status'] == 200 and data['is_paid'] == 0:
            # 购机时间超过7天  直接关闭订单
            if (data.get('buy_date') + datetime.timedelta(days=7)).date() < now.date() and data.get('is_supply') == 0:
                yield DB_PR.execute('update broken_screen_insurance set status = -400 , updated_at = %s '
                                    'where sn = %s and endpoint = %s', (updated_at, data['sn'], endpoint))
                data['status'] = -400
            elif data.get('is_supply') == 1 and (data.get('supply_at') + datetime.timedelta(days=7)).date() < now.date():
                yield DB_PR.execute('update broken_screen_insurance set status = -400 , updated_at = %s '
                                    'where sn = %s and endpoint = %s', (updated_at, data['sn'], endpoint))
                data['status'] = -400
            # 在购机时间内    允许重新提交申请
            elif (data.get('audited_at') + datetime.timedelta(hours=4)) < now:
                yield DB_PR.execute('update broken_screen_insurance set status = -300 , updated_at = %s '
                                    'where sn = %s and endpoint = %s', (updated_at, data['sn'], endpoint))
                data['status'] = -300
        # 保修期已过
        if data['status'] == 300:
            now = now.date()
            buy_date = data['created_at']
            # buy_day = datetime.datetime.strptime(data['created_at'].strftime('%Y-%m-%d'), '%Y-%m-%d' )
            # data['month'] = 12
            period = (buy_date + relativedelta(months=data['month']) - relativedelta(days=1)).date()
            if period < now:
                yield DB_PR.execute('update broken_screen_insurance set status = 500 , updated_at = %s '
                                    'where sn = %s and endpoint = %s', (updated_at, data['sn'], endpoint))
                data['status'] = 500
        del data['buy_date']
        del data['supply_at']
        # del data['audited_at']
        data = _changeTS(data, ['created_at', 'audited_at'])
    ret = dict()
    ret['is_End'] = is_End
    ret['size'] = size
    ret['data'] = datas
    raise gen.Return(ret)


@gen.coroutine
def check_is_exist(barcode):
    sql = 'SELECT `status` FROM broken_screen_insurance WHERE barcode = %s  AND `status` not IN (-400, -500) ORDER BY created_at DESC  '
    cur = yield DB_PR.execute(sql, barcode)
    data = cur.fetchone()
    if data:
        raise gen.Return(data['status'])
    raise gen.Return(None)


@gen.coroutine
def get_order(order_sn, barcode):
    if order_sn:
        sql = 'SELECT b.sn, b.endpoint, b.barcode, b.model_name, b.`status`, b.pay_amount, b.amount, b.month, ' \
              'b.buy_date, b.audited_at, b.created_at, b.is_paid, b.is_refund, b.phone, b.type, b.is_direct_sales, ' \
              'b.standard, s.`name` as standard_name, b.is_supply, b.supply_at FROM broken_screen_insurance b ' \
              'LEFT JOIN broken_screen_insurance_standard s ON  b.standard = s.id WHERE b.sn =  %s'
        param = tuple([order_sn])
    elif barcode:
        # 可能有多条记录-- 退款要求查正常的状态300
        sql = 'SELECT b.sn, b.endpoint, b.barcode, b.model_name, b.`status`, b.pay_amount, b.amount, b.month, ' \
              'b.buy_date, b.audited_at, b.created_at, b.is_paid, b.is_refund, b.phone, b.type, b.is_direct_sales, ' \
              'b.standard, s.`name` as standard_name FROM broken_screen_insurance b ' \
              'LEFT JOIN broken_screen_insurance_standard s ON  b.standard = s.id ' \
              'WHERE b.barcode = %s and b.status = 300'
        param = tuple([barcode])
    else:
        raise gen.Return(None)
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def order_verify(order_sn, barcode):
    sql = 'SELECT count(1) as count FROM broken_screen_insurance bsi ' \
          'WHERE bsi.sn != %s and bsi.barcode = %s and bsi.`status` in (300, 400)'
    param = tuple([order_sn, barcode])

    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchone()
    raise gen.Return(data['count'])


@gen.coroutine
def check_order(data):
    updated_at = _now_date()
    now = datetime.datetime.now()
    # 支付超时
    if data['status'] == 200 and data['is_paid'] == 0:
        # 购机时间超过3天  直接关闭订单
        if (data.get('buy_date') + datetime.timedelta(days=3)).date() < now.date() and data.get('is_supply') == 0:
            yield DB_PR.execute('update broken_screen_insurance set status = -400 , updated_at = %s '
                                'where sn = %s', (updated_at, data['sn']))
            raise gen.Return(-1)
        # 在购机时间内    允许重新提交申请
        elif (data.get('audited_at') + datetime.timedelta(hours=4)) < now:
            yield DB_PR.execute('update broken_screen_insurance set status = -300 , updated_at = %s '
                                'where sn = %s ', (updated_at, data['sn']))
            raise gen.Return(-2)
        raise gen.Return(1)
    else:
        raise gen.Return(-3)




@gen.coroutine
def trade_pre_order(order_sn, pay_amount, order):
    url = PAY_HOST + '/trade/pay/preorder'
    # print pay_amount
    param = {
        'out_trade_no': order_sn,
        'owner': "碎屏保",
        'trade_expire': str(14400),
        'subject': "（碎屏保）机型："+order['model_name']+'，投保标准：'+order['standard_name'],
        'detail': "（碎屏保）机型："+order['model_name']+'，投保标准：'+order['standard_name']+'，条码：'+order['barcode'],
        'total_fee': str(pay_amount),
        'notify_url': 'https://api-repair-hub.readboy.com/broken_screen_insurance/callback/pay'
    }
    # print param
    request = make_request(url, param, 'POST')
    client = httpclient.AsyncHTTPClient()
    ret = None
    try:
        response = yield client.fetch(request)
        # print response
        resp = tornado.escape.json_decode(response.body)
        ret = resp.get('data')
    except:
        app_log.error('pre order error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def pay_order_query(order_sn, pay_id):
    url = PAY_HOST + '/trade/pay/query'
    if order_sn:
        param = {
            'out_trade_no': order_sn
        }
    else:
        param = {
            'pay_id': pay_id
        }
    request = make_request(url, param, 'GET')
    # data = urllib.urlencode(data)
    client = httpclient.AsyncHTTPClient()
    ret = None
    try:
        response = yield client.fetch(request)
        resp = tornado.escape.json_decode(response.body)
        app_log.info(resp)
        ret = resp.get('data')
    except:
        app_log.error('pay order query error', exc_info=True)

    raise gen.Return(ret)


@gen.coroutine
def order_pay_sure(data, out_trade_no):
    sql = 'update broken_screen_insurance set is_paid = %s, paid_at = %s, status = %s, trade_plat = %s, pay_id = %s' \
          ' where sn = %s'
    ret = None
    if data.get('trade_plat') == 'alipay':
        trade_plat = 2
    else:
        trade_plat = 1
    try:

        yield DB_PR.execute(sql, (1, data['paid_at'], 300, trade_plat, data['pay_id'], out_trade_no))
        ret = True
    except:
        app_log.error('update data error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def order_return(sn):
    sql = 'update broken_screen_insurance set status = %s, updated_at = %s where sn = %s'
    ret = None
    now = _now_date()
    try:
        yield DB_PR.execute(sql, (700, now, sn))
        ret = True
    except:
        app_log.error('update data error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def broken_screen_refund(order_sn):
    """
    退款
    :param order_sn:
    :return:
    """
    url = PAY_HOST + '/trade/refund/create'
    # print pay_amount
    param = {
        'out_trade_no': order_sn,
        'notify_url': 'https://api-repair-hub.readboy.com/broken_screen_insurance/callback/refund'
    }
    # print param
    request = make_request(url, param, 'POST')
    client = httpclient.AsyncHTTPClient()
    ret = [None, '调用失败']
    try:
        response = yield client.fetch(request)

        resp = tornado.escape.json_decode(response.body)
        app_log.info(resp)
        if resp.get('ok') == 1:
            ret = [True, resp.get('data')]
        else:
            ret = [False, resp.get('msg')]
    except:
        app_log.error('pre order error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def refund_order_query(order_sn):
    url = PAY_HOST + '/trade/refund/query'
    param = {
        'out_trade_no': order_sn
    }
    request = make_request(url, param, 'GET')
    # data = urllib.urlencode(data)
    client = httpclient.AsyncHTTPClient()
    ret = [None, 'msg']
    try:
        response = yield client.fetch(request)
        resp = tornado.escape.json_decode(response.body)
        # print resp
        if resp.get('ok') == 1:
            ret = [resp.get('data'), 'success']
        else:
            ret = [False, resp['msg']]
    except:
        app_log.error('pay order query error', exc_info=True)

    raise gen.Return(ret)


@gen.coroutine
def order_refund_sure(data, out_trade_no):
    sql = 'update broken_screen_insurance set is_refund = %s , refund_amount=%s, refund_id = %s, refund_at = %s, ' \
          'status = %s, remark = %s where sn = %s'
    ret = None
    try:
        yield DB_PR.execute(sql, (1, data['refund_fee'], data['refund_id'], data['refund_at'], 600, '退机退款', out_trade_no))
        ret = True
    except:
        app_log.error('update data error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def check_has_broken_screen(barcode):
    sql = 'SELECT * FROM broken_screen_insurance WHERE barcode = %s AND `status` in (100, 200, 300)'
    cur = yield DB_PR.execute(sql, barcode)
    data = cur.fetchone()
    if data:
        data = _changeTS(data, ['audited_at', 'usaged_at', 'paid_at', 'buy_date', 'created_at', 'updated_at', 'refund_at'])
        data = _image_to_json(data, ['image_path'])
    raise gen.Return(data)


@gen.coroutine
def exchange_insurance(data, new_barcode, order_sn):
    sn = _unique_sn()
    data['sn'] = sn
    remark = data['remark']
    if remark:
        remark = remark + '--' + data['barcode']
    else:
        remark = '换机--' + data['barcode']
    data['barcode'] = new_barcode
    created_at = _now_date()

    # keys = ['uid', 'endpoint', 'endpoint_name', 'top_agency', 'second_agency', 'sn', 'barcode', 'name', 'phone',
    #         'identity_card', 'model_id', 'model_name', 'standard', 'month', 'image_path', 'video_path', 'auditor',
    #         'audit_status', 'audit_opinion', 'audited_at', 'status', 'created_at', 'usage_state', 'usaged_at',
    #         'buy_date', 'pay_amount', 'is_paid', 'paid_at']
    #
    # field = ','.join(keys)
    # values = ','.join(['%s'] * len(keys))
    # sql = 'insert into broken_screen_insurance (' + field + ') values (' + values + ')'
    ret = None
    trans = yield DB_PR.begin()
    try:
        # 换机用原来的记录
        yield trans.execute('update broken_screen_insurance set barcode = %s, remark = %s, created_at = %s '
                            'where sn = %s and endpoint = %s',
                            (new_barcode, remark, created_at, order_sn, data['endpoint']))
        # param = tuple([data.get(i) for i in keys])
        # yield trans.execute(sql, param)
        trans.commit()
        ret = True
    except:
        trans.rollback()
        app_log.error('add insurance error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def pay_overtime():
    sql = 'select sn, endpoint, audited_at from broken_screen_insurance where status = 200'
    cur = yield DB_PR.execute(sql)
    data = cur.fetchall()
    now = datetime.datetime.now()
    # print data
    # print now
    for i in data:
        # 审核时间大于1小时45分小于两小时
        if (i.get('audited_at') + datetime.timedelta(hours=1, minutes=45)) < now \
                < (i.get('audited_at') + datetime.timedelta(hours=2)):
            # 支付超时推送
            sms_model.pay_overtime_sms_push(i['sn'], i['endpoint'])


@gen.coroutine
def direct_sales_return(sn):
    sql = 'update broken_screen_insurance set status = %s, updated_at = %s where sn = %s'
    ret = None
    now = _now_date()
    try:
        yield DB_PR.execute(sql, (600, now, sn))
        ret = True
    except:
        app_log.error('update data error', exc_info=True)
    raise gen.Return(ret)

## 添加退款备注
@gen.coroutine
def add_refund_remark(sn):
    sql = 'update broken_screen_insurance set remark = %s, updated_at = %s where sn = %s'
    ret = None
    now = _now_date()
    try:
        yield DB_PR.execute(sql, ('后台退款', now, sn))
        ret = True
    except:
        app_log.error('update data error', exc_info=True)
    raise gen.Return(ret)