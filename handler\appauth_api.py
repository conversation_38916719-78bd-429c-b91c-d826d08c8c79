# encoding=utf-8
# __author__ = 'lch'

import tornado
from tornado import gen
from tornado.log import app_log
from tornado.httpclient import AsyncHTTPClient, HTTPRequest
import urllib
from hashlib import md5
import traceback

import util.com as com
from handler import ApiHandler
import store.appauth_model as appauth_model
import conf.config as config


def _secret(app):
    # return md5('auth://service.readboy.com/rbcare/20170316/' + app).hexdigest()
    return md5('auth://service.readboy.com/'+config.APP_NAME+'/'+config.APP_DATE+'/' + app).hexdigest()


class AppAuthList(ApiHandler):
    @gen.coroutine
    def get(self):
        auths = yield appauth_model.listAppAuth()
        self.success(auths)


class AppAuthAdd(ApiHandler):
    @gen.coroutine
    def get(self):
        appid = self.get_argument('appid')
        name = self.get_argument('name')
        desc = self.get_argument('desc', '')
        plat = self.get_argument('plat', 'android')

        auth = {
            'appid': appid,
            'appkey': _secret(appid),
            'name': name,
            'platform': plat,
            'description': desc,
            'status': 0
        }
        ok, msg = yield appauth_model.addAppAuth(auth)
        self.success(None) if ok else self.error(msg, com.ERROR_UNKNOWN)


class AppAuthGet(ApiHandler):
    @gen.coroutine
    def get(self):
        appid = self.get_argument('appid')
        auth = yield appauth_model.getAppAuthDetail(appid)
        self.success(auth) if auth else self.error(u'未找到appid=%s的应用授权' % appid, com.ERROR_UNKNOWN)


class AppAuthDel(ApiHandler):
    @gen.coroutine
    def get(self):
        appid = self.get_argument('appid')
        ret = yield appauth_model.delAppAuth(appid)
        self.success(None) if ret else self.error(u'删除应用授权失败', com.ERROR_UNKNOWN)


class AppAuthTest(ApiHandler):
    @gen.coroutine
    def get(self):
        appid = self.get_argument('appid')
        auth = yield appauth_model.getAppAuth(appid)
        if not auth:
            raise gen.Return(self.error(u'没找到appid=%s的应用授权' % appid, com.ERROR_EMPTY_DATA))

        client = AsyncHTTPClient()
        device_item = ['CLASSONE C3L', '-d863992030000081', appid, '1']
        device_id = '/'.join(device_item)
        t = 1489747653  # int(time.time())
        sn = md5(device_id+auth['appkey']+str(t)).hexdigest()
        params = {'device_id': device_id, 't': t, 'sn': sn}
        url = self.request.protocol+'://'+self.request.host+'/appauth/list?'+urllib.urlencode(params)
        request = HTTPRequest(url, method='GET', connect_timeout=10, request_timeout=10, validate_cert=False)
        ret = {
            'appid': appid,
            'appkey': auth['appkey'],
            'request_url': url,
            'request_params': params
        }
        try:
            response = yield client.fetch(request)
            data = tornado.escape.json_decode(response.body)
            ret['request_response'] = data
        except Exception as e:
            traceback.print_exc()
            ret['request_error'] = e.message
        self.success(ret)

