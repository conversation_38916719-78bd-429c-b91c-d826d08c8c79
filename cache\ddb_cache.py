#coding=utf-8
#encoding=utf-8
#__author__="QZL"

import time
import arrow
import redis
from conf import config
import json

#cache_disable = False
#rds = redis.StrictRedis().from_url(config.DATA_REDIS_URI, socket_timeout=1, socket_connect_timeout=1)
DT = 3600

class DATA_REDIS(object):
    def __init__(self):
        super(DATA_REDIS, self).__init__()
        try:
            self.rds = redis.StrictRedis().from_url(config.REDIS_URI, socket_timeout=1, socket_connect_timeout=1)
            self.cache_disable = False
        except Exception as e:
            print "redis error:", e
            self.cache_disable = True


data_cache = DATA_REDIS()


def _today_remain_seconds():
    """
    返回到当天24点的时间秒数
    :return:
    """
    now = arrow.now()
    delta = now.ceil('day')-now
    return int(delta.total_seconds())+10


def name_layer_sound():
    return 'ddb_layer_sound'


def get_layer_sound():
    if data_cache.cache_disable:
        return None
    try:
        name = name_layer_sound()
        data = data_cache.rds.get(name)
        data = json.loads(data)
    except Exception as e:
        if isinstance(e, redis.ConnectionError):
            data_cache.cache_disable = True
            print "redis_cache:get data fail!", e
        return None
    return data


def set_layer_sound(data):
    if data_cache.cache_disable or not data:
        return
    try:
        name = name_layer_sound()
        data = json.dumps(data)
        data_cache.rds.set(name, data, DT)
    except Exception as e:
        if isinstance(e, redis.ConnectionError):
            data_cache.cache_disable = True
        print "redis_cache:set data fail!", e
    return


def name_sound_list(key, page, number):
    return 'ddb_sound_list:%s_%s_%s' % (key, page, number)


def get_sound_list(key, page, number):
    if data_cache.cache_disable:
        return None
    try:
        name = name_sound_list(key, page, number)
        data = data_cache.rds.get(name)
        data = json.loads(data)
    except Exception as e:
        if isinstance(e, redis.ConnectionError):
            data_cache.cache_disable = True
            print "redis_cache:get data fail!", e
        return None
    return data


def set_sound_list(data, key, page, number):
    if data_cache.cache_disable or not data:
        return
    try:
        name = name_sound_list(key, page, number)
        data = json.dumps(data)
        data_cache.rds.set(name, data, DT)
    except Exception as e:
        if isinstance(e, redis.ConnectionError):
            data_cache.cache_disable = True
        print "redis_cache:set data fail!", e
    return


def name_books(key, page, number):
    return 'ddb_books:%s_%s_%s' % (key, page, number)


def get_books(key, page, number):
    if data_cache.cache_disable:
        return None
    try:
        name = name_books(key, page, number)
        data = data_cache.rds.get(name)
        data = json.loads(data)
    except Exception as e:
        if isinstance(e, redis.ConnectionError):
            data_cache.cache_disable = True
            print "redis_cache:get data fail!", e
        return None
    return data


def set_books(data, key, page, number):
    if data_cache.cache_disable or not data:
        return
    try:
        name = name_books(key, page, number)
        data = json.dumps(data)
        data_cache.rds.set(name, data, DT)
    except Exception as e:
        if isinstance(e, redis.ConnectionError):
            data_cache.cache_disable = True
        print "redis_cache:set data fail!", e
    return