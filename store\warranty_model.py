# coding=utf-8
import hashlib

import tornado
from tornado import gen, httpclient, escape
from tornado.log import app_log
import traceback
import time
from hashlib import md5
from store import DB_PR, DB, DB2

HTTP_TIMEOUT = 5
MAX_CLIENTS = 1000


httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)

key = 'Web'    
secret = 'M4S8tUB8OBBvIUN7'
yx_host = 'http://localhost:8000'
# yx_host = 'http://api-yxtest.readboy.com/api'
# yx_host = 'https://api-yx.readboy.com/api'

@gen.coroutine
def checkMachineWear(imei=None):
    if not imei:
        raise gen.Return(None)
    host = 'http://wear.readboy.com:8080/api/info?'
    url = host + 'imei=' + imei
    client = httpclient.AsyncHTTPClient()
    request = httpclient.HTTPRequest(url, method='GET', connect_timeout=HTTP_TIMEOUT,
                                  request_timeout=HTTP_TIMEOUT, validate_cert=False, auth_username='wearapi', auth_password='readboy999')
    try:
        response = yield client.fetch(request)
        data = tornado.escape.json_decode(response.body)
        # app_log.info('success: url=%s, ret=%s' % (url, data))
    except:
        app_log.error("Exception in exception handler: url=%s" % url, exc_info=True)
        data = None
        raise gen.Return(-1)
    if not data or not data.get('ok') or not data.get('data'):
        raise gen.Return(None)
    default_data = {
        'barcode': '',
        'model': '',
        'customer_name': '',
        'customer_phone': '',
        'buy_date': '',
        'salesman': '',
        'endpoint_name': '',
        'endpoint_phone': '',
        'endpoint_address': '',
        'status': 0
    }
    if data.get('data'):
        d = data.get('data')
        default_data['buy_date'] = d.get('device').get('bindtime')
        default_data['model'] = d.get('device').get('type')
        default_data['status'] = 1
    raise gen.Return(default_data)

@gen.coroutine
def _request(url):
    sn = md5(key+'-'+str(int(time.time()))+'-'+secret).hexdigest()
    authKey = key+'-'+str(int(time.time()))+'-'+sn
    url += '&authKey='+authKey
    client = httpclient.AsyncHTTPClient()
    request = httpclient.HTTPRequest(url, method='GET', connect_timeout=HTTP_TIMEOUT,
                                  request_timeout=HTTP_TIMEOUT, validate_cert=False)
    try:
        response = yield client.fetch(request)
        data = tornado.escape.json_decode(response.body)
        # app_log.info('success: url=%s, ret=%s' % (url, data))
    except:
        app_log.error("Exception in exception handler: url=%s" % url, exc_info=True)
        data = None
    raise gen.Return(data)


@gen.coroutine
def checkMES(code):
    params = 'keyword=%s' % code
    url = 'http://api-mes.readboy.com/index.php?s=/Api/Barcode/search.html&'+params
    ret = yield _request(url)
    if ret:
        if ret.get('errcode') == '0':
            data = ret.get('data')
            try:
                data['product_date'] = data['update_time']  # .strftime('%Y-%m-%d %H:%M:%S')
                del data['update_time']
            except:
                data = 0
            raise gen.Return(data)
    raise gen.Return(None)


@gen.coroutine
def checkMESV1(number, barcode, imei):
    if barcode:
        params = 'barcode=%s' % barcode
    elif imei:
        params = 'imei=%s' % imei
    elif number:
        params = 'number=%s' % number
    else:
        raise gen.Return(None)
    # params = 'keyword=%s' % code
    url = 'http://api-mes.readboy.com/index.php?s=/Api/Barcode/all.html&'+params
    ret = yield _request(url)
    if ret:
        if ret.get('errcode') == '0':
            data = ret.get('data')
            try:
                data['product_date'] = data['update_time']  # .strftime('%Y-%m-%d %H:%M:%S')
                del data['update_time']
            except:
                data = 0
            raise gen.Return(data)
    raise gen.Return(None)


@gen.coroutine
def check_endpoint(endpointId):
    """
    检查是否是直营电商终端
    """
    param = []
    sql = '''SELECT
                e.name,
                e.top_agency,
                e.second_agency,
                e.is_direct_sales,
                a.channel AS endpoint_channel
            FROM
                endpoint e
            LEFT JOIN agency a 
                        ON
                a.id = e.top_agency
            WHERE
                a.is_direct_sales = 1
                AND a.channel = 'e_commerce'
                AND e.is_direct_sales = 1
                AND e.id = %s '''
    if endpointId:
        param.append(endpointId)
    cur = yield DB.execute(sql, param)
    data = cur.fetchone()
    if data:
        raise gen.Return(data)
    raise gen.Return(False)


@gen.coroutine
def checkMachineByWarranty(number, barcode, imei):
    param = []
    if barcode:
        params = 'barcode = %s'
        param = [barcode]
    elif imei:
        params = 'imei = %s'
        param = [imei]
    elif number:
        params = 'number = %s'
        param = [number]
    else:
        raise gen.Return(None)
    sql = 'SELECT barcode, number, imei as imei1, model, model_id, state, ' \
          'product_date ' \
          'FROM warranty WHERE ' + params + ' and (status = 1 or status = 0)'
    cur = yield DB.execute(sql, param)
    ret = cur.fetchone()
    if ret:
        ret['product_date'] = ret['product_date'].strftime('%Y-%m-%d %H:%M:%S')
        ret['imei1'] = ret['imei1'] if ret['imei1'] else ''
        ret['number'] = ret['number'] if ret['number'] else ''
        ret['model'] = ret['model'] if ret['model'] else ''
        ret['model_id'] = ret['model_id'] if ret['model_id'] else ''
        ret['state'] = ret['state'] if ret['state'] else ''
        raise gen.Return(ret)
    raise gen.Return(None)


@gen.coroutine
def activateEndpoint(endpoint):
    ret = False
    try:
        yield DB.execute('update endpoint set active_at=NOW() where id=%s', endpoint)
        ret = True
    except:
        app_log.info('activateEndpoint %d failed' % endpoint)
        traceback.print_exc()
    raise gen.Return(ret)


@gen.coroutine
def getByBarcode(barcode):
    cur = yield DB.execute('Select w.barcode, w.salesman, w.customer_name, w.customer_phone, w.customer_addr, ' +
                           'w.buy_date, w.product_date, w.model_id, w.model, w.endpoint, w.status, '
                           'e.name as endpoint_name, ' +
                           'e.address as endpoint_address, e.phone as endpoint_phone, e.manager as endpoint_manager' +
                           ' from warranty w left JOIN endpoint e on w.endpoint=e.id where w.barcode=%s AND w.status=1',
                           barcode)
    warranty = cur.fetchone()
    if warranty:
        warranty['buy_date'] = warranty['buy_date'].strftime('%Y.%m.%d')
        warranty['product_date'] = warranty['product_date'].strftime('%Y.%m.%d')
    raise gen.Return(warranty)


@gen.coroutine
def getByImei(imei):
    cur = yield DB.execute('Select w.barcode, w.number, w.imei, w.salesman, w.customer_name, w.customer_phone, w.customer_addr, ' +
                           'w.buy_date, w.product_date, w.model_id, w.model, w.endpoint, w.status, '
                           'e.name as endpoint_name, ' +
                           'e.address as endpoint_address, e.phone as endpoint_phone, e.manager as endpoint_manager' +
                           ' from warranty w left JOIN endpoint e on w.endpoint=e.id where w.imei=%s AND w.status=1',
                           imei)
    warranty = cur.fetchone()
    if warranty:
        warranty['buy_date'] = warranty['buy_date'].strftime('%Y.%m.%d')
        warranty['product_date'] = warranty['product_date'].strftime('%Y.%m.%d')
    raise gen.Return(warranty)


@gen.coroutine
def getByNumber(number):
    cur = yield DB.execute('Select w.barcode, w.salesman, w.customer_name, w.customer_phone, w.customer_addr, ' +
                           'w.buy_date, w.product_date, w.model_id, w.model, w.endpoint, w.status, '
                           'e.name as endpoint_name, ' +
                           'e.address as endpoint_address, e.phone as endpoint_phone, e.manager as endpoint_manager' +
                           ' from warranty w left JOIN endpoint e on w.endpoint=e.id where w.number=%s '
                           'AND (w.status=1 or w.status = 0)',
                           number)
    warranty = cur.fetchone()
    if warranty:
        if warranty['status'] == 1:
            warranty['buy_date'] = warranty['buy_date'].strftime('%Y.%m.%d')
        warranty['product_date'] = warranty['product_date'].strftime('%Y.%m.%d')
    raise gen.Return(warranty)


@gen.coroutine
def get_warranty_info(barcode):
    sql = 'SELECT customer_name, customer_phone, buy_date, model_id, model, endpoint ' \
          'FROM warranty WHERE barcode = %s AND `status` = 1'
    cur = yield DB.execute(sql, barcode)
    data = cur.fetchone()
    raise gen.Return(data)


@gen.coroutine
def getByPhone(phone):
    cur = yield DB.execute(
        'Select w.barcode, w.number, w.imei, w.salesman, w.customer_sex, w.customer_name, w.customer_phone, '
        'w.customer_addr, w.student_name, w.student_school, w.student_sex, w.student_grade, w.student_birthday, '
        'w.buy_date, w.product_date, w.model, w.purchase_way, w.recommender, w.recommender_phone '
        'from warranty w where w.customer_phone= %s AND w.status=1', phone)
    cards = cur.fetchall()
    if cards:
        for warranty in cards:
            warranty['student_birthday'] = warranty['student_birthday'].strftime('%Y-%m-%d %H:%M:%S') if warranty.get(
                'student_birthday') else ''
            warranty['buy_date'] = warranty['buy_date'].strftime('%Y-%m-%d %H:%M:%S')
            warranty['product_date'] = warranty['product_date'].strftime('%Y-%m-%d %H:%M:%S')
    raise gen.Return(cards)