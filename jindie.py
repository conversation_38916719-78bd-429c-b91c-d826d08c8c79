#coding=utf-8
#encoding=utf-8
#__author__="QZL"
import datetime
import random
import traceback

import pymysql
import urllib, urllib2
import hashlib
import time
import json
import sys
reload(sys)
sys.setdefaultencoding( "utf-8" )

print_format = '{time}, {method}, {data}'

def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

DEBUG = False

key_k3 = 'K3CloudJX'
key_mes = 'Web'
secret_k3 = 'M4S8tU45OBBvIUN7'
secret_mes = 'M4S8tUB8OBBvIUN7'

def verify_k3(url):
    authKey = get_signature_k3()
    url += '&authKey='+authKey
    return url

def get_signature_k3():
    sn = hashlib.md5(key_k3+'-'+str(int(time.time()))+'-'+secret_k3).hexdigest()
    authKey = key_k3+'-'+str(int(time.time()))+'-'+sn
    return authKey

def verify_mes(url):
    authKey = get_signature_mes()
    url += '&authKey='+authKey
    return url

def get_signature_mes():
    sn = hashlib.md5(key_mes+'-'+str(int(time.time()))+'-'+secret_mes).hexdigest()
    authKey = key_mes+'-'+str(int(time.time()))+'-'+sn
    return authKey


connect = pymysql.connect(
    host='localhost',
    user='root',
    passwd='q',
    # db='rbcare',
    db='post_repair',
    charset='utf8',
    # host='localhost',
    # user='root',
    # passwd='q',
    # db='rbcare',
    # # db='readboydata',
    # charset='utf8',
) if DEBUG else pymysql.connect(
    host='rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',
    user='yxrepair',
    passwd='clC30J3DQLN3w7ma',
    db='post_repair',
    # db='readboydata',
    charset='utf8',
)
cursor = connect.cursor(cursor=pymysql.cursors.DictCursor)


def material_group():
    url = URL_MES + 's=/ApiWai/Material/type.html'
    url = verify_mes(url)
    print print_format.format(time=_now_date(), method='material_group', data=url)
    req = urllib2.Request(url)
    resp = urllib2.urlopen(req)
    data = resp.read()
    print print_format.format(time=_now_date(), method='material_group', data=data)


# 查看有无新物料,有则插入
def material():
    """
    查看有无新物料,有则插入
    :return:
    """
    url = URL_MES + 's=/ApiWai/Material/lists.html&page=1&rows=30000'
    url = verify_mes(url)
    print print_format.format(time=_now_date(), method='material', data=url)
    req = urllib2.Request(url)
    data = []
    try:
        resp = urllib2.urlopen(req)
        js = resp.read().encode('utf-8')
        if js:
            js = json.loads(js)
            if js.get("data"):
               data = js['data']
    except Exception as e:
        print print_format.format(time=_now_date(), method='material', data=e)
        strs = '寄修物料出bug了(若每间隔30分钟出现此提示,说明金蝶接口有问题,需要处理)' + url
        sendWXWorkMsg(strs, tousername=['0861']) ## 刘超华,黎猷金

    print print_format.format(time=_now_date(), method='material', data=len(data))
    for i in data:
        is_new = False
        d = dict()
        d['name'] = i.get('FName') or i.get('mat_name')
        d['code'] = i.get('FNumber') or i.get('mat_code')
        d['old_code'] = i.get('FOldNumber') or ''
        d['specification'] = i.get('FSpecification') or i.get('mat_model')
        d['group'] = i.get('FBaseProperty') or i.get('group_code')
        d['from'] = 1
        key = [
            'name',
            'code',
            'old_code',
            'specification',
            'group',
            'from',
        ]
        try:
            sql = 'select count(*) as count from material where code=%s'
            param = tuple([d.get('code')])
            cursor.execute(sql, param)
            result = cursor.fetchone()
            if not result or not result.get('count'):
                sql = 'insert into material (`name`,code,old_code,specification,`group`,`from`) values (%s, %s, %s, %s, %s,%s)'
                param = tuple([d.get(i) for i in key])
                cursor.execute(sql, param)
                connect.commit()
                is_new = True
            sql = 'select count(*) as count from material_new where code=%s'
            param = tuple([d.get('code')])
            cursor.execute(sql, param)
            result = cursor.fetchone()
            if not result or not result.get('count'):
                sql = 'insert into material_new (`name`,code,old_code,specification,`group`,`from`) values (%s, %s, %s, %s, %s,%s)'
                param = tuple([d.get(i) for i in key])
                cursor.execute(sql, param)
                connect.commit()
                is_new = True
        except Exception as e:
            print print_format.format(time=_now_date(), method='material', data=e)
            cursor.rollback()
        if is_new:
            print print_format.format(time=_now_date(), method='material', data=d)
    # connect.close()
    # print print_format.format(time=_now_date(), method='material', data=data)


# 查询库存数量
def material_quantity():
    """
    1、获取金蝶
    2、如果有数据更新material_new库存等于0
    3、更新material_new库存等于金蝶库存
    :return:
    """
    url = URL_K3 + 's=/KingDee/StkInventory/index.html&FStockFNumber=04.02'
    url = verify_k3(url)
    print print_format.format(time=_now_date(), method='material_quantity', data=url)
    req = urllib2.Request(url)
    resp = urllib2.urlopen(req)
    js = json.loads(resp.read())
    updated_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if js and js.get('errcode') == '0':
        data_k3 = js.get('data')
        print print_format.format(time=_now_date(), method='material_quantity', data='len_data_k3:%d' % len(data_k3))
        if not data_k3 or len(data_k3) <= 0:
            return
        sql = 'update material_new set quantity=0, updated_at=%s where `from`=1 and `group` != %s'
        cursor.execute(sql, (updated_at, '97004')) # 排除指定分类的物料
        connect.commit()
        for r in data_k3:
            code = r.get('FMaterialId.FNumber')
            if isinstance(code, basestring) and code.find('97004.') == 0:
                continue
            # print r
            sql = 'update material_new set quantity=quantity+%s, updated_at=%s where code=%s and `from`=1'
            param = tuple([r.get('FBaseQty'), updated_at, code])
            # print sql % param
            try:
                cursor.execute(sql, param)
                connect.commit()
            except Exception as e:
                print print_format.format(time=_now_date(), method='material_quantity', data=e)
            # break

def material_quantity_test():
    url = URL_K3 + 's=/KingDee/StkInventory/index.html&FStockFNumber=04.02'
    url = verify_k3(url)
    print print_format.format(time=_now_date(), method='material_quantity_test', data=url)
    req = urllib2.Request(url)
    resp = urllib2.urlopen(req)
    js = json.loads(resp.read())
    if js and js.get('errcode') == '0':
        data_k3 = js.get('data')
        print print_format.format(time=_now_date(), method='material_quantity_test', data='len_data_k3:%d' % len(data_k3))
        if not data_k3 or len(data_k3) <= 0:
            return
        # 检查金蝶返回数量是否都大于零
        l_qty = [x.get('FBaseQty') for x in data_k3]
        l_qty_1 = map(lambda x: x > 0, l_qty)
        l_qty_1_all = all(l_qty_1)
        print print_format.format(time=_now_date(), method='material_quantity_test', data='l_qty_1_all:%s' % l_qty_1_all)
        # 读取缺库存物料列表
        sql_select = 'select * from material_new '
        sql_where = 'where quantity <= 0 and price > 0 '
        sql = sql_select + sql_where
        cursor.execute(sql)
        data_material = cursor.fetchall()
        print print_format.format(time=_now_date(), method='material_quantity_test', data='len_data_material:%d' % len(data_material))
        # 检查金蝶返回是否有重复编码
        l_fnum = [x.get('FMaterialId.FNumber') for x in data_k3]
        d_fnum_count = {x : l_fnum.count(x) for x in l_fnum}
        d_fnum_count_1 = {}
        for k, v in d_fnum_count.items():
            if v > 1:
                d_fnum_count_1[k] = v
        print print_format.format(time=_now_date(), method='material_quantity_test', data='d_fnum_count_1:%s' % d_fnum_count_1)
        # 检查缺库存物料是否存在于金蝶返回
        l_material_code = [x.get('code') for x in data_material]
        d_material_k3_qty = {}
        for c in l_material_code:
            if c in l_fnum:
                d_material_k3_qty[c] = l_qty[l_fnum.index(c)]
        print print_format.format(time=_now_date(), method='material_quantity_test', data='d_material_k3_qty:%s' % d_material_k3_qty)


# 更新新物料表
def calculate_quantity():
    """
    1、查询已使用的物料
    2、遍历物料把库存减一
    :return:
    """
    # 获取没核销订单的维修配件
    sql_material = 'select pr_m.count as count, m.code as code from pr_material as pr_m ' \
          'join `order` as o on pr_m.pr_sn=o.sn ' \
          'left join pr_expense as pr_e on o.sn=pr_e.pr_sn ' \
          'left join material as m on pr_m.material_id=m.id ' \
          'where o.status >= 500 ' \
          'and pr_e.updated_at is null ' \
          'and o.connect not in (3,6) '
    cursor.execute(sql_material)
    data_material = cursor.fetchall()
    # 获取没核销订单的自选配件-代寄的小单缓存没算上
    sql_oa = 'select pr_oa.count as count, m.code as code from pr_optional_accessory as pr_oa ' \
          'join `order` as o on pr_oa.pr_sn=o.sn ' \
          'left join pr_expense as pr_e on o.sn=pr_e.pr_sn ' \
          'left join material as m on pr_oa.material_id=m.id ' \
          'where o.status >= 100 ' \
          'and pr_oa.status = 0 ' \
          'and pr_e.updated_at is null ' \
          'and o.connect not in (3,6) '
    cursor.execute(sql_oa)
    data_oa = cursor.fetchall()
    # 减去没核销的物料库存
    updated_at = _now_date()
    sql = 'update material_new set quantity=quantity-%s, updated_at=%s ' \
          'where code=%s and `from`=1 and `group` != %s '
    if data_material:
        for i in data_material:
            param = tuple([i.get('count'), updated_at, i.get('code'), '97004'])
            cursor.execute(sql, param)
            connect.commit()
    if data_oa:
        for i in data_oa:
            param = tuple([i.get('count'), updated_at, i.get('code'), '97004'])
            cursor.execute(sql, param)
            connect.commit()


def update_quantity():
    """
    1、更新material库存=0
    2、查询material_new库存大于0的物料
    3、更新material库存等于material_new
    :return:
    """
    updated_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    sql = 'update material set quantity=0 where `from`=1'
    cursor.execute(sql)
    connect.commit()
    sql = 'select code, quantity from material_new where quantity > 0'
    cursor.execute(sql)
    result = cursor.fetchall()
    if result:
        for i in result:
            sql = 'update material set quantity=%s, updated_at=%s  where code=%s'
            param = tuple([i.get('quantity'), updated_at, i.get('code')])
            cursor.execute(sql, param)
            connect.commit()


def sale_order():
    url = URL_K3 + 's=/KingDee/SalSaleOrder/index.html'
    url = verify_k3(url)
    req = urllib2.Request(url)
    resp = urllib2.urlopen(req)
    js = json.loads(resp.read())


def sendWXWorkMsg(content, touser=['UserID1'], tousername=['0861'], torealname=['李灿凤']):
    URL = 'https://oa.readboy.com/index.php?s=/Weixin/Api/send.html&auth_key='
    KEY = 'WeChatReadboyApi2018'
    # auth_key = "时间截-随机6位数-".md5("时间截-随机6位数-".md5(key))
    nowtime = str(int(time.time()))
    rand = str(random.randint(100000, 999999))
    auth_key = '-'.join(
        [nowtime, rand, hashlib.md5('-'.join([nowtime, rand, hashlib.md5(KEY).hexdigest()])).hexdigest()])
    URL = URL + auth_key
    data = {
        'touser': touser,
        'tousername': tousername,
        'torealname': torealname,
        'content': content
    }
    data = json.dumps(data)
    data = {
        'data': data,
    }
    try:
        data = urllib.urlencode(data)
        req = urllib2.Request(URL, data)
        res = urllib2.urlopen(req)
        print res.read()
    except:
        traceback.print_exc()


def main():
    # material_group()
    material()
    material_quantity()
    calculate_quantity()
    update_quantity()
    connect.close()

URL_K3 = 'http://192.168.16.247/k3cloud/index.php?'
URL_MES = 'http://192.168.16.247/V3/index.php?'

if __name__ == '__main__':
    main()
