# encoding=utf-8
# __author__ = 'lch'

import os
import tornado.httpserver
import tornado.ioloop
import tornado.options
import tornado.web
from tornado.options import define, options
from store import broken_screen_insurance_model
import conf.config as config
import urls

'''web主程序'''


define("port", default=8010, help="run on the given port", type=int)


class Application(tornado.web.Application):
    def __init__(self):
        settings = dict(
            debug=config.DEBUG,
            static_path=os.path.join(os.path.dirname(__file__), "docs")
        )
        if config.DEBUG:
            handlers = urls.handlers + [
                (r'/docs', tornado.web.StaticFileHandler, dict(path=settings['static_path'], default_filename='index.html')),
                (r'/docs/(.*)', tornado.web.StaticFileHandler, dict(path=settings['static_path'], default_filename='index.html'))
            ]
        else:
            handlers = urls.handlers
        tornado.web.Application.__init__(self, handlers, **settings)


if __name__ == "__main__":
    tornado.options.parse_command_line()
    tornado.options.logging = 'debug'
    http_server = tornado.httpserver.HTTPServer(Application(), xheaders=True)
    http_server.listen(options.port)
    tornado.ioloop.IOLoop.instance().set_blocking_log_threshold(3)
    tornado.ioloop.PeriodicCallback(broken_screen_insurance_model.pay_overtime, 300000).start()
    tornado.ioloop.IOLoop.instance().start()
