# encoding=utf-8
# __author__ = 'lch'

from tornado import gen
from tornado.log import app_log

import util.com as com
from handler import ApiHandler
import cache.user_cache as auth_cache
import store.user_model as user_model
import store.region_model as region_model


class HelloWorld(ApiHandler):
    def get(self):
        self.success(None, msg=u'欢迎使用读书郎Care服务')


class Login(ApiHandler):
    @gen.coroutine
    def post(self):
        username = self.get_argument('username', '', True)
        password = self.get_argument('password', '', True)
        access_token = self.get_argument('access_token', '', True)
        if username and password:
            # check username password
            user = yield user_model.passwordLogin(username, password)
        elif access_token:
            # check token
            user = yield user_model.tokenLogin(access_token)
        else:
            raise gen.Return(self.error(u'登录失败，参数错误', com.ERROR_PARAM))
        if not user:
            raise gen.Return(self.error(u'登录授权已失效' if access_token else u'用户名或密码错误', com.ERROR_BAD_TOKEN))
        if user['status'] == 0:
            raise gen.Return(self.error(u'用户已被禁用', com.ERROR_BAD_TOKEN))
        # app_log.info(user)
        # 返回基本的个人信息
        del user['status']
        avatar = user.get('avatar')
        if avatar:
            user['avatar'] = 'http://care.readboy.com/upload/'+avatar
        else:
            user['avatar'] = ""
        raise gen.Return(self.success(user))
