# coding=utf-8

from conf.config import ALIPAY_APPID, ALIPAY_APPNAME, ALIPAY_NOTIFY_URL, ALIPAY_PRIVATE_KEY, ALIPAY_PUBLIC_KEY, \
    AR_ALIPAY_NOTIFY_URL
from util.MyDefaultAlipayClient import MyDefaultAlipayClient
import tornado
from tornado import gen, httpclient
from tornado.log import app_log
from log import alipay_log
import traceback
import datetime
from store import DB_PR, DB
import urllib
from util import com
import hashlib, base64
import json
import time
import random
import urllib2
import threading
from urllib import quote
from alipay.aop.api.AlipayClientConfig import AlipayClientConfig
from alipay.aop.api.DefaultAlipayClient import DefaultAlipayClient
from alipay.aop.api.domain.AlipayTradeCloseModel import AlipayTradeCloseModel
from alipay.aop.api.request.AlipayTradeCloseRequest import AlipayTradeCloseRequest
from alipay.aop.api.domain.AlipayTradeAppPayModel import AlipayTradeAppPayModel
from alipay.aop.api.domain.AlipayTradeRefundModel import AlipayTradeRefundModel
from alipay.aop.api.request.AlipayTradeAppPayRequest import AlipayTradeAppPayRequest
from alipay.aop.api.request.AlipayTradeRefundRequest import AlipayTradeRefundRequest
from alipay.aop.api.domain.AlipayTradeQueryModel import AlipayTradeQueryModel
from alipay.aop.api.domain.AlipayTradeFastpayRefundQueryModel import AlipayTradeFastpayRefundQueryModel
from alipay.aop.api.request.AlipayTradeQueryRequest import AlipayTradeQueryRequest
from alipay.aop.api.request.AlipayTradeFastpayRefundQueryRequest import AlipayTradeFastpayRefundQueryRequest
import uuid
from alipay.aop.api.constant.ParamConstants import *
from alipay.aop.api.util.WebUtils import *
from alipay.aop.api.util.SignatureUtils import *
from alipay.aop.api.util.CommonUtils import *
from alipay.aop.api.util.EncryptUtils import *
import sys
reload(sys)
sys.setdefaultencoding( "utf-8" )

HTTP_TIMEOUT = 5
MAX_CLIENTS = 1000


httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)


def create_alipay_client():
    """
    实例化客户端
    :return:
    """
    ret = dict()
    for i in ALIPAY_APPID:
        """
        设置配置，包括支付宝网关地址、app_id、应用私钥、支付宝公钥等，其他配置值可以查看AlipayClientConfig的定义。
        """
        alipay_client_config = AlipayClientConfig()
        alipay_client_config.server_url = 'https://openapi.alipay.com/gateway.do'
        alipay_client_config.app_id = ALIPAY_APPID.get(i)
        alipay_client_config.app_private_key = ALIPAY_PRIVATE_KEY.get(i)
        alipay_client_config.alipay_public_key = ALIPAY_PUBLIC_KEY.get(i)
        """
            得到客户端对象。
            注意，一个alipay_client_config对象对应一个DefaultAlipayClient，定义DefaultAlipayClient对象后，alipay_client_config不得修改，如果想使用不同的配置，请定义不同的DefaultAlipayClient。
            logger参数用于打印日志，不传则不打印，建议传递。
            """
        client = MyDefaultAlipayClient(alipay_client_config=alipay_client_config)
        ret[i] = client
    return ret

alipay_client = create_alipay_client()


def create_pay(appid, out_trade_no, total_fee, notify_url_type):
    """
    构造唤起支付宝客户端支付时传递的请求串示例：alipay.trade.app.pay
    """
    # print out_trade_no
    model = AlipayTradeAppPayModel()
    model.timeout_express = "90m"                                                   # 最晚付款时间
    model.total_amount = str(total_fee)                                             # 付款金额  单位：元
    # model.product_code = "QUICK_MSECURITY_PAY"                                    # 销售产品码
    # model.body = "Iphone6 16G"                                                    #对一笔交易的具体描述信息
    model.subject = '-'.join([ALIPAY_APPNAME.get(appid, ''), '寄修费用'])            # 商品的标题
    model.out_trade_no = str(out_trade_no)                                          # 商户网站唯一订单号
    # model.notify_url = ALIPAY_NOTIFY_URL
    request = AlipayTradeAppPayRequest(biz_model=model)
    if notify_url_type == 1:
        request.notify_url = AR_ALIPAY_NOTIFY_URL
    else:
        request.notify_url = ALIPAY_NOTIFY_URL
    response = alipay_client.get(appid).sdk_execute(request)
    alipay_log.info(response)
    return response


def create_refund(appid, out_trade_no, total_fee, out_request_no):
    """
    构造唤起支付宝客户端支付时传递的请求串示例：alipay.trade.refund
    """
    # print out_trade_no
    model = AlipayTradeRefundModel()
    model.out_trade_no = str(out_trade_no)  # 商户网站唯一订单号
    model.refund_amount = total_fee                                                  # 退款金额  单位：元
    # model.product_code = "QUICK_MSECURITY_PAY"                                    # 销售产品码
    # model.body = "Iphone6 16G"                                                    #对一笔交易的具体描述信息
    model.refund_reason = '-'.join([ALIPAY_APPNAME.get(appid, ''), '退款费用'])            # 商品的标题
    model.out_request_no = str(out_request_no)
    # model.notify_url = ALIPAY_NOTIFY_URL
    request = AlipayTradeRefundRequest(biz_model=model)

    # request.notify_url = ALIPAY_NOTIFY_URL
    response = alipay_client.get(appid).execute(request)
    alipay_log.info(response)
    ret = None
    response = json.loads(response)
    if response.get('code') == '10000':
        ret = response.get('trade_no')
    return ret

@gen.coroutine
def query_pay(appid, readboy_sn):
    model = AlipayTradeQueryModel()
    model.out_trade_no = str(readboy_sn)
    request = AlipayTradeQueryRequest(biz_model=model)
    try:
        response = yield alipay_client.get(appid).gen_execute(request)
    except:
        alipay_log.error('query_pay error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(response)


@gen.coroutine
def query_refund(appid, out_trade_no, readboy_sn):
    model = AlipayTradeFastpayRefundQueryModel()
    model.out_trade_no = str(out_trade_no)
    model.out_request_no = str(readboy_sn)
    request = AlipayTradeFastpayRefundQueryRequest(biz_model=model)
    try:
        response = yield alipay_client.get(appid).gen_execute(request)
    except:
        alipay_log.error('query_refund error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(response)

@gen.coroutine
def close_pay(appid, readboy_sn):
    model = AlipayTradeCloseModel()
    model.out_trade_no = str(readboy_sn)
    request = AlipayTradeCloseRequest(biz_model=model)
    try:
        response = yield alipay_client.get(appid).gen_execute(request)
    except:
        alipay_log.error('close_pay error', exc_info=True)
        raise gen.Return(None)
    raise gen.Return(response)


def notify(data):
    if not data.get('app_id'):
        return False
    ### 转换成对应appid
    appid = data['app_id']
    my_appid = dict(zip(ALIPAY_APPID.values(), ALIPAY_APPID.keys()))
    return alipay_client.get(my_appid.get(appid)).notify_check(data)


def notify_return(status):
    """
    返回字符串
    :param status:
    :return:
    """
    if status:
        return 'success'
    else:
        return 'fail'


def main():
    pass


if __name__ == "__main__":
    t = time.time()
    # test()
    main()
    print time.time() - t