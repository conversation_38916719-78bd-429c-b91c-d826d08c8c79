# 订单查询接口文档

## 接口概述

根据关键字（6位条码后缀或11位手机号）查询相关订单，并返回每个订单的最新日志记录。

## 接口信息

- **URL**: `/order/query`
- **方法**: `GET`
- **描述**: 根据关键字查询订单最新日志

## 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| keyword | string | 是 | 查询关键字，支持6位条码后缀或11位手机号 |

### 关键字格式说明

1. **6位条码后缀**: 由字母和数字组成，长度为6位
   - 示例: `ABC123`, `123456`
   - 用于查询条码后6位匹配的订单

2. **11位手机号**: 以1开头的11位数字
   - 示例: `13800138000`
   - 用于查询该手机号下的所有订单

## 响应格式

### 成功响应

```json
{
    "ok": 1,
    "msg": "success",
    "data": {
        "keyword": "123456",
        "total_orders": 2,
        "logs": [
            {
                "id": 12345,
                "pr_sn": "20231201123456789012",
                "order_sn": "20231201123456789012",
                "pr_status": 800,
                "log_status": 800,
                "log_from": "app",
                "relation_key": "",
                "operation": "finish_order",
                "uid": 1001,
                "admin": 0,
                "title": "回寄产品已被接收，寄修服务结束",
                "remark": "",
                "date": "2023-12-01 15:30:00"
            },
            {
                "id": 12344,
                "pr_sn": "20231130123456789011",
                "order_sn": "20231130123456789011",
                "pr_status": 600,
                "log_status": 600,
                "log_from": "admin",
                "relation_key": "",
                "operation": "repair_complete",
                "uid": 0,
                "admin": 2001,
                "title": "维修完成，准备回寄",
                "remark": "更换屏幕",
                "date": "2023-11-30 14:20:00"
            }
        ]
    }
}
```

### 错误响应

#### 参数错误
```json
{
    "ok": 0,
    "msg": "关键字不能为空",
    "errno": 1001
}
```

```json
{
    "ok": 0,
    "msg": "关键字格式错误，应为6位条码后缀或11位手机号",
    "errno": 1001
}
```

#### 未找到数据
```json
{
    "ok": 0,
    "msg": "未找到相关订单",
    "errno": 1002
}
```

```json
{
    "ok": 0,
    "msg": "未找到订单日志",
    "errno": 1002
}
```

#### 系统错误
```json
{
    "ok": 0,
    "msg": "查询失败",
    "errno": 1000
}
```

## 响应字段说明

### data 字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| keyword | string | 查询的关键字 |
| total_orders | int | 找到的订单总数 |
| logs | array | 订单日志列表 |

### logs 数组中的日志对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | int | 日志记录ID |
| pr_sn | string | 寄修单号 |
| order_sn | string | 订单号（与pr_sn相同） |
| pr_status | int | 寄修状态 |
| log_status | int | 日志状态 |
| log_from | string | 日志来源（app/admin/web等） |
| relation_key | string | 关联键 |
| operation | string | 操作类型 |
| uid | int | 用户ID |
| admin | int | 管理员ID |
| title | string | 日志标题 |
| remark | string | 备注信息 |
| date | string | 日志时间（格式：YYYY-MM-DD HH:MM:SS） |

## 使用示例

### 查询条码后缀为123456的订单
```
GET /order/query?keyword=123456
```

### 查询手机号13800138000的订单
```
GET /order/query?keyword=13800138000
```

## 注意事项

1. 接口返回的是每个订单的**最新**日志记录，不是全部日志
2. 日志记录会经过过滤处理，排除某些特定状态的记录
3. 如果订单有特殊状态（如状态400），会进行额外的信息补充
4. 查询结果按订单创建时间倒序排列
5. 接口需要遵循系统的签名验证机制（如果启用）

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 1000 | 系统错误 |
| 1001 | 参数错误 |
| 1002 | 数据不存在 |
