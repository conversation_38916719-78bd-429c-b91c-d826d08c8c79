# coding=utf-8

import hashlib
import json
import random
import requests
import time
import urllib
import urllib2

from tornado.log import app_log


warning_reminder = [
    {'work_num': '0506', 'name': '刘超华', 'user_id': 'fa6af23a20fde589f22d1b4a18c802d6'},
]
warning_reminder_id_list = filter(lambda x: x, [u.get('user_id') for u in warning_reminder])


def send_work_wechat_msg(user, msg):
    """
    发送企业微信消息
    :param user: 用户数组
    :param msg: 消息文本
    :return:
    """
    URL = 'https://oa.readboy.com/index.php?s=/Weixin/Api/send.html&auth_key='
    KEY = 'WeChatReadboyApi2018'

    t = str(int(time.time()))
    rd = str(random.randint(100000, 999999))
    key = hashlib.md5(KEY).hexdigest()
    s = '-'.join([t, rd, key])
    md5 = hashlib.md5(s).hexdigest()
    sn = '-'.join([t, rd, md5])

    data = {
        'tousername': user,
        'content': '寄修中心提醒你：\n' + msg
    }
    data = json.dumps(data)
    data = {'data': data}
    data = urllib.urlencode(data)
    url = URL + sn
    try:
        req = urllib2.Request(url, data)
        resp = urllib2.urlopen(req)
    except:
        print "work weichat api failed！"


WORK_WX_CORP_ID = 'wx547e2dffb3a2fcbb'
WORK_WX_AGENT_ID = '1000027'
WORK_WX_AGENT_SECRET = 'GtOdej0is4z_Xp3pni5KW4YDs6K6OFvuLVUpUWpOGic'

def get_token():
    url = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken'
    payload = {'corpid': WORK_WX_CORP_ID, 'corpsecret': WORK_WX_AGENT_SECRET}
    req = requests.get(url, params=payload)
    res = req.json()
    if res['errmsg'] == 'ok':
        return res.get('access_token') or res.get('msgid', '')
    else:
        return res

def send_message(to_user_ids, text_content):
    """
    :param to_user_ids: 指定企业微信用户id数组
    :param text_content: 要发送的文本信息
    """
    # 参数
    if not isinstance(text_content, basestring):
        return
    to_user_str = '|'.join(to_user_ids)
    # 获取token
    token = get_token()
    if not isinstance(token, basestring):
        app_log.error('work_wechat get token error: %s' % token, exc_info=True)
        return
    # 发送消息
    url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send'
    payload_query = {'access_token': token}
    payload_json = {
        'touser': to_user_str,
        'msgtype': 'text',
        'agentid': WORK_WX_AGENT_ID,
        'text': {'content': '寄修中心提醒你：\n' + text_content},
        'safe': 0,
        'enable_id_trans': 0,
        'enable_duplicate_check': 0,
    }
    req = requests.post(url, params=payload_query, json=payload_json)
    if not req.status_code == requests.codes.ok:
        app_log.error('work_wechat send message error: %s' % req, exc_info=True)
        return
    res = req.json()
    if res.get('errmsg') != 'ok':
        app_log.error('work_wechat send message error: %s' % res, exc_info=True)
        return
