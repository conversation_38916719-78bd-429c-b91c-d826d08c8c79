# encoding=utf-8
# 测试新增的订单查询接口

import time
import hashlib

# 测试配置
BASE_URL = "http://localhost:8010"
ENDPOINT = "/order/query"

def test_order_query():
    """测试订单查询接口"""
    
    # 测试用例
    test_cases = [
        {
            "name": "测试6位条码后缀",
            "keyword": "123456",
            "expected_status": "success or empty"
        },
        {
            "name": "测试11位手机号",
            "keyword": "13800138000",
            "expected_status": "success or empty"
        },
        {
            "name": "测试空关键字",
            "keyword": "",
            "expected_status": "error"
        },
        {
            "name": "测试错误格式关键字",
            "keyword": "12345",
            "expected_status": "error"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        
        # 构造请求参数
        params = {
            "keyword": test_case["keyword"],
            # 添加必要的签名参数（如果需要的话）
            "ua": "1/test/test/test/1.0/test",
            "t": "1234567890",
            "sn": "test_signature"
        }
        
        try:
            # 发送请求
            response = requests.get(f"{BASE_URL}{ENDPOINT}", params=params, timeout=10)
            
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"解析结果: {json.dumps(data, ensure_ascii=False, indent=2)}")
                except json.JSONDecodeError:
                    print("响应不是有效的JSON格式")
            
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
        
        print("-" * 50)

def get_repair_param_with_fixed_timestamp():
    t = int(time.time())
    appid = 'yx.readboy.com'
    appkey = '710eb100ff2120cbcde0d78e532de8cg'
    device_item = ['1', '', '', appid, '', '']
    device_id = '/'.join(device_item)
    sn = hashlib.md5((device_id + appkey + str(t)).encode('utf-8')).hexdigest()
    params = {
        't': t,
        'ua': device_id,
        'sn': sn
    }
    return params

if __name__ == "__main__":
    print("开始测试订单查询接口...")
    print(get_repair_param_with_fixed_timestamp())
    print("\n测试完成！")
