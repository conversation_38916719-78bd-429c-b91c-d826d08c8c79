[String] ${POST_REPAIR_API_IN_PORT} = '8010'
[String] ${POST_REPAIR_API_OUT_PORT} = '43186'
[String] ${DEBUGPY_IN_PORT} = '5678'
[String] ${DEBUGPY_OUT_PORT} = '45678'
[String] ${APP_PATH} = '/usr/src/app'

docker run -d `
--name=dev_env-post_repair_api-env_pip-master `
-e TZ=Asia/Shanghai `
-p ${POST_REPAIR_API_OUT_PORT}:${POST_REPAIR_API_IN_PORT} `
-p ${DEBUGPY_OUT_PORT}:${DEBUGPY_IN_PORT} `
--mount type=bind,source=${pwd},target=${APP_PATH} `
post_repair_api-env_pip:master `
python -m debugpy --listen 0.0.0.0:${DEBUGPY_IN_PORT} ${APP_PATH}/app.py `
