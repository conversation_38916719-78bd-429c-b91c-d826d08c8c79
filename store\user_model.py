# encoding=utf-8
# __author__ = 'lch'
import urllib

from store import DB, DB_PR
# import util.com as com
from tornado import gen, httpclient, escape
import bcrypt
from hashlib import md5
import time
import tornado
from tornado.log import app_log
import cache.user_cache as auth_cache
import datetime
import copy
from util import com
from log import db_log

TOKEN_TTL = 3000*24*3600  # seconds
HTTP_TIMEOUT = 5
MAX_CLIENTS = 1000


httpclient.AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient", max_clients=MAX_CLIENTS)

def _password_verify(password, pwhash):
    salt = pwhash[:29]
    curhash = bcrypt.hashpw(password.encode('utf-8'), salt.encode('utf-8'))
    return curhash == pwhash


@gen.coroutine
def passwordLogin(username, password):
    cur = yield DB.execute('Select u.id, u.username, u.password, u.name, u.avatar, u.status, ue.endpoint, '+
                           'e.name as endpoint_name, e.address as endpoint_address, e.phone as endpoint_phone, '+
                           'e.manager as endpoint_manager from admin_users u '+
                           'RIGHT JOIN user_endpoint ue on u.id=ue.uid LEFT JOIN endpoint e on ue.endpoint=e.id '+
                           'where u.username=%s', username)
    user = cur.fetchone()
    if user:
        if user['status'] == 0:
            raise gen.Return(user)
        if _password_verify(password, user['password']):
            del user['password']
            uid = user['id']
            auth = auth_cache.getAuth(uid)
            now = int(time.time())
            if not auth:
                token = md5(username+password+str(now)).hexdigest()
            else:
                token = auth['access_token']
            expire = now + TOKEN_TTL
            auth_cache.setAuth(uid, token, expire)
            user['access_token'] = token
            user['access_expire'] = expire
            user['uid'] = uid
            del user['id']
            raise gen.Return(user)
    raise gen.Return(None)


@gen.coroutine
def tokenLogin(token):
    auth = auth_cache.checkAuth(token)
    user = None
    if auth:
        cur = yield DB.execute('Select u.username, u.name, u.avatar, u.status, ue.endpoint, '+
                               'e.name as endpoint_name, e.address as endpoint_address, e.phone as endpoint_phone, '+
                               'e.manager as endpoint_manager from admin_users u '+
                               'RIGHT JOIN user_endpoint ue on u.id=ue.uid LEFT JOIN endpoint e on ue.endpoint=e.id '+
                               'where u.id=%s', auth['uid'])
        user = cur.fetchone()
        if not user or user['status'] == 0:
            auth_cache.delAuthToken(token)
            raise gen.Return(None)
        user.update(auth)
    raise gen.Return(user)


@gen.coroutine
def checkToken(token=None):
    if not token:
        raise gen.Return(None)
    ret = None
    data = None
    host = 'http://api-auth-hub.readboy.com/oauth2/auth?appid=postrepair_api&appsecret=5e534df7f0e546aff573229e5927bd28'
    url = host + '&access_token=' + token
    client = httpclient.AsyncHTTPClient()
    request = httpclient.HTTPRequest(url, method='GET', connect_timeout=HTTP_TIMEOUT,
                                  request_timeout=HTTP_TIMEOUT, validate_cert=False)
    try:
        response = yield client.fetch(request)
        data = tornado.escape.json_decode(response.body)
        app_log.info('success: url=%s, ret=%s' % (url, data))
    except:
        app_log.error("Exception in exception handler: url=%s" % url, exc_info=True)
        raise gen.Return(ret)
    if not data or data.get('ok') != 1:
        raise gen.Return(ret)
    if data.get('data'):
        ret = data.get('data')
    raise gen.Return(ret)


@gen.coroutine
def get_wechat_userinfo(access_token):
    host = 'http://api-auth-hub.readboy.com/userinfo'
    ua = "1/yx1.0/1/postrepair_api/1.0/1"
    t = int(time.time())
    sn = md5(ua+"5e534df7f0e546aff573229e5927bd28"+str(t)).hexdigest()
    param = {
        "ua": ua,
        "t": t,
        "sn": sn,
        "access_token": access_token,
        "platform": "wechat"
    }
    path = urllib.urlencode(param)
    url = host + "?" + path
    ret = None
    data = None
    client = httpclient.AsyncHTTPClient()
    request = httpclient.HTTPRequest(url, method='GET', connect_timeout=HTTP_TIMEOUT,
                                     request_timeout=HTTP_TIMEOUT, validate_cert=False)
    try:
        response = yield client.fetch(request)
        data = tornado.escape.json_decode(response.body)
        app_log.info('success: url=%s, ret=%s' % (url, data))
    except:
        app_log.error("Exception in exception handler: url=%s" % url, exc_info=True)
    if not data or data.get("ok") != 1:
        raise gen.Return(ret)
    if data.get("data"):
        ret = data['data']
    raise gen.Return(ret)


@gen.coroutine
def check_roles(uid):
    cur2 = yield DB_PR.execute('select r.id, r.slug, r.name from admin_users u RIGHT JOIN admin_role_users ru '
                            'on u.id=ru.user_id LEFT JOIN admin_roles r on ru.role_id=r.id where u.id = %s', uid)
    role_lst = cur2.fetchall()
    ret = []
    if role_lst:
        for i in role_lst:
            ret.append(i['slug'])

    raise gen.Return(ret)


@gen.coroutine
def storeUser(data, token):
    ### 保存到数据库中
    data['updated_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    data['auth_center_id'] = data.get('id')
    data['account_center_id'] = data.get('ucid')
    key = [
        'auth_center_id',
        'account_center_id',
        'username',
        'name',
        'phone',
        'avatar',
        'access_token',
        'access_expire',
        'updated_at',
    ]
    key_copy = copy.copy(key)
    for i in key_copy:
        if not data.get(i, None):
            key.remove(i)
    update = '=%s,'.join(key) + '=%s'
    fields = ','.join(key)
    values = ','.join(['%s']*len(key))
    # sql = 'insert into admin_users (' + fields + ') values ('+ values +') ON DUPLICATE KEY UPDATE ' + update
    # param = tuple([data.get(i) for i in key]*2)
    # print sql % param
    ret = None
    trans = yield DB_PR.begin()
    try:
        # yield trans.execute(sql, param)
        sql = 'select count(*) as count from admin_users where auth_center_id=%s'
        param = tuple([data.get('id')])
        cur = yield trans.execute(sql, param)
        user = cur.fetchone()
        if user and user.get('count'):
            sql = 'update admin_users set ' + update + ' where auth_center_id=%s'
            param = tuple([data.get(i) for i in key] + [data.get('id')])
            yield trans.execute(sql, param)
        else:
            sql = 'insert into admin_users (' + fields + ') values ('+ values +')'
            param = tuple([data.get(i) for i in key])
            yield trans.execute(sql, param)
        cur = yield trans.execute('select id, open_id from admin_users where auth_center_id=%s',
                                  data.get('auth_center_id'))
        query = cur.fetchone()
        if not query.get('id'):
            raise Exception('not found admin_user auth_center_id=%s' % data.get('auth_center_id'))
        if not query.get("open_id"):
            wechat_userinfo = yield get_wechat_userinfo(token)
            if wechat_userinfo and wechat_userinfo.get("platform_info"):
                platform_info = wechat_userinfo['platform_info']
                if platform_info.get("username"):
                    yield trans.execute('update admin_users set open_id = %s where id = %s',
                                        (platform_info['username'], query['id']))
            else:
                if data.get('account_center_id'):
                    wechat_userinfo = yield get_profile(data.get('account_center_id'))
                    if wechat_userinfo:
                        yield trans.execute('update admin_users set open_id = %s where id = %s',
                                            (wechat_userinfo, query['id']))
        trans.commit()
        uid = com.safeInt(query.get('id'))
        token = data.get('access_token')
        expire = data.get('access_expire')
        auth_cache.setAuth(uid, token, expire)
        ret = {'uid': com.safeInt(query.get('id')), 'access_token': data.get('access_token'), 'access_expire': data.get('access_expire')}
    except:
        trans.rollback()
        app_log.error('store user error', exc_info=True)
    raise gen.Return(ret)


@gen.coroutine
def get_user_phone(uid):
    """
    获取用户手机号
    :param uid:
    :return:
    """
    sql = 'select phone from admin_users where id = %s'
    cur = yield DB_PR.execute(sql, uid)
    data = cur.fetchone()
    if data.get('phone'):
        raise gen.Return(data['phone'])
    raise gen.Return(None)


@gen.coroutine
def get_profile(uid):

    host = 'https://account.readboy.com/native/searchUid'
    app_id = "com.readboy.post_repair"
    app_sec = "9938ab8e2ba870179aba8067665c2391"
    t = int(time.time())
    sn = "********" + str(t) + md5(str(t) + app_sec + md5(app_id).hexdigest()).hexdigest()+app_id
    param = {
        "sn": sn,
        "key": uid,
    }
    path = urllib.urlencode(param)
    url = host + "?" + path
    ret = None
    data = None
    client = httpclient.AsyncHTTPClient()
    request = httpclient.HTTPRequest(url, method='GET', connect_timeout=HTTP_TIMEOUT,
                                     request_timeout=HTTP_TIMEOUT, validate_cert=False)
    try:
        response = yield client.fetch(request)
        data = tornado.escape.json_decode(response.body)
        app_log.info('success: url=%s, ret=%s' % (url, data))
    except:
        app_log.error("Exception in exception handler: url=%s" % url, exc_info=True)
    if data.get("weixin"):
        ret = data['weixin']
    raise gen.Return(ret)


@gen.coroutine
def get_endpoint_id_by_uid(uid):
    sql = 'select * from user_endpoint where uid = %s '
    cur = yield DB.execute(sql, uid)
    data = cur.fetchone()
    if data and data.get('endpoint'):
        raise gen.Return(data['endpoint'])
    raise gen.Return(None)
