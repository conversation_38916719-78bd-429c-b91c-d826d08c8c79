# encoding=utf-8
# __author__ = 'lch'
import string
from collections import OrderedDict
import sys
import hashlib
import time
import random
import json
import urllib, urllib2
import pymysql
import datetime
import logging
logging.basicConfig()
reload(sys)
sys.setdefaultencoding('utf-8')


DEBUG = True
PAY_HOST = 'https://api-pay-hub.readboy.com'
APP_ID = 'screen_insurance_h5'
APP_SECRET = 'c10eb100ff2850cbcde0d78e532de8c6'
connect = pymysql.connect(
    host='rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com',
    user='rbcare',
    passwd='9ZS0sebU2IfMTp3U',
    db='post_repair',
#   db='post_repair_test',
    charset='utf8',
)
cursor = connect.cursor(cursor=pymysql.cursors.DictCursor)


def _now_date():
    """
    获取当前日期时间字符串
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')


class ComplexEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            return str(obj)
        else:
            return json.JSONEncoder.default(self, obj)


def print_log(method, data=None, time=None):
    at = OrderedDict()
    at['time'] = time if time else _now_date()
    at['method'] = method
    dic = OrderedDict()
    dic['at'] = at
    dic['data'] = data
    print json.dumps(dic, ensure_ascii=False, cls=ComplexEncoder)


def nonce_str():
    char = string.ascii_letters + string.digits
    return "".join(random.choice(char) for _ in range(32))


def get_sign(obj):
    """生成签名"""
    od = OrderedDict(sorted(obj.items()))
    s = '&'.join([k + '=' + v for k, v in od.iteritems()])

    # 签名步骤二：在string后加入KEY
    app_key = '&appsecret=' + APP_SECRET
    StringA = "{0}{1}".format(s, app_key)
    # 签名步骤三：加密
    sign = hashlib.md5(StringA).hexdigest().upper()
    return sign


def make_data(param):
    default_param = {
        'appid': 'screen_insurance_h5',
        'nonce_str': nonce_str(),
    }
    param.update(default_param)
    param['sign'] = get_sign(param)
    # print param
    return param


def trade_pay_query(out_trade_no):
    url_base = PAY_HOST + '/trade/pay/query'
    param = {
        'out_trade_no': out_trade_no,
    }
    data = make_data(param)
    data = urllib.urlencode(data)
    header = {
        'content-type': 'application/x-www-form-urlencoded',
    }
    url = '%s?%s' % (url_base, data)
    print_log(method='trade_pay_query', data=url)
    req = urllib2.Request(url, headers=header)
    resp = urllib2.urlopen(req)
    read = resp.read()
    print_log(method='trade_pay_query', data=read)
    js = json.loads(read)
    if js.get('ok') == 1:
        return js.get('data')
    return None


def order_pay_sure(data, out_trade_no):
    sql = 'update broken_screen_insurance set is_paid = %s, paid_at = %s, status = %s, trade_plat = %s, pay_id = %s ' \
          'where sn = %s'
    ret = None
    if data.get('trade_plat') == 'alipay':
        trade_plat = 2
    else:
        trade_plat = 1
    try:
        cursor.execute(sql, (1, data['paid_at'], 300, trade_plat, data['pay_id'], out_trade_no))
        connect.commit()
        ret = True
    except Exception as e:
        print_log(method='order_pay_sure', data=e)
    return ret


def manual_query():
    cnt = 0
    while True:
        input_str_1 = raw_input('[%d] input broken_screen_insurance sn > ' % cnt)
        print_log(method='manual_query', data=input_str_1)
        if input_str_1 == 'x' or input_str_1 == 'exit':
            break
        else:
            cnt += 1
            ret = trade_pay_query(input_str_1)
            if ret and ret.get('status') == 1:
                print_log(method='manual_query', data=ret)
                input_str_2 = raw_input('[%d] query check if do sure? (y/n) > ' % cnt)
                if input_str_2 == 'y':
                    print_log(method='manual_query', data='yes order_pay_sure')
                    order_pay_sure(ret, input_str_1)
        print


def get_order():
    sql = 'SELECT sn, buy_date, is_supply, audited_at FROM broken_screen_insurance ' \
          'WHERE `status` = 200 AND is_paid = 0 '
    cursor.execute(sql)
    data = cursor.fetchall()
    print_log(method='get_order', data=data)
    return data


if __name__ == '__main__':
    print
    while True:
        input_str_1 = raw_input('input method to do > ')
        print_log(method='main', data=input_str_1)
        if input_str_1 == 'x' or input_str_1 == 'exit':
            break
        else:
            if input_str_1 == 'manual_query':
                ret = manual_query()
                print_log(method='main', data=ret)
            elif input_str_1 == 'get_order':
                ret = get_order()
                print_log(method='main', data=ret)
            else:
                print_log(method='main', data='invalid method name')
        print
    print

"""
now = datetime.datetime.now()
print_log(method='main', data={'now': now, 'add4hour': now + datetime.timedelta(hours=4)})
"""
""" 
input_str_1 = '20201230171205spb771675'
ret = {
    'trade_plat': 'alipay',
    'paid_at': '2020-12-30 17:15:57', # '2020-12-30 17:15:59'
    'pay_id': '202012301715520032623963854741'
}
order_pay_sure(ret, input_str_1)
"""

