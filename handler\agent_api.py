# encoding=utf-8
# __author__ = 'qry'
import datetime
import json

from handler import <PERSON>piHand<PERSON>
from tornado import gen
from store import agent_model, warranty_model, repair_model
from util import com


class PseudoCode(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        if self.roles_data:
            if len(self.roles_data) > 0:
                for role in self.roles_data:
                    if role in com.SERVICE_SLUG:
                        break
                else:
                    raise gen.Return(self.error('权限不足', com.ERROR_NOT_PERMIT))
            else:
                raise gen.Return(self.error('权限不足', com.ERROR_NOT_PERMIT))
        else:
            raise gen.Return(self.error('token无效', com.ERROR_BAD_TOKEN))
        # uid = 1
        model_id = self.get_argument('model_id', 0)
        model_name = self.get_argument('model_name', '')
        if not model_id or not model_name:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        data = yield agent_model.get_pseudo_code(uid, model_id, model_name)
        raise gen.Return(self.success(data) if data else self.error(u'未获取到伪码，请重新申请', com.ERROR_PARAM))


class PR_PeriodAgent(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        barcode = self.get_argument('barcode', None)
        if not barcode:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        # order_cache = yield agent_model.check_order_cache(uid, barcode)
        # if order_cache:
        #     raise gen.Return(self.error(u'此条码已有一条保存记录', com.ERROR_PARAM))
        pseudo_code = yield agent_model.check_model_id_pseudo_code(barcode)
        # 判断是否是伪码
        machine = {}
        if pseudo_code:
            if pseudo_code['status'] == 1:
                raise gen.Return(self.error(u'此伪码已被使用', com.ERROR_PARAM))
            model_name = pseudo_code['model_name']
            model_id = pseudo_code['model_id']
        else:
            machine = yield warranty_model.checkMachineByWarranty(None, barcode, None)
            if not machine:
                machine = yield warranty_model.checkMESV1(None, barcode, None)
            if not machine or not machine.get('barcode'):
                raise gen.Return(self.error(u'未找到条码', com.ERROR_PARAM))
            barcode = machine['barcode']
            model_name = machine['model']
            model_id = machine['model_id']
        model_info = yield repair_model.check_model(model_id)
        if not model_info or not model_info.get('model_id'):
            raise gen.Return(self.error(u'寄修服务未支持该机型', com.ERROR_BAD_REQUEST))
        model_id = model_info['model_id']
        category_id = yield repair_model.check_category_id(model_id)
        # 检查机器是否正在维修中
        check = yield repair_model.check_barcode_order(barcode)
        if check:
            check = yield repair_model.check_barcode_order(barcode, uid)
            if check:
                data = dict()
                data['can'] = False
                data['history'] = check
                data['prompt_enable'] = model_info['prompt_enable']
                data['prompt_info'] = model_info['prompt_info']
                raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
            raise gen.Return(self.error(u'机器正在寄修中', com.ERROR_ALREADY_EXISTS))
        warranty = yield warranty_model.getByBarcode(barcode)
        in_period = repair_model.in_period(warranty)
        # 手表服务器验证
        if not pseudo_code and not warranty and machine.get('imei1') and category_id in [3]:
            pass
            # warranty = yield warranty_model.checkMachineWear(machine['imei1'])
            # if isinstance(warranty, dict):
            #     if warranty.get('buy_date') == '0000-00-00 00:00:00':
            #         warranty['buy_date'] = None
            #     else:
            #         warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'], '%Y-%m-%d %H:%M:%S').strftime('%Y.%m.%d') if warranty.get('buy_date') else None
            #     in_period = repair_model.in_period(warranty)
        screen_insurance = yield repair_model.get_broken_screen_insurance(barcode)
        in_si_period, used_screen_insurance = yield repair_model.in_si_period(screen_insurance)
        data = dict()
        data['can'] = True
        data['model_name'] = model_name
        data['model_id'] = model_id
        data['barcode'] = barcode
        data['in_period'] = in_period
        data['has_warranty'] = 1 if warranty and warranty['status'] == 1 else 0
        data['in_si_period'] = in_si_period
        data['used_screen_insurance'] = used_screen_insurance
        data['has_screen_insurance'] = 1 if screen_insurance else 0
        data['category_name'] = yield repair_model.get_machine_category_name(category_id)
        data['endpoint'] = yield repair_model.endpoints()
        default_contact = yield repair_model.get_default_contact(uid)
        data['contact'] = yield repair_model.contact(default_contact)
        data['express_fee_description'] = yield repair_model.explain('express_fee_description')
        data['prompt_enable'] = model_info['prompt_enable']
        data['prompt_info'] = model_info['prompt_info']
        data['image_tip'] = "若实物故障与图片不符，因此导致的维修时间延迟或维修不当，由用户自行承担"
        data['external_fault_tip'] = "您的机器是否出现刮痕、裂痕？"
        data['internal_fault_tip'] = "您的机器是否曾受液体浸入？"
        damage_data = yield repair_model.damage(category_id)
        if damage_data:
            for key in damage_data:
                image = []
                if key.get('image_front'):
                    key['image_front'] = 'https://dt.readboy.com/' + key['image_front']
                    image.append(key['image_front'])
                if key.get('image_back'):
                    key['image_back'] = 'https://dt.readboy.com/' + key['image_back']
                    image.append(key['image_back'])
                del key['image_front']
                del key['image_back']
                key['image'] = image
        data['damage'] = damage_data
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))

# 代理商寄修小单缓存
class PR_SaveAgentRepair(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        content = self.get_argument('content', '')
        agent_order_sn = self.get_argument('agent_order_sn', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        if not content.get('barcode') or not content.get('serial'):
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))

        # 验证数据是否正确安全
        data = yield repair_model.ban_repair()
        if data:
            raise gen.Return(self.error(data, com.ERROR_EMPTY_DATA))

        ### 验证条码是否在黑名单中
        check_black_list = yield repair_model.check_black_list(content['barcode'])
        if check_black_list:
            raise gen.Return(self.error(u'无法寄修，有疑问请联系400客服人员', com.ERROR_PARAM))

        barcode = content['barcode']
        number = ''
        imei = ''
        # 判断是否有过缓存记录
        if agent_order_sn:
            order_cache = yield agent_model.check_order_cache(uid, barcode, agent_order_sn)
            if order_cache:
                raise gen.Return(self.error(u'此条码已有一条保存记录', com.ERROR_PARAM))
        pseudo_code = yield agent_model.check_pseudo_code(barcode, uid)
        # 判断是否是伪码
        machine = {}
        is_pseudo_code = 0
        if pseudo_code:
            is_pseudo_code = 1
            if pseudo_code['status'] == 1:
                raise gen.Return(self.error(u'此伪码已被使用', com.ERROR_PARAM))
            model_name = pseudo_code['model_name']
        else:
            machine = yield warranty_model.checkMachineByWarranty(None, barcode, None)
            if not machine:
                machine = yield warranty_model.checkMESV1(None, barcode, None)
            if not machine or not machine.get('model') or not machine.get('barcode'):
                raise gen.Return(self.error(u'数据有误, 请判断条码是否正确', com.ERROR_PARAM))
            barcode = machine['barcode']
            number = machine['number']
            imei = machine['imei1']
            model_name = machine['model']
        model_info = yield repair_model.check_model_id(model_name)
        if not model_info or not model_info.get('model_id'):
            raise gen.Return(self.error(u'寄修服务未支持该机型', com.ERROR_EMPTY_DATA))
        model_id = model_info['model_id']
        category_id = yield repair_model.check_category_id(model_id)
        ### 检查机器是否正在维修中
        check = yield repair_model.check_barcode_order(barcode)
        if check:
            raise gen.Return(self.error(u'该设备寄修中', com.ERROR_PARAM))
        color = machine.get('color')
        warranty = yield warranty_model.getByBarcode(barcode)
        in_period = repair_model.in_period(warranty)
        ### 手表服务器验证
        if not warranty and machine.get('imei1') and category_id in [3]:
            pass
            # warranty = yield warranty_model.checkMachineWear(machine['imei1'])
            # if isinstance(warranty, dict):
            #     if warranty.get('buy_date') == '0000-00-00 00:00:00':
            #         warranty['buy_date'] = None
            #     else:
            #         warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'], '%Y-%m-%d %H:%M:%S').strftime('%Y.%m.%d') if warranty.get('buy_date') else None
            #     in_period = repair_model.in_period(warranty)
            #     warranty = None
        is_special = yield repair_model.check_special(barcode)
        repeat = yield repair_model.barcode_repeat(barcode)
        # 碎屏保
        screen_insurance = yield repair_model.get_broken_screen_insurance(barcode)
        in_si_period, used_screen_insurance = yield repair_model.in_si_period(screen_insurance)
        content['in_si_period'] = in_si_period
        content['has_screen_insurance'] = 1 if screen_insurance else 0
        content['barcode'] = barcode
        content['serial'] = number
        content['imei'] = imei or u''
        content['model_name'] = model_name
        content['model_id'] = model_id
        content['color'] = color if color else ''
        content['in_period'] = 2 if is_special or not warranty else in_period
        content['reason'] = 2
        content['has_warranty'] = 1 if warranty else 0
        content['repeat_order'] = repeat
        content['is_pseudo_code'] = is_pseudo_code
        # 插入数据
        add = yield agent_model.save_repair(uid, content, agent_order_sn)
        raise gen.Return(self.success(add) if add else self.error(u'寄修错误', com.ERROR_PARAM) )


class PR_ModifyAgentRepair(ApiHandler):
    @gen.coroutine
    def post(self):

        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        content = self.get_argument('content', '')
        sn = self.get_argument('order_sn', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        order_cache = yield agent_model.check_order_cache_by_sn(uid, sn)
        if not order_cache:
            raise gen.Return(self.error(u'无此单号信息，无法修改', com.ERROR_PARAM))
        data = yield agent_model.modify_agent_repair(uid, sn, content)
        raise gen.Return(self.success(data) if data else self.error(u'修改失败', com.ERROR_PARAM))


class AddAgentOrder(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        order = yield agent_model.get_order(uid, content['agent_order_sn'])
        if not order:
            raise gen.Return(self.error(u'请确认子订单sn码是否正确', com.ERROR_PARAM))
        content['order'] = order
        data = yield agent_model.add_agent_order(content)
        raise gen.Return(self.success(data) if data else self.error(u'上传失败', com.ERROR_SYSTEM))


class AgentChangeExpress(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        sn = self.get_argument('agent_order_sn', None)
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        validate = yield agent_model.check_order(uid, sn)
        if validate == -1:
            raise gen.Return(self.error(u'有待审核订单，不允许修改寄修', com.ERROR_PARAM))
        if validate == 0:
            raise gen.Return(self.error(u'订单状态不允许修改寄修', com.ERROR_PARAM))
        # 获取子订单
        order_sn = yield agent_model.get_order_sn(uid, sn)
        if not order_sn:
            raise gen.Return(self.error(u'订单状态不允许修改寄修', com.ERROR_PARAM))
        content['uid'] = uid
        data = yield agent_model.change_order(sn, content, order_sn)
        raise gen.Return(self.success(data) if data else self.error(u'修改寄修失败', com.ERROR_PARAM))


class AgentOrderList(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 15
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        barcode = self.get_argument('barcode', '')
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        order_sn = self.get_argument('order_sn', '')
        status = self.get_argument('status', '')
        if start and not end:
            raise gen.Return(self.error(u'时间格式错误', com.ERROR_PARAM))
        order_type = 0
        if order_sn:
            order_type = yield agent_model.check_order_type(uid, order_sn)
        total_order = yield agent_model.total_order(uid, order_sn, order_type, barcode, status, start, end)
        sub_order = yield agent_model.sub_order(uid, order_sn, order_type, barcode, status, start, end)
        ret = dict()
        for order in total_order:
            order['sub_order'] = []
            for sub_order_item in sub_order:
                # pseudo_code = yield agent_model.check_pseudo_code(sub_order_item['barcode'], uid)
                # sub_order_item['is_pseudo_code'] = 1 if pseudo_code else 0
                if sub_order_item.get('status') in [410, 480, 490]:
                    sub_order_item['status'] = 400
                if order.get('agent_order_sn') == sub_order_item.get('agent_order_sn'):
                    order['sub_order'].append(sub_order_item)
        ret['total_order'] = total_order
        # ret['sub_order'] = sub_order
        raise gen.Return(self.success(ret))


class AgentOrderBill(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        if start and not end:
            raise gen.Return(self.error(u'时间格式错误', com.ERROR_PARAM))
        statistics_order = yield agent_model.statistics_order(uid, start, end)
        raise gen.Return(self.success(statistics_order)
                         if statistics_order else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class AgentOrderBillDetail(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 15
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        if (start and not end) or not start or not end:
            raise gen.Return(self.error(u'时间格式错误', com.ERROR_PARAM))
        total_order = yield agent_model.total_order_bill_detail(uid, start, end)
        sub_order = yield agent_model.sub_order_bill_detail(uid, start, end)
        ret = dict()
        for order in total_order:
            order['sub_order'] = []
            for sub_order_item in sub_order:
                if order.get('agent_order_sn') == sub_order_item.get('agent_order_sn'):
                    order['sub_order'].append(sub_order_item)
        ret['total_order'] = total_order
        # ret['sub_order'] = sub_order
        raise gen.Return(self.success(ret))


class ConfirmRepair(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        order_sn = self.get_argument('order_sn', '')
        if not order_sn:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        validate = yield repair_model.check_order(uid, order_sn)
        if validate not in [500]:
            raise gen.Return(self.error(u'订单状态不允许修改寄修', com.ERROR_PARAM))
        data = yield agent_model.confirm_repair(uid, order_sn)
        raise gen.Return(self.success(data) if data else self.error(u'确认维修失败', com.ERROR_SYSTEM))


class PR_CancelAgentRepair(ApiHandler):
    @gen.coroutine
    def post(self):

        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        # content = self.get_argument('content', '')
        sn = self.get_argument('order_sn', '')
        if not sn:
            raise gen.Return(self.error(u'缺少参数', com.ERROR_PARAM))
        order_cache = yield agent_model.check_order_cache_by_sn(uid, sn)
        if not order_cache:
            raise gen.Return(self.error(u'无此单号信息，无法取消', com.ERROR_PARAM))
        data = yield agent_model.cancel_agent_repair(uid, sn)
        raise gen.Return(self.success(data) if data else self.error(u'删除失败', com.ERROR_PARAM))


class AgentOrderCacheList(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 14133
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        data = yield agent_model.order_cache(uid)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class AgentOrderCacheDetail(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 14133
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        order_sn = self.get_argument('order_sn', '')
        if not order_sn:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        data = yield agent_model.order_cache_detail(uid, order_sn)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class AgentOrderCacheDelete(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 14133
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        order_sn = self.get_argument('order_sn', '')
        if not order_sn:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        data = yield agent_model.order_cache_delete(uid, order_sn)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))
