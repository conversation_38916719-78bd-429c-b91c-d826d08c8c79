# encoding=utf-8
# __author__ = 'lch'

import tornado
from tornado import gen
from tornado.log import app_log
import re
import time
import datetime
# from dateutil.parser import parse
import arrow
import traceback

import util.com as com
from handler import ApiHandler, tokenauth
import store.warranty_model as warranty_model
import store.endpoint_model as endpoint_model
import cache.warranty_cache as warranty_cache

TWO_YEAR_AGO = 2*365*24*3600


def _isBarcode(barcode):
    return re.match(r'[A-Za-z0-9]{6,14}', barcode) is not None


def _isImei(imei):
    return re.match(r'\d{15}', imei) is not None


def _isSerialNumber(number):
    return re.match(r'[A-Za-z0-9]{8,64}', number) is not None


def _isPhone(phone):
    return re.match(r'^1[0-9]{10}$', phone) is not None


def _absolute_datetime(now, delta_v, delta_u, updown='floor'):
    if delta_u == 'h':
        ret = now.shift(hours=delta_v)
        ret = ret.floor('hour') if updown == 'floor' else ret.ceil('hour')
    elif delta_u == 'd':
        ret = now.shift(days=delta_v)
        ret = ret.floor('day') if updown == 'floor' else ret.ceil('day')
    elif delta_u == 'w':
        ret = now.shift(weeks=delta_v)
        ret = ret.floor('week') if updown=='floor' else ret.ceil('week')
    elif delta_u == 'm':
        ret = now.shift(months=delta_v)
        ret = ret.floor('month') if updown == 'floor' else ret.ceil('month')
    elif delta_u == 'y':
        ret = now.shift(years=delta_v)
        ret = ret.floor('year') if updown == 'floor' else ret.ceil('year')
    else:
        ret = now
    return ret.datetime


class Warranty(ApiHandler):
    def data_received(self, chunk):
        pass

    @gen.coroutine
    def get(self):
        phone = self.get_argument("phone", "")
        if not phone:
            raise gen.Return(self.error(u"电话不能为空", com.ERROR_PARAM))
        if not _isPhone(phone):
            raise gen.Return(self.error(u'电话格式错误', com.ERROR_PARAM))
        data = yield warranty_model.getByPhone(phone)
        ret = dict()
        ret['list'] = data
        raise gen.Return(self.success(ret, "success"))