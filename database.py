#coding=utf-8
#encoding=utf-8
#__author__="QZL"

import pymysql
from conf.config import MYSQL_DB
import json


conn = pymysql.connect(host=MYSQL_DB['host'],
                       port=MYSQL_DB['port'],
                       user=MYSQL_DB['user'],
                       password=MYSQL_DB['passwd'],
                       database=MYSQL_DB['db'],
                       charset='utf8mb4',
                       cursorclass=pymysql.cursors.DictCursor,
                       autocommit=True)


def dir_key():
    with conn.cursor() as cursor:
        sql = 'update sound set dir=concat_ws("/", layer1, layer2, layer3)'
        cursor.execute(sql)

def layer_sound():
    with conn.cursor() as cursor:
        sql = 'replace into layer_sound select layer1, layer2, layer3 from sound group by layer1, layer2, layer3'
        cursor.execute(sql)

def tree():
    with conn.cursor() as cursor:
        # sql = 'select layer1, layer2, layer3, dir from sound group by layer1, layer2, layer3'
        # cursor.execute(sql)
        # data = cursor.fetchall()

        dir_list = list()

        sql = 'select distinct layer1 as name from sound'
        cursor.execute(sql)
        layer1 = cursor.fetchall()
        for i in layer1:
            sql = 'select distinct layer2 as name from sound where layer1=%s'
            cursor.execute(sql, i.get('name'))
            children = cursor.fetchall()
            i['level'] = 1
            if children:
                i['children'] = list()
                for j in children:
                    if j.get('name'):
                        i['children'].append(j.get('name'))
            if not i['children']:
                i['children'] = None
            dir_list.append(i)


        sql = 'select distinct layer2 as name from sound'
        cursor.execute(sql)
        layer2 = cursor.fetchall()
        # print prettyJson(layer2)
        for i in layer2:
            sql = 'select distinct layer3 as name from sound where layer2=%s'
            cursor.execute(sql, i.get('name'))
            children = cursor.fetchall()
            # print prettyJson(children)
            i['level'] = 2
            if children:
                i['children'] = list()
                for j in children:
                    if j.get('name'):
                        i['children'].append(j.get('name'))
            if not i['children']:
                i['children'] = None
            dir_list.append(i)
            # print prettyJson(i)


        sql = 'select distinct layer3 as name from sound'
        cursor.execute(sql)
        layer3 = cursor.fetchall()
        for i in layer3:
            i['level'] = 3
            i['children'] = None
            if i.get('name'):
                dir_list.append(i)
        dir_dict = {i.get('name'):i for i in dir_list}
        for j in [3,2,1]:
            for i in dir_dict:
                if dir_dict[i].get('level') == j:
                    if dir_dict[i].get('children'):
                        new_child = list()
                        for k in dir_dict[i]['children']:
                            new_child.append(dir_dict[k])
                        dir_dict[i]['children'] = new_child
                    else:
                        del dir_dict[i]['children']
        ret = [dir_dict[i] for i in dir_dict if dir_dict[i].get('level') == 1]
        print prettyJson(ret)

def prettyJson(s):
    return json.dumps(s, indent=2, ensure_ascii=False)

def main():
    ### 音频需要更新则执行以下两个函数
    # dir_key()
    # layer_sound()
    tree()

if __name__ == '__main__':
    main()