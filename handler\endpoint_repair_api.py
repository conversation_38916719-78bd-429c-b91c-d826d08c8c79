# encoding=utf-8
# __author__ = 'qry'
import datetime
import json

from handler import ApiHandler
from tornado import gen
from store import endpoint_repair_model, optional_accessory_model, repair_model, warranty_model, agent_model
from util import com

# 终端代寄 小单缓存
class PR_SaveEndpointRepair(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        content = self.get_argument('content', '')
        endpoint_id = com.safeInt(self.get_argument("endpoint_id", ""), 0)
        agent_order_sn = self.get_argument('agent_order_sn', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        if not content.get('barcode') or not content.get('serial'):
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        if not content.get('phone'):
            raise gen.Return(self.error(u'手机号不能为空', com.ERROR_PARAM))
        if not com.is_hone(content['phone']):
            raise gen.Return(self.error(u'手机号格式出错', com.ERROR_PARAM))
        if content.get('is_user_address') == 1:
            for i in ['province', 'city', 'district', 'address']:
                if not content.get(i):
                    raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        # 验证数据是否正确安全
        data = yield repair_model.ban_repair()
        if data:
            raise gen.Return(self.error(data, com.ERROR_EMPTY_DATA))

        ### 验证手机是否在黑名单中
        check_black_phone = yield repair_model.check_black_phone(content['phone'])
        if check_black_phone:
            raise gen.Return(self.error(u'无法寄修，有疑问请联系400客服人员', com.ERROR_PARAM))

        ### 验证条码是否在黑名单中
        check_black_list = yield repair_model.check_black_list(content['barcode'])
        if check_black_list:
            raise gen.Return(self.error(u'无法寄修，有疑问请联系400客服人员', com.ERROR_PARAM))

        barcode = content['barcode']
        # 判断是否有过缓存记录
        if agent_order_sn:
            order_cache = yield endpoint_repair_model.check_order_cache(uid, barcode, agent_order_sn, endpoint_id)
            if order_cache:
                raise gen.Return(self.error(u'此条码已有一条保存记录', com.ERROR_PARAM))
        number = ''
        imei = ''
        pseudo_code = yield agent_model.check_model_id_pseudo_code(barcode)
        # 判断是否是伪码
        machine = {}
        if pseudo_code:
            if pseudo_code['status'] == 1:
                raise gen.Return(self.error(u'此伪码已被使用', com.ERROR_PARAM))
            model_name = pseudo_code['model_name']
            model_id = pseudo_code['model_id']
        else:
            machine = yield warranty_model.checkMachineByWarranty(None, barcode, None)
            if not machine:
                machine = yield warranty_model.checkMESV1(None, barcode, None)
            number = machine['number']
            imei = machine['imei1']
            if not machine or not machine.get('model') or not machine.get('barcode'):
                raise gen.Return(self.error(u'数据有误, 请判断条码是否正确', com.ERROR_PARAM))
            barcode = machine['barcode']
            model_name = machine['model']
            model_id = machine['model_id']
        model_info = yield repair_model.check_model(model_id)
        if not model_info or not model_info.get('model_id'):
            raise gen.Return(self.error(u'寄修服务未支持该机型', com.ERROR_BAD_REQUEST))
        model_id = model_info['model_id']
        category_id = yield repair_model.check_category_id(model_id)
        # 检查机器是否正在维修中
        check = yield repair_model.check_barcode_order(barcode)
        if check:
            raise gen.Return(self.error(u'该设备寄修中', com.ERROR_PARAM))
        color = machine.get('color')
        warranty = yield warranty_model.getByBarcode(barcode)
        in_period = repair_model.in_period(warranty)
        ### 手表服务器验证
        if not warranty and machine.get('imei1') and category_id in [3]:
            pass
            # warranty = yield warranty_model.checkMachineWear(machine['imei1'])
            # if isinstance(warranty, dict):
            #     if warranty.get('buy_date') == '0000-00-00 00:00:00':
            #         warranty['buy_date'] = None
            #     else:
            #         warranty['buy_date'] = datetime.datetime.strptime(warranty['buy_date'], '%Y-%m-%d %H:%M:%S').strftime('%Y.%m.%d') if warranty.get('buy_date') else None
            #     in_period = repair_model.in_period(warranty)
            #     warranty = None
        # 判断是否是特批，特批默认保外，没有保卡也默认保外
        is_special = yield repair_model.check_special(barcode)
        repeat = yield repair_model.barcode_repeat(barcode)
        ar_repeat = yield repair_model.ar_repeat(barcode)
        screen_insurance = yield repair_model.get_broken_screen_insurance(barcode)
        in_si_period, used_screen_insurance = yield repair_model.in_si_period(screen_insurance)
        content['in_si_period'] = in_si_period
        content['has_screen_insurance'] = 1 if screen_insurance else 0
        content['barcode'] = barcode
        content['serial'] = number
        content['imei'] = imei or u''
        content['model_name'] = model_name
        content['model_id'] = model_id
        content['color'] = color if color else ''
        content['in_period'] = 2 if is_special or not warranty else in_period
        content['reason'] = 2
        content['has_warranty'] = 1 if warranty else 0
        content['repeat_order'] = repeat
        content['ar_repeat'] = ar_repeat
        # 插入数据
        add = yield endpoint_repair_model.save_repair(uid, content, agent_order_sn, endpoint_id)
        raise gen.Return(self.success(add) if add else self.error(u'寄修错误', com.ERROR_PARAM))


class PR_ModifyEndpointRepair(ApiHandler):
    @gen.coroutine
    def post(self):

        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint_id = com.safeInt(self.get_argument("endpoint_id", ""), 0)
        content = self.get_argument('content', '')
        sn = self.get_argument('order_sn', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        if content.get('is_user_address') == 1:
            for i in ['province', 'city', 'district', 'address']:
                if not content.get(i):
                    raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        if not content.get('phone'):
            raise gen.Return(self.error(u'手机号不能为空', com.ERROR_PARAM))
        if not com.is_hone(content['phone']):
            raise gen.Return(self.error(u'手机号格式出错', com.ERROR_PARAM))
        order_cache = yield endpoint_repair_model.check_order_cache_by_sn(uid, sn, endpoint_id)
        if not order_cache:
            raise gen.Return(self.error(u'无此单号信息，无法修改', com.ERROR_PARAM))
        data = yield endpoint_repair_model.modify_agent_repair(uid, sn, content, endpoint_id)
        raise gen.Return(self.success(data) if data else self.error(u'修改失败', com.ERROR_PARAM))


class AddEndpointOrder(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint_id = com.safeInt(self.get_argument("endpoint_id", ""), 0)
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        order = yield endpoint_repair_model.get_order(uid, content['agent_order_sn'], endpoint_id)
        # print order
        if not order:
            raise gen.Return(self.error(u'请确认子订单sn码是否正确', com.ERROR_PARAM))
        content['order'] = order
        data = yield endpoint_repair_model.add_agent_order(content)
        raise gen.Return(self.success(data) if data else self.error(u'上传失败', com.ERROR_SYSTEM))


class EndpointChangeExpress(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint_id = com.safeInt(self.get_argument("endpoint_id", ""), 0)
        sn = self.get_argument('agent_order_sn', None)
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        validate = yield endpoint_repair_model.change_express_check_order(uid, sn, endpoint_id)
        if validate == -1:
            raise gen.Return(self.error(u'有待审核订单，不允许修改寄修', com.ERROR_PARAM))
        if validate == 0:
            raise gen.Return(self.error(u'订单状态不允许修改寄修', com.ERROR_PARAM))
        # 获取子订单
        order_sn = yield endpoint_repair_model.get_order_sn(uid, sn, endpoint_id)
        if not order_sn:
            raise gen.Return(self.error(u'订单状态不允许修改寄修', com.ERROR_PARAM))
        content['uid'] = uid
        data = yield endpoint_repair_model.change_order(sn, content, order_sn)
        raise gen.Return(self.success(data) if data else self.error(u'修改寄修失败', com.ERROR_PARAM))


class EndpointOrderList(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint_id = com.safeInt(self.get_argument("endpoint_id", ""), 0)
        barcode = self.get_argument('barcode', '')
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        phone = self.get_argument('phone', '')
        order_sn = self.get_argument('order_sn', '')
        status = self.get_argument('status', '')
        if start and not end:
            raise gen.Return(self.error(u'时间格式错误', com.ERROR_PARAM))
        order_type = 0
        if order_sn:
            order_type = yield endpoint_repair_model.check_order_type(uid, order_sn, endpoint_id)
        total_order = yield endpoint_repair_model.total_order(uid, order_sn, order_type, barcode, status,
                                                              start, end, phone, endpoint_id)
        # sub_order = yield endpoint_repair_model.sub_order(uid, order_sn, order_type, barcode, status, start, end, phone)
        ret = dict()
        # for order in total_order:
        #     order['sub_order'] = []
        #     for sub_order_item in sub_order:
        #         # print sub_order_item
        #         agent_order_sn = sub_order_item.get('agent_order_sn')
        #         # del sub_order_item['agent_order_sn']
        #         if order.get('agent_order_sn') == agent_order_sn:
        #             sub_order_cache = sub_order_item
        #             sub_order_cache.pop('agent_order_sn')
        #             order['sub_order'].append(sub_order_cache)
        ret['total_order'] = total_order
        raise gen.Return(self.success(ret))


class EndpointMyHistory(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        date = self.get_argument('date', None)
        phone = self.get_argument('phone', '')
        number = com.safeInt(self.get_argument('number', 10))
        if not phone:
            raise gen.Return(self.error(u'参数phone不能为空', com.ERROR_PARAM))
        data = yield endpoint_repair_model.repair_history(phone, date, number)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class EndpointRepairDetail(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        # endpoint_id = com.safeInt(self.get_argument("endpoint_id", ""), 0)
        sn = self.get_argument('order_sn', None)
        validate = yield endpoint_repair_model.check_order(sn)
        if not validate:
            raise gen.Return(self.error(u'无权操作订单', com.ERROR_PARAM))
        # 待支付状态下有支付单的需要查询支付状态
        yield repair_model.order_pay_check(sn)

        ret = dict()
        ret['info'] = yield repair_model.order_info(sn)
        ret['log'] = yield repair_model.order_log(sn)
        ret['malfunction'] = yield repair_model.malfunction(sn)
        ret['accessory'] = yield repair_model.accessory(sn)
        ret['optional_accessory'] = yield optional_accessory_model.pr_oa_list(sn)
        ret['endpoint'] = yield repair_model.endpoint(ret['info'].get('repair_endpoint'))
        ret['is_pseudo_code'] = 0
        raise gen.Return(self.success(ret) if ret else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class PR_CancelEndpointRepair(ApiHandler):
    @gen.coroutine
    def post(self):

        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        # content = self.get_argument('content', '')
        endpoint_id = com.safeInt(self.get_argument("endpoint_id", ""), 0)
        sn = self.get_argument('order_sn', '')
        if not sn:
            raise gen.Return(self.error(u'缺少参数', com.ERROR_PARAM))
        order_cache = yield endpoint_repair_model.check_order_cache_by_sn(uid, sn, endpoint_id)
        if not order_cache:
            raise gen.Return(self.error(u'无此单号信息，无法取消', com.ERROR_PARAM))
        data = yield endpoint_repair_model.cancel_agent_repair(uid, sn, endpoint_id)
        raise gen.Return(self.success(data) if data else self.error(u'删除失败', com.ERROR_PARAM))