# coding=utf-8
# encoding=utf-8
#__author__="sgh"
# 云客服通话记录录音拉取脚本

import sys
import hashlib
import time
import random
import json
import urllib, urllib2
from jindie import URL
import pymysql
import requests
import datetime

import oss2
import conf.config as config

oss_auth = oss2.Auth(config.OSS_ACCESS_ID, config.OSS_ACCESS_KEY)
bucket = oss2.Bucket(oss_auth, config.OSS_ENDPOINT, config.OSS_BUCKET)

reload(sys)
sys.setdefaultencoding('utf-8')

DEBUG = False

connect = pymysql.connect(
    host='**************',
    user='root',
    passwd='root',
    # db='rbcare',
    db='post_repair',
    charset='utf8',
    # host='localhost',
    # user='root',
    # passwd='q',
    # db='rbcare',
    # # db='readboydata',
    # charset='utf8',
) if DEBUG else pymysql.connect(
    # host='rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com',
    host='rm-wz9049tx2592u4e6p.mysql.rds.aliyuncs.com',
    user='yxrepair',
    passwd='clC30J3DQLN3w7ma',
    db='post_repair',
    # db='readboydata',
    charset='utf8',
)
cursor = connect.cursor(cursor=pymysql.cursors.DictCursor)

def sync_sound_to_oss():

    sql = 'select * from `call_log` where download_sound_status = 0 and bill_id != "" and record_file != ""'
    cursor.execute(sql)
    result = cursor.fetchone()
    if result:

        # 获取录音内容
        url = result.get('file_server') + '/' + result.get('record_file')
        res = requests.get(url)
        music = res.content

        # 生成新的URL
        oss_filename = 'rbcare/repair/upload/ykf_sound/' + result.get('record_file')
        final_filename = config.OSS_CNAME + oss_filename

        ret = bucket.put_object(oss_filename, music)

        if ret.status == 200:

            # 存入新地址,改变状态
            sql = 'update call_log set download_sound_status = 1 , sound_url = (%s) where call_sheet_id = (%s) '
            cursor.execute(sql , ( final_filename , result.get('call_sheet_id')))
            connect.commit()

            print('upload success --- ' + final_filename)
        else:
            print('upload fail --- ' + final_filename)
    else:
        print('no one need to upload')

def main():
    sync_sound_to_oss()

if __name__ == '__main__':
    main()
