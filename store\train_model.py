# encoding=utf-8
# __author__ = 'lch'

import time
import datetime
from tornado import gen
import traceback
import json

from store import DB
# import util.com as com
import conf.config as config

@gen.coroutine
def getVideos(ts=0, count=10):
    ts = time.time() if ts <= 0 else ts
    start = datetime.datetime.fromtimestamp(ts).strftime('%Y-%m-%d %H:%M:%S')
    cur = yield DB.execute('Select id, title, preview, path, description, created_at, updated_at '+
                           'from train_videos where created_at<%s and status=1 order by created_at desc limit %s', (start, count+1))
    vs = cur.fetchall()
    if not vs:
        raise gen.Return(None)
    l = len(vs)
    isEnd = l < count+1
    size = l if isEnd else count
    data = vs[0:size]
    for card in data:
        preview = card['preview']
        previews = None
        if preview:
            pres = json.loads(preview)
            if pres and isinstance(pres, list):
                previews = [config.OSS_CNAME+pre for pre in pres]
        card['preview'] = previews
        card['path'] = config.OSS_CNAME + card['path']
        card['created_at'] = card['created_at'].strftime('%Y.%m.%d %H:%M:%S')
        card['updated_at'] = card['updated_at'].strftime('%Y.%m.%d %H:%M:%S')
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)


@gen.coroutine
def getTrain(page=1, count=10, type=-1, category=-1):
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    if category > 0 and type > 0:
        cur = yield DB.execute('Select id, name, preview, path, description, created_at, updated_at, star, download_count '+
                           'from train where type=%s and category=%s and status=1 order by top desc, id desc limit %s, %s',
                               (type, category, offset, count))
    elif type > 0:
        cur = yield DB.execute('Select id, name, preview, path, description, created_at, updated_at, star, download_count ' +
                               'from train where type=%s and status=1 order by top desc, id desc limit %s, %s',
                               (type, offset, count))
    else:
        cur = yield DB.execute('Select id, name, preview, path, description, created_at, updated_at, star, download_count ' +
                               'from train where status=1 order by top desc, id desc limit %s, %s',
                               (offset, count))
    vs = cur.fetchall()
    if not vs:
        raise gen.Return(None)
    l = len(vs)
    isEnd = l < count
    size = l if isEnd else count
    data = vs[0:size]
    for card in data:
        preview = card['preview']
        previews = None
        if preview:
            pres = json.loads(preview)
            if pres and isinstance(pres, list):
                previews = [config.OSS_CNAME+pre for pre in pres]
        card['preview'] = previews
        card['path'] = config.OSS_CNAME + card['path']
        card['created_at'] = card['created_at'].strftime('%Y/%m/%d %H:%M:%S')
        card['updated_at'] = card['updated_at'].strftime('%Y/%m/%d %H:%M:%S')
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)


@gen.coroutine
def getTrainType():
    cur = yield DB.execute('Select id, title from train_type order by `order` ASC')
    ret = cur.fetchall()
    raise gen.Return(ret)


@gen.coroutine
def getTrainCategory(type = -1):
    if type > 0:
        cur = yield DB.execute('Select id, title from train_category where id in (select category from train where type = %s group by category) order by `order` ASC', (type))
    else:
        cur = yield DB.execute('Select id, title from train_category order by `order` ASC')
    ret = cur.fetchall()
    raise gen.Return(ret)


@gen.coroutine
def getTrainTypeCategory():
    cur = yield DB.execute('select type , category, c.title from (select type , category from train where category > 0 group by type, category) as t left join train_category c on t.category=c.id;')
    ret = cur.fetchall()
    raise gen.Return(ret)


@gen.coroutine
def setTrainDownload(i=0):
    b = False
    if i < 0:
        raise gen.Return(b)
    cur = yield DB.execute('update train set increment=increment+1 where id=%s', (i))
    ret = cur.rowcount
    if ret > 0:
        b = True
    raise gen.Return(b)


@gen.coroutine
def getTrainHot(page, count):
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    cur = yield DB.execute('Select id, name, preview, path, description, created_at, updated_at, star, download_count ' +
                               'from train where status=1 order by download_count desc limit %s, %s',
                               (offset, count+1))
    vs = cur.fetchall()
    if not vs:
        raise gen.Return(None)
    l = len(vs)
    isEnd = l < count+1
    size = l if isEnd else count
    data = vs[0:size]
    for card in data:
        preview = card['preview']
        previews = None
        if preview:
            pres = json.loads(preview)
            if pres and isinstance(pres, list):
                previews = [config.OSS_CNAME+pre for pre in pres]
        card['preview'] = previews
        card['path'] = config.OSS_CNAME + card['path']
        card['created_at'] = card['created_at'].strftime('%Y/%m/%d %H:%M:%S')
        card['updated_at'] = card['updated_at'].strftime('%Y/%m/%d %H:%M:%S')
    ret = {
        'data': data,
        'size': size,
        'isEnd': isEnd
    }
    raise gen.Return(ret)