# encoding=utf-8
# __author__ = 'lch'

import md5
import re
import time
import math
try:
    import simplejson as json
except:
    import json


# errno
FETAL_NONE = 6000  # 无条件禁止
FETAL_SERVICE_OFF = 6001  # 服务器关闭
FETAL_CLIENT_NOT_SUPPORT = 6002  # 非法或不支持的客户端
FETAL_UNAUTH = 6003  # 未授权的机型
FETAL_BLACKLIST = 6004  # 已被列入黑名单

ERROR_UNKNOWN = 7000  # 未知错误
ERROR_SYSTEM = 7001  # 系统错误
ERROR_DATABASE = 7002  # 数据库错误
ERROR_BUSY = 7003  # 系统繁忙
ERROR_PARAM = 7004  # 参数错误
ERROR_TOOFAST = 7005  # 频繁调用
ERROR_ALREADY_EXISTS = 7006  # 已经存在
ERROR_NOT_FOUND = 7007  # 未找到
ERROR_BAD_REQUEST = 7008  # 错误请求
ERROR_ACCESS = 7009  # 未登录
ERROR_NOT_PERMIT = 7010  # 权限不足
ERROR_IO_ERROR = 7011  # IO错误
ERROR_LIMIT = 7012  # 超出限额
ERROR_EMPTY_DATA = 7013  # 空数据
ERROR_NO_MONEY = 7014  # 资金不足
ERROR_BAD_STATE = 7015  # 状态错误
ERROR_BAD_CONTENT = 7016  # 错误数据
ERROR_NOT_Direct_E = 7017  # 非直营电商
ERROR_BAD_TOKEN = 7200  # token无效
SERVICE_SLUG = ['pr_service']

TYPE_BOOK = 1
TYPE_TUTOR = 2
TYPE_MXMJ = 3
TYPE_HGTX = 4
TYPE_EXEX = 5
TYPE_QST = 6
TYPE_PAGE = 10000
TYPE_WORD = 10001
TYPE_HANZI = 10002
TYPE_MSSP = 10003
TYPE_WSP = 10004

STAGE_ACTION = {1: 'xiaoxue', 2: 'chuzhong', 3: 'gaozhong'}
STAT_TYPE_NAME = {
    1: 'book', 2: 'tutor', 3: 'mxmj', 4: 'hgtx', 5: 'exex', 6: 'qst',
    10000: 'page', 10001: 'word', 10002: 'hanzi', 10003: 'msvideo', 10004: 'microvideo'
}
STAGE_SCHOOLTYPE = {
    1: [1, 3, 5, 7],
    2: [2, 3, 6, 7],
    3: [4, 5, 6, 7]
}


# 显示中文
def toJson(obj):
    return json.dumps(obj, ensure_ascii=False)


# 美化，带换行
def prettyJson(obj, indent=2):
    return json.dumps(obj, ensure_ascii=False, indent=indent)


# 安全的取整
def safeInt(s, default=0):
    try:
        return int(s)
    except Exception:
        return default


# 安全的取浮点数
def safeFloat(s, default=0):
    try:
        return float(s)
    except Exception:
        return default


# 获取两组坐标之间的距离（米）
def getDistance(lng1, lat1, lng2, lat2):
    EARTH_RADIUS = 6378.137  # 地球半径
    radLat1 = lat1 * math.pi / 180.0;
    radLat2 = lat2 * math.pi / 180.0;
    a = radLat1 - radLat2;
    b = (lng1 * math.pi / 180.0) - (lng2 * math.pi / 180.0);
    s = 2 * math.asin(math.sqrt(math.pow(math.sin(a/2),2) + math.cos(radLat1) * math.cos(radLat2) * math.pow(math.sin(b/2),2)));
    s = s * EARTH_RADIUS;
    s = round(s * 1000);

    return s;


# 叶子节点
def leafChapters(tree, secId):
    leafIds = []
    targetDepth = [65535]

    def traverseChaps(chap, chapId, depth):
        found = False
        id = chap['id']
        if id == chapId:
            targetDepth[0] = depth
            found = True

        children = chap.get('children')
        if children is None:
            if depth > targetDepth[0] or id == chapId:
                leafIds.append(id)
        else:
            for child in children:
                ret = traverseChaps(child, chapId, depth+1)
                if not found and ret:
                    found = ret
                    break

        return found

    traverseChaps(tree, secId, 0)
    return leafIds


# return: stage, subject
def splitCourse(csId):
    csIds = [
        [65, 66, 67],
        [53, 54, 55, 56, 57, 58, 59, 60, 61],
        [42, 43, 44, 45, 46, 47, 48, 49, 50],
    ]

    for i, group in enumerate(csIds):
        for j, id in enumerate(group):
            if id == csId:
                return i+1, j+1
    return 0, 0


def gradeToStage(grade):
    if grade == 0:
        return 0
    elif grade <= 6:
        return 1
    elif grade <= 9:
        return 2
    elif grade <= 12:
        return 3
    else:
        return 0


def parseStage(stage):
    stages = {'xiaoxue': 1, 'chuzhong': 2, 'gaozhong': 3}
    stageId = stages.get(stage, 0)
    return stageId


def parseSubject(subject):
    subjects = {'yw': 1, 'sx': 2, 'yy': 3, 'wl': 4, 'hx': 5,
                'sw': 6, 'zz': 7, 'ls': 8, 'dl': 9, 'kx': 10}
    subjectId = subjects.get(subject, 0)
    return subjectId


def subjectIdToName(subid):
    SUBJECT_IDS = {
        1: 'yw', 2: 'sx', 3: 'yy', 4: 'wl', 5: 'hx', 6: 'sw',
        7: 'zz', 8: 'ls', 9: 'dl', 10: 'kx'
    }
    return SUBJECT_IDS.get(subid)


def checkGrade(stage, grade):
    STAGE_GRADES = {1: [1,2,3,4,5,6], 2: [6,7,8,9], 3: [10,11,12]}
    grades = STAGE_GRADES[stage]
    return grade in grades


def getCoursesByStage(stage):
    STAGE_COURSES = {
        1: [65, 66, 67],
        2: [53, 54, 55, 56, 57, 58, 59, 60, 61],
        3: [42, 43, 44, 45, 46, 47, 48, 49, 50],
    }
    return STAGE_COURSES.get(stage)


def getGradesByStage(stage):
    STAGE_GRADES = {1: [1,2,3,4,5,6], 2: [6,7,8,9], 3: [10,11,12]}
    return STAGE_GRADES.get(stage)


def getSubjectsByStage(stage):
    subjects = ['yw', 'sx', 'yy', 'wl', 'hx', 'sw', 'zz', 'ls', 'dl']
    return subjects[0:3] if stage == 1 else subjects


def courseIdToSubjectName(csId):
    stage, subject = splitCourse(csId)
    return subjectIdToName(subject)


def getSubjectIdsByStage(stage):
    subIds = [1, 2, 3, 4, 5, 6, 7, 8, 9]
    return subIds[0:3] if stage == 1 else subIds


# 唯一数据id
def _guid(id, type):
    return str(id)+'|'+str(type)

# 根据数据资源类型定义返回正确格式的id值
def _id(id, type):
    return safeInt(id, id) if type<10000 else id

# 根据一组节点拼成树结构，节点必须包含id、children、level字段
def makeTree(secs):
    sec_map = {}
    root = None
    for sec in secs:
        sec_map[sec['id']] = sec
        if root is None and sec['level'] == 0:
            root = sec

    def linkChildren(node):
        ids = node.get('children')
        if ids and len(ids) > 0:
            children = []
            for id in ids:
                sec = sec_map[id]
                linkChildren(sec)
                children.append(sec)
            node['children'] = children

    linkChildren(root)
    return root


# 遍历树形结构
def iterTree(ch, parent=None, dump=False, depth=-1):
    if dump:
        print ch['id'], ch['name']
    if depth == -1:
        yield parent, ch
    else:
        yield parent, ch, depth

    children = ch.get('children')
    if children:
        for child in children:
            if depth == -1:
                for p, c in iterTree(child, ch, dump, depth):
                    yield p, c
            else:
                for p, c, d in iterTree(child, ch, dump, depth+1):
                    yield p, c, d


def is_hone(phone):
    return re.match(r'^1[0-9]{10}$', phone) is not None