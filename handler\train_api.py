# encoding=utf-8
# __author__ = 'lch'

from tornado import gen
from tornado.web import Request<PERSON><PERSON><PERSON>
from tornado.log import app_log
import traceback
import json

import util.com as com
from handler import ApiHandler, tokenauth
import store.train_model as train_model


class TrainVideos(RequestHandler):
    @gen.coroutine
    def get(self):
        ts = com.safeInt(self.get_argument('ts', 0), 0)
        count = com.safeInt(self.get_argument('count', 10), 10)

        ret = yield train_model.getVideos(ts, count)
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET')
        self.success(ret) if ret else self.error(u'没有发布任何视频', com.ERROR_EMPTY_DATA)

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.dumpJson(ret)

    def dumpJson(self, ret):
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            self.write(json.dumps(ret, ensure_ascii=False, indent=4))
        else:
            self.write(json.dumps(ret, ensure_ascii=False))


class TrainList(RequestHandler):
    @gen.coroutine
    def get(self):
        page = com.safeInt(self.get_argument('page', 1), 1)
        count = com.safeInt(self.get_argument('count', 10), 10)
        type = com.safeInt(self.get_argument('type', -1), -1)
        category = com.safeInt(self.get_argument('category', -1), -1)

        ret = yield train_model.getTrain(page, count, type, category)
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET')
        self.success(ret) if ret else self.error(u'未找到数据', com.ERROR_EMPTY_DATA)

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.dumpJson(ret)

    def dumpJson(self, ret):
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            self.write(json.dumps(ret, ensure_ascii=False, indent=4))
        else:
            self.write(json.dumps(ret, ensure_ascii=False))


class TrainType(RequestHandler):
    @gen.coroutine
    def get(self):
        ret = yield train_model.getTrainType()
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET')
        self.success(ret) if ret else self.error(u'未找到数据', com.ERROR_EMPTY_DATA)

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.dumpJson(ret)

    def dumpJson(self, ret):
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            self.write(json.dumps(ret, ensure_ascii=False, indent=4))
        else:
            self.write(json.dumps(ret, ensure_ascii=False))


class TrainCategory(RequestHandler):
    @gen.coroutine
    def get(self):
        t = com.safeInt(self.get_argument('type', -1))
        ret = yield train_model.getTrainCategory(t)
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET')
        self.success(ret) if ret else self.error(u'未找到数据', com.ERROR_EMPTY_DATA)

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.dumpJson(ret)

    def dumpJson(self, ret):
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            self.write(json.dumps(ret, ensure_ascii=False, indent=4))
        else:
            self.write(json.dumps(ret, ensure_ascii=False))


class TrainTypeCategory(RequestHandler):
    @gen.coroutine
    def get(self):
        ret = yield train_model.getTrainTypeCategory()
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET')
        self.success(ret) if ret else self.error(u'未找到数据', com.ERROR_EMPTY_DATA)

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.dumpJson(ret)

    def dumpJson(self, ret):
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            self.write(json.dumps(ret, ensure_ascii=False, indent=4))
        else:
            self.write(json.dumps(ret, ensure_ascii=False))


class TrainDownload(RequestHandler):
    @gen.coroutine
    def get(self):
        i = com.safeInt(self.get_argument('id', 0))
        ret = yield train_model.setTrainDownload(i)
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET')
        self.success(ret)

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.dumpJson(ret)

    def dumpJson(self, ret):
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            self.write(json.dumps(ret, ensure_ascii=False, indent=4))
        else:
            self.write(json.dumps(ret, ensure_ascii=False))


class TrainHot(ApiHandler):
    @gen.coroutine
    @tokenauth
    def get(self):
        page = com.safeInt(self.get_argument('page', 1))
        count = com.safeInt(self.get_argument('count', 10))
        data = yield train_model.getTrainHot(page, count)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))