# encoding=utf-8
# __author__ = 'sgh'
# __date__ = '2021-10-27'

from store import DB_PR
# import util.com as com
from tornado import gen
from log import db_log

@gen.coroutine
def add_call_log(data):
    """
    记录云客服通话记录
    :param data: 通话记录
    :return:
    """
    key = ['call_no', 'called_no', 'call_sheet_id', 'call_type', 'begin', 'end', 'agent', 'state', 'call_state', 'record_file', 'file_server' , 'province' , 'district' , 'bill_id']
    fields = ','.join(key)
    values = ','.join(['%s'] * len(key))
    ### 处理输入数据
    param = tuple([data.get(i) for i in key])
    sql = 'insert into call_log (' + fields + ') values ('+ values +')'
    try:
        cur = yield DB_PR.execute(sql, param)
    except:
        db_log.error('store call log error:', exc_info=True)
        raise gen.Return(False)
    raise gen.Return(True)
