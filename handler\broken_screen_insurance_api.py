# encoding=utf-8
# __author__ = 'qry'
import json
import random
from collections import OrderedDict

from tornado import gen
from tornado.log import app_log

from handler.warranty_api import _isBarcode, _isPhone
from log import db_log
from store import broken_screen_insurance_model, warranty_model, endpoint_model, alipay_model, sms_model
from handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from store.broken_screen_insurance_model import broken_screen_insurance_standard, save_broken_screen_insurance
from store.warranty_model import check_endpoint
from util import com
import datetime
from tornado.web import RequestHandler


class BrokenScreenInsurance(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        barcode = self.get_argument('barcode', '')
        if not barcode:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))

        is_exist = yield broken_screen_insurance_model.check_is_exist(barcode)
        if is_exist in [100, 200, 300, 400, 500]:
            raise gen.Return(self.error(u'此条码已申请过碎屏保，无法再次申请', com.ERROR_ALREADY_EXISTS))
        warranty = yield warranty_model.get_warranty_info(barcode)
        if not warranty:
            raise gen.Return(self.error(u'此条码无保卡', com.ERROR_PARAM))

        today = datetime.datetime.now().date()
        buy_day = warranty['buy_date']
        if (buy_day + datetime.timedelta(days=3)).date() < today:
            raise gen.Return(self.error(u'此订单保卡时间已超过72小时，无法购买碎屏保', com.ERROR_PARAM))
        insurance_standard = yield broken_screen_insurance_model.get_insurance_standard(warranty['model_id'])
        if not insurance_standard:
            raise gen.Return(self.error(u'此机型无投保标准，无法投保', com.ERROR_EMPTY_DATA))
        ret = dict()
        ret['insurance_standard'] = insurance_standard
        ret['customer_name'] = warranty['customer_name']
        ret['customer_phone'] = warranty['customer_phone']
        raise gen.Return(self.success(ret))

    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        
        # 碎屏保订单审核服务时间 8：00---21：30
        if datetime.datetime.now().strftime('%H:%M:%S') > '21:30:00' or datetime.datetime.now().strftime('%H:%M:%S') < '08:00:00':
            raise gen.Return(self.error(u'碎屏保订单审核服务时间 8：00---21：30', com.ERROR_PARAM))

        resubmit = com.safeInt(self.get_argument('resubmit', 1), 1)
        order_sn = self.get_argument('order_sn', '')
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据错误', com.ERROR_PARAM))
        key_check = ['endpoint', 'endpoint_name', 'barcode', 'name', 'phone', 'standard']
        for i in key_check:
            if i not in content.keys():
                raise gen.Return(self.error(u'缺少参数' + i, com.ERROR_PARAM))

        barcode = content['barcode']
        # 检测是否已申请
        is_exist = yield broken_screen_insurance_model.check_is_exist(barcode)
        if is_exist in [100, 200, 300, 400, 500]:
            raise gen.Return(self.error(u'此条码已申请过碎屏保，无法再次申请', com.ERROR_ALREADY_EXISTS))
        if is_exist in [-300] and resubmit == 1:
            raise gen.Return(self.error(u'已有申请单,请勿重新申请,请重新提交旧申请单', com.ERROR_PARAM))
        # 检测保卡
        warranty = yield warranty_model.get_warranty_info(barcode)
        if not warranty:
            raise gen.Return(self.error(u'此条码无保卡', com.ERROR_PARAM))
        today = datetime.datetime.now()
        buy_day = warranty['buy_date']
        if (buy_day + datetime.timedelta(days=3)) < today:
            raise gen.Return(self.error(u'此订单保卡时间已超过72小时，无法购买碎屏保', com.ERROR_PARAM))
        # 获取投保标准
        insurance_standard = yield broken_screen_insurance_model.get_insurance_standard(warranty['model_id'])
        if not insurance_standard:
            raise gen.Return(self.error(u'此机型无投保标准，无法投保', com.ERROR_PARAM))
        # 获取终端信息和检测是否电商和是否直营
        endpoint = yield endpoint_model.get_endpoint_info(content['endpoint'])
        if not endpoint:
            raise gen.Return(self.error(u'无此终端, 请确认输入是否正确', com.ERROR_PARAM))
        # 直营电商才可以赠送
        if content.get('type') == 2 and \
                (endpoint.get('is_direct_sales') != 1 or endpoint.get('endpoint_channel') != 'e_commerce'):
            raise gen.Return(self.error(u'此终端渠道类型非电商，购买方式错误', com.ERROR_PARAM))

        insurance_standard_detail = yield broken_screen_insurance_model.get_insurance_standard_by_id(content['standard'])
        if not insurance_standard_detail:
            raise gen.Return(self.error(u'无此投保标准，无法投保', com.ERROR_PARAM))
        content['model_id'] = warranty['model_id']
        content['model_name'] = warranty['model']
        content['buy_date'] = warranty['buy_date']
        content['top_agency'] = endpoint['top_agency']
        content['second_agency'] = endpoint['second_agency']
        content['pay_amount'] = insurance_standard_detail['pay_amount']
        content['month'] = insurance_standard_detail['month']
        content['amount'] = insurance_standard_detail['amount']
        content['insurance_times'] = insurance_standard_detail['insurance_times']
        content['insurance_times_remain'] = insurance_standard_detail['insurance_times']
        content['endpoint_channel'] = endpoint['endpoint_channel']
        content['is_direct_sales'] = endpoint['is_direct_sales']
        if resubmit == 2:
            if is_exist not in [-300, -200]:
                raise gen.Return(self.error(u'状态不对，无法重新提交审核', com.ERROR_PARAM))
            # 软删除旧纪录
            data = yield broken_screen_insurance_model.delete_insurance(content['endpoint'], order_sn)
            if not data:
                raise gen.Return(self.error(u'申请提交审核失败', com.ERROR_SYSTEM))
        data = yield broken_screen_insurance_model.add_insurance(uid, content)
        raise gen.Return(self.success(data) if data else self.error(u'内部错误', com.ERROR_SYSTEM))

# 后台导入碎屏保
class AdminBrokenScreenInsurance(ApiHandler):

    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        barcode = self.get_argument('barcode', '')
        if not barcode:
            raise gen.Return(self.error(u'参数错误,条码不可为空', com.ERROR_PARAM))
        if barcode and not _isBarcode(barcode):
            raise gen.Return(self.error(u'条码格式错误', com.ERROR_PARAM))
        name = self.get_argument('name', '')
        if not name:
            raise gen.Return(self.error(u'参数错误,姓名不可为空', com.ERROR_PARAM))
        phone = self.get_argument('phone', '')
        if not phone:
            raise gen.Return(self.error(u'参数错误,手机不可为空', com.ERROR_PARAM))
        if not _isPhone(phone):
            raise gen.Return(self.error(u'手机格式错误', com.ERROR_PARAM))
        identity_card = self.get_argument('identity_card', '')
        # 检查是否申请过碎屏保
        is_exist = yield broken_screen_insurance_model.check_is_exist(barcode)
        if is_exist in [100, 200, 300, 400, 500]:
            raise gen.Return(self.error(u'此条码已申请过碎屏保，无法再次申请', com.ERROR_ALREADY_EXISTS))
        # 检查条码保卡
        warranty = yield warranty_model.get_warranty_info(barcode)
        if not warranty:
            raise gen.Return(self.error(u'此条码无保卡,不可导入碎屏保', com.ERROR_PARAM))
        # 检查是否是直营电商终端
        endpoint = yield check_endpoint(warranty.get('endpoint'))
        if not endpoint:
            raise gen.Return(self.error(u'此终端渠道类型非电商或非直营，不可申请碎屏保', com.ERROR_NOT_Direct_E))
        # 检查该机型投保标准
        standard = yield broken_screen_insurance_standard(warranty.get('model_id'))
        if not standard:
            raise gen.Return(self.error(u'此机型无投保标准，无法投保', com.ERROR_EMPTY_DATA))
        now = datetime.datetime.now()
        formatted_date = now.strftime('%Y%m%d%H%M%S')
        random_number = random.randint(100000, 999999)
        sn = "{}spb{}".format(formatted_date, random_number)
        bsisValues = {'barcode': barcode,
                      'name': name,
                      'phone': phone,
                      # 'uid': userId,
                      'identity_card': identity_card,
                      'type': 2,
                      'model_id': warranty.get('model_id'),
                      'model_name': warranty.get('model'),
                      'buy_date': warranty.get('buy_date').strftime("%Y-%m-%d %H:%M:%S"),
                      'created_at': warranty.get('buy_date').strftime("%Y-%m-%d %H:%M:%S"),
                      'endpoint': warranty.get('endpoint'),
                      'top_agency': endpoint.get('top_agency'),
                      'second_agency': endpoint.get('second_agency'),
                      'endpoint_channel': endpoint.get('endpoint_channel'),
                      'is_direct_sales': endpoint.get('is_direct_sales'),
                      'endpoint_name': endpoint.get('name'),
                      'pay_amount': standard.get('pay_amount'),
                      'month': standard.get('month'),
                      'amount': standard.get('amount'),
                      'insurance_times': standard.get('insurance_times'),
                      'insurance_times_remain': standard.get('insurance_times'),
                      'standard': standard.get('id'),
                      'is_supply': 1,
                      'sn': sn,
                      'status': 300,
                      'supply_at': now.strftime('%Y-%m-%d %H:%M:%S')}
        is_saved = yield save_broken_screen_insurance(bsisValues)
        raise gen.Return(self.success(True) if is_saved else self.error(u'导入电子碎屏保失败', com.ERROR_UNKNOWN))


# 获取支持投保的产品型号
# Add By SuGuanghua 2021-12-24 10:04
class SupportInsuranceModels(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))

        data = yield broken_screen_insurance_model.get_support_insurance_models()
        raise gen.Return(self.success(data) if data else self.error(u'暂无支持投保产品', com.ERROR_EMPTY_DATA))

class BrokenScreenInsuranceStandard(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        model_id = self.get_argument('model_id', '')
        if not model_id:
            raise gen.Return(self.error(u'机型id不能为空', com.ERROR_PARAM))

        data = yield broken_screen_insurance_model.get_insurance_standard(model_id)
        raise gen.Return(self.success(data) if data else self.error(u'此机型无投保标准', com.ERROR_EMPTY_DATA))


class BrokenScreenInsuranceDetail(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        order_sn = self.get_argument('order_sn', '')
        endpoint = self.get_argument('endpoint', '')
        if not order_sn or not endpoint:
            raise gen.Return(self.error(u'投保单号不能为空', com.ERROR_PARAM))

        data = yield broken_screen_insurance_model.get_insurance_detail(uid, endpoint,  order_sn)
        raise gen.Return(self.success(data) if data else self.error(u'无此单号信息', com.ERROR_EMPTY_DATA))


class BrokenScreenInsuranceList(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        endpoint = self.get_argument('endpoint', '')
        count = com.safeInt(self.get_argument('count', 10), 10)
        page = com.safeInt(self.get_argument('page', 1), 1)
        standard = self.get_argument('standard', '')
        barcode = self.get_argument('barcode', '')
        phone = self.get_argument('phone', '')
        identity_card = self.get_argument('identity_card', '')
        start = self.get_argument('start', '')
        end = self.get_argument('end', '')
        status = self.get_argument('status', '')
        if not endpoint:
            raise gen.Return(self.error(u'终端id不能为空', com.ERROR_PARAM))
        if start and not end:
            raise gen.Return(self.error(u'结束时间不能为空', com.ERROR_PARAM))
        data = yield broken_screen_insurance_model.get_insurance_list(endpoint, standard, barcode, phone,
                                                                      identity_card, count, page, start, end, status)
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_EMPTY_DATA))


class TradePayPreOrder(ApiHandler):
    @gen.coroutine
    def post(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))

        order_sn = self.get_argument('order_sn', '')
        # print order_sn
        order = yield broken_screen_insurance_model.get_order(order_sn, None)
        if not order:
            raise gen.Return(self.error(u'无此单号记录', com.ERROR_PARAM))
        check_order = yield broken_screen_insurance_model.check_order(order)
        if check_order == -1:
            raise gen.Return(self.error(u'此订单保卡时间已超过72小时，请刷新页面', com.ERROR_PARAM))
        if check_order == -2:
            raise gen.Return(self.error(u'此订单支付超时，请重新提交审核申请', com.ERROR_PARAM))
        if check_order == -3:
            raise gen.Return(self.error(u'状态不允许', com.ERROR_PARAM))
        order_verify = yield broken_screen_insurance_model.order_verify(order_sn, order['barcode'])
        if order_verify > 0:
            raise gen.Return(self.error(u'此条码已有一单完成支付，请勿重复操作', com.ERROR_PARAM))
        data = yield broken_screen_insurance_model.trade_pre_order(order_sn, order['pay_amount'], order)
        raise gen.Return(self.success(data) if data else self.error(u'内部错误', com.ERROR_SYSTEM))


class PayOrderQuery(ApiHandler):
    @gen.coroutine
    def get(self):
        order_sn = self.get_argument('order_sn', '')
        pay_id = self.get_argument('pay_id', '')
        if not order_sn and not pay_id:
            raise gen.Return(self.error(u'参数错误', com.ERROR_PARAM))
        data = yield broken_screen_insurance_model.pay_order_query(order_sn, pay_id)
        raise gen.Return(self.success(data) if data else self.error('未找到数据', com.ERROR_EMPTY_DATA))


class CallbackScreenInsurancePay(RequestHandler):
    @gen.coroutine
    def post(self):
        data = self.request.body
        data = json.loads(data)
        # print param.get('out_trade_no')
        app_log.info(data)
        out_trade_no = data.get('out_trade_no', '')   # sn 码

        if not out_trade_no:
            self.error(u'参数错误', com.ERROR_PARAM)
            raise gen.Return(self.finish())
        order = yield broken_screen_insurance_model.get_order(out_trade_no, None)
        if not order:
            self.error(u'无此流水号记录', com.ERROR_PARAM)
            raise gen.Return(self.finish())
        app_log.info(order)
        if order['is_paid'] == 0:
            if order['status'] == 200:
                data = yield broken_screen_insurance_model.pay_order_query(out_trade_no, None)
                # app_log.info(data)
                if data and data.get('status') == 1:
                    ret = yield broken_screen_insurance_model.order_pay_sure(data, out_trade_no)
                    app_log.info(ret)
                    if ret:
                        yield sms_model.broken_screen_sms_push(out_trade_no, order['endpoint'])
                        param = OrderedDict()
                        param['model'] = order['model_name']
                        param['sn'] = order['barcode']
                        param['money'] = str(order['amount']) + '元'
                        param['expire'] = str(order['month']) + '个月'
                        param = json.dumps(param)
                        yield sms_model.broken_screen_sms('649437', order['phone'], param)
                        self.success(ret)
                        raise gen.Return(self.finish())
                    else:
                        self.error(u'支付确认不成功', com.ERROR_PARAM)
                        raise gen.Return(self.finish())
                else:
                    self.error(u'未查到支付记录', com.ERROR_PARAM)
                    raise gen.Return(self.finish())
            self.success(True)
            raise gen.Return(self.finish())
        else:
            self.success(True)
            raise gen.Return(self.finish())

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.set_header('Access-Control-Allow-Origin', '*')
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.set_header('Access-Control-Allow-Origin', '*')
        self.dumpJson(ret)
        # app_log.info(s)

    def dumpJson(self, ret):
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET,POST')
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            s = json.dumps(ret, ensure_ascii=False, indent=4)
        else:
            s = json.dumps(ret, ensure_ascii=False)
        self.write(s)
        return s


class BrokenScreenRefund(ApiHandler):
    @gen.coroutine
    def post(self):
        order_sn = self.get_argument('order_sn', '')
        barcode = self.get_argument('barcode', '')
        # print order_sn
        order = yield broken_screen_insurance_model.get_order(order_sn, barcode)
        if not order:
            raise gen.Return(self.error(u'无此单号记录', com.ERROR_PARAM))
        if order['status'] not in [100, 200, 300]:
            raise gen.Return(self.error(u'当前状态不允许退款', com.ERROR_PARAM))
        now = datetime.datetime.now() + datetime.timedelta(days=-7)
        # 碎屏保投保时间超过7天   不允许退保   状态改为超期退保700
        if order['created_at'].date() < now.date():
            warranty = yield warranty_model.get_warranty_info(barcode)
            if not warranty:
                yield broken_screen_insurance_model.order_return(order_sn)
            raise gen.Return(self.success(True))
        # 直营退保直接改为600 (0元直接退保)
        if order.get('is_direct_sales') == 1 or order.get('pay_amount') == 0 or order.get('pay_amount') == '0.00':
            ret = yield broken_screen_insurance_model.direct_sales_return(order['sn'])
            raise gen.Return(self.success(True) if ret else self.error(u'退保失败', com.ERROR_SYSTEM))
        data, msg = yield broken_screen_insurance_model.broken_screen_refund(order['sn'])
        raise gen.Return(self.success(data) if data else self.error(msg, com.ERROR_SYSTEM))

## 碎屏保退款 ,寄修后台调用
class BrokenScreenRefund2(ApiHandler):
    @gen.coroutine
    def post(self):
        order_sn = self.get_argument('order_sn', '')
        barcode = self.get_argument('barcode', '')
        order = yield broken_screen_insurance_model.get_order(order_sn, barcode)
        if not order:
            raise gen.Return(self.error(u'无此单号记录', com.ERROR_PARAM))
        
        yield broken_screen_insurance_model.add_refund_remark(order['sn'])
        data, msg = yield broken_screen_insurance_model.broken_screen_refund(order['sn'])
        raise gen.Return(self.success(data) if data else self.error(msg, com.ERROR_SYSTEM))

class CallbackScreenInsuranceRefund(RequestHandler):
    @gen.coroutine
    def post(self):
        data = self.request.body
        data = json.loads(data)
        # print param.get('out_trade_no')
        db_log.info(data)
        out_trade_no = data.get('out_trade_no', '')   # sn 码
        if not out_trade_no:
            self.error(u'参数错误', com.ERROR_PARAM)
            raise gen.Return(self.finish())
        order = yield broken_screen_insurance_model.get_order(out_trade_no, None)
        if not order:
            self.error(u'无此流水号记录', com.ERROR_PARAM)
            raise gen.Return(self.finish())
        if order['is_refund'] == 0:
            if order['status'] == 300 or order['status'] == -500:
                data, msg = yield broken_screen_insurance_model.refund_order_query(out_trade_no)
                if not data:
                    self.error(u'查询出错', com.ERROR_PARAM)
                    raise gen.Return(self.finish())
                refund_orders = data['refund_orders'][0]
                if refund_orders and refund_orders['status'] == 1:
                    ret = yield broken_screen_insurance_model.order_refund_sure(refund_orders, out_trade_no)
                    if ret:
                        self.success(ret)
                        raise gen.Return(self.finish())
            self.success(True)
            raise gen.Return(self.finish())
        else:
            self.success(True)
            raise gen.Return(self.finish())

    def success(self, data=None, msg='success'):
        ret = {'ok': 1, 'msg': msg, 'data': data}
        self.set_header('Access-Control-Allow-Origin', '*')
        self.dumpJson(ret)

    def error(self, msg=u'unknown error', errno=com.ERROR_UNKNOWN):
        ret = {'ok': 0, 'msg': msg, 'errno': errno}
        self.set_header('Access-Control-Allow-Origin', '*')
        self.dumpJson(ret)
        # app_log.info(s)

    def dumpJson(self, ret):
        self.set_header('Access-Control-Allow-Origin', '*')
        self.set_header('Access-Control-Allow-Methods', 'GET,POST')
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        # self.write(json.dumps(ret, ensure_ascii=False))
        if self.settings['debug']:
            s = json.dumps(ret, ensure_ascii=False, indent=4)
        else:
            s = json.dumps(ret, ensure_ascii=False)
        self.write(s)
        return s


class ScreenInsuranceRefundQuery(ApiHandler):
    @gen.coroutine
    def get(self):
        uid = yield self.check_uid()
        # uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))

        order_sn = self.get_argument('order_sn', '')
        data, msg = yield broken_screen_insurance_model.refund_order_query(order_sn)
        raise gen.Return(self.success(data) if data else self.error(msg, com.ERROR_EMPTY_DATA))


class BrokenScreenCheck(ApiHandler):
    @gen.coroutine
    def get(self):
        barcode = self.get_argument('barcode', '')
        # print barcode
        if not barcode:
            raise gen.Return(self.error(u'参数barcode不能为空', com.ERROR_PARAM))
        data = yield broken_screen_insurance_model.check_has_broken_screen(barcode)
        # print data
        raise gen.Return(self.success(data) if data else self.error(u'未找到数据', com.ERROR_PARAM))


class BrokenScreenExchange(ApiHandler):
    @gen.coroutine
    def post(self):
        barcode = self.get_argument('old_barcode', '')
        new_barcode = self.get_argument('new_barcode', '')
        order_sn = self.get_argument('order_sn', '')

        # print barcode
        if not barcode or not new_barcode or not order_sn:
            raise gen.Return(self.error(u'参数barcode 或 order_sn不能为空', com.ERROR_PARAM))
        data = yield broken_screen_insurance_model.check_has_broken_screen(barcode)
        if not data:
            raise gen.Return(self.success(data))
        # de = yield broken_screen_insurance_model.delete_insurance(data['endpoint'], order_sn)
        # warranty = yield warranty_model.get_warranty_info(new_barcode)
        # if not warranty:
        #     raise gen.Return(self.error(u'此条码无保卡, 无法更换碎屏保', com.ERROR_PARAM))
        data = yield broken_screen_insurance_model.exchange_insurance(data, new_barcode, order_sn)
        # print data
        raise gen.Return(self.success(data) if data else self.error(u'内部错误', com.ERROR_SYSTEM))


class BrokenScreenCheckByBarcodeList(ApiHandler):
    @gen.coroutine
    def get(self):
        content = self.get_argument('content', '')
        try:
            content = json.loads(content)
        except:
            raise gen.Return(self.error(u'数据有误', com.ERROR_PARAM))
        ret = dict()
        for barcode in content['barcode']:
            data = yield broken_screen_insurance_model.check_has_broken_screen(barcode)
            ret[barcode] = 1 if data else 0
        raise gen.Return(self.success(ret) if ret else self.error(u'未找到数据', com.ERROR_PARAM))