# encoding=utf-8

import datetime

from decimal import Decimal

from conf import config
from store import DB_PR

from tornado import gen
from tornado.log import app_log


OA_STATUS_NORMAL = 0
OA_STATUS_CANCEL_PARTLY = -1
OA_STATUS_CANCEL_TOTAL = -2
PR_OA_STATUS_NORMAL = 0
PR_OA_STATUS_CANCEL = -1


def oa_image_fill(data):
    for d in data:
        if d.get('image') and isinstance(d['image'], basestring):
            d['image'] = config.OSS_CNAME + d['image']


oac_select_fields = ['id', 'name', 'sort']
oac_select_fields_str = ','.join(oac_select_fields)

@gen.coroutine
def oac_list():
    sql_base = 'select {fields} from optional_accessory_category '
    sql_base_select = sql_base.format(fields=oac_select_fields_str)
    sql_where = 'where enable = 1 '
    # 取数据
    sql_select = sql_base_select + sql_where
    cur = yield DB_PR.execute(sql_select)
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    raise gen.Return(data)

@gen.coroutine
def oac_group_count():
    sql_base = 'select category_id, count(*) as count from optional_accessory '
    sql_where = 'where enable = 1 '
    sql_group = 'group by category_id '
    sql_select = sql_base + sql_where + sql_group
    cur = yield DB_PR.execute(sql_select)
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    raise gen.Return(data)

def oac_group_count_to_dict(oac_count_list):
    dic = {}
    if oac_count_list and len(oac_count_list) > 0:
        for i in oac_count_list:
            if i.get('category_id', 0) > 0:
                dic[i['category_id']] = i.get('count', 0)
    return dic


oa_select_fields = ['id', 'category_id', 'name', 'sort', 'image', 'sell_price']
oa_select_fields_str = ','.join(oa_select_fields)

@gen.coroutine
def oa_list(category_id, page, count):
    if count > 100:
        count = 100
    offset = (page - 1) * count
    if offset <= 0:
        offset = 0
    param_order = tuple([offset, count + 1])
    sql_base = 'select {fields} from optional_accessory '
    sql_base_count = sql_base.format(fields='count(*) as count')
    sql_base_select = sql_base.format(fields=oa_select_fields_str)
    sql_where = 'where enable = 1 '
    param_where = tuple()
    if category_id:
        sql_where += 'and category_id = %s '
        param_where = tuple([category_id])
    sql_order = 'order by sort desc  limit %s, %s'
    # 取数据
    sql_count = sql_base_count + sql_where
    sql_select = sql_base_select + sql_where + sql_order
    param = param_where + param_order
    cur = yield DB_PR.execute(sql_select, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    # 格式
    oa_image_fill(data)
    l = len(data)
    is_end = l < count + 1
    size = l if is_end else count
    data = data[0:size]
    # 总数
    cur = yield DB_PR.execute(sql_count, param_where)
    data_c = cur.fetchone()
    all_count = None
    if data_c:
        all_count = data_c.get('count')
    # 返回
    ret = {
        'data': data,
        'size': size,
        'is_end': is_end,
        'all_count': all_count
    }
    raise gen.Return(ret)

@gen.coroutine
def oa_list_by_ids(oa_ids):
    sql_base = 'select * from optional_accessory oa '
    sql_where = 'where oa.id in (%s) ' % ','.join(['%s'] * len(oa_ids))
    sql = sql_base + sql_where
    param_where = tuple(oa_ids)
    cur = yield DB_PR.execute(sql, param_where)
    data = cur.fetchall()
    if not data:
        raise gen.Return([])
    raise gen.Return(data)

@gen.coroutine
def oa_quantity_dict(oa_ids):
    if not isinstance(oa_ids, list) or not len(oa_ids) > 0:
        raise gen.Return({})
    sql_base = 'select oa.id, m.quantity from optional_accessory oa '
    sql_join = 'left join material m on oa.material_id = m.id '
    sql_where = 'where oa.id in (%s) ' % ','.join(['%s'] * len(oa_ids))
    sql = sql_base + sql_join + sql_where
    param_where = tuple(oa_ids)
    cur = yield DB_PR.execute(sql, param_where)
    data = cur.fetchall()
    if not data:
        raise gen.Return({})
    raise gen.Return({d['id'] : d['quantity'] for d in data})

@gen.coroutine
def pr_oa_count_dict(sn):
    sql_base = 'select oa_id, sum(count) as count from pr_optional_accessory '
    sql_where = 'where pr_sn = %s and status = %s and cancel_at is null '
    sql_group = 'group by oa_id '
    sql = sql_base + sql_where + sql_group
    param = tuple([sn, PR_OA_STATUS_NORMAL])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return({})
    raise gen.Return({d['oa_id'] : d['count'] for d in data})

@gen.coroutine
def material_quantity(material_ids):
    sql_base = 'select id, quantity from material '
    sql_where = 'where id in (%s) ' % ','.join(['%s'] * len(material_ids))
    sql = sql_base + sql_where
    param_where = tuple(material_ids)
    cur = yield DB_PR.execute(sql, param_where)
    data = cur.fetchall()
    if not data:
        raise gen.Return(None)
    raise gen.Return(data)

def material_quantity_to_dict(material_quantity_list):
    dic = {}
    if material_quantity_list and len(material_quantity_list) > 0:
        for i in material_quantity_list:
            if i.get('id', 0) > 0:
                dic[i['id']] = i.get('quantity', 0)
    return dic


oa_sn_select_fields = ['name', 'image', 'count', 'price', 'status']
oa_sn_select_fields_str = ','.join(oa_sn_select_fields)

@gen.coroutine
def pr_oa_list(sn):
    sql_base = 'select {fields} from pr_optional_accessory pr_oa '
    sql_join = 'left join optional_accessory oa on pr_oa.oa_id = oa.id '
    sql_where = 'where pr_oa.pr_sn = %s '
    sql_base_select = sql_base.format(fields=oa_sn_select_fields_str)
    sql = sql_base_select + sql_join + sql_where
    param = tuple([sn])
    cur = yield DB_PR.execute(sql, param)
    data = cur.fetchall()
    if not data:
        raise gen.Return([])
    oa_image_fill(data)
    raise gen.Return(data)


pr_oa_insert_fields = ['pr_sn', 'oa_id', 'material_id', 'count', 'price', 'status', 'created_at', 'updated_at']
pr_oa_insert_fields_str = ','.join(pr_oa_insert_fields)

@gen.coroutine
def pr_oa_from_input(sn, content):
    to_pr_oa_list = []
    if not isinstance(sn, basestring):
        raise gen.Return(to_pr_oa_list)
    if not isinstance(content, list) and not isinstance(content, tuple):
        raise gen.Return(to_pr_oa_list)
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    for item in content:
        if not isinstance(item, dict):
            continue
        oa_id = item.get('oa_id')
        count = item.get('count')
        if not isinstance(oa_id, int) or oa_id <= 0 or not isinstance(count, int) or count <= 0:
            continue
        dic = {
            'pr_sn': sn,
            'oa_id': oa_id,
            'count': count,
            'status': PR_OA_STATUS_NORMAL,
            'created_at': now,
            'updated_at': now,
        }
        to_pr_oa_list.append(dic)
    if len(to_pr_oa_list) > 0:
        oa_ids = [x.get('oa_id') for x in to_pr_oa_list]
        oa_list_info = yield oa_list_by_ids(oa_ids)
        for oa_d in to_pr_oa_list:
            oa_id = oa_d.get('oa_id')
            filted = filter(lambda x: x.get('id') == oa_id, oa_list_info)
            if len(filted) > 0:
                oa_d['material_id'] = filted[0].get('material_id')
                oa_d['price'] = filted[0].get('sell_price')
    raise gen.Return(to_pr_oa_list)

@gen.coroutine
def pr_oa_add(sn, endpoint_id, pr_oa_from):
    """
    修改指定订单的自选配件列表
    """
    trans = yield DB_PR.begin()
    ret = False
    try:
        # 删除原有配件列表
        yield pr_oa_recover_material(trans, sn)
        yield pr_oa_delete_all(trans, sn)
        # 插入订单关联配件信息
        if len(pr_oa_from) > 0:
            value_str = ','.join(['%s'] * len(pr_oa_insert_fields))
            values = ','.join(['('+value_str+')'] * len(pr_oa_from))
            param_insert = []
            for pr_oa in pr_oa_from:
                for k in pr_oa_insert_fields:
                    param_insert.append(pr_oa.get(k))
            param_insert = tuple(param_insert)
            sql_insert = 'insert into pr_optional_accessory ({fields}) values {values} '.format(fields=pr_oa_insert_fields_str, values=values)
            cur = yield trans.execute(sql_insert, param_insert)
        # 读取订单的自选配件
        sql_get_select = 'select oa_id, count, price, status, cancel_at from pr_optional_accessory '
        sql_get_where = 'where pr_sn = %s '
        sql_get = sql_get_select + sql_get_where
        param_get = tuple([sn])
        cur = yield trans.execute(sql_get, param_get)
        pr_oa_list = cur.fetchall()
        # 计算订单自选配件费用
        ## 统计金额
        oa_amount = Decimal('0.00')
        oa_cast = Decimal('0.00')
        for pr_oa in pr_oa_list:
            count = pr_oa.get('count', 0)
            price = pr_oa.get('price', Decimal('0.00'))
            if count <= 0 or price <= 0:
                continue
            oa_amount += count * price
            if pr_oa.get('status') == PR_OA_STATUS_NORMAL and pr_oa.get('cancel_at') == None:
                oa_cast += count * price
        ## 执行操作
        tname = 'order_cache' if endpoint_id > 0 else '`order`'
        yield pr_oa_recalculate_order(trans, sn, OA_STATUS_NORMAL, oa_amount, oa_cast)
        # 扣除物料库存
        sql_update = 'update material set quantity = quantity - %s where id = %s '
        for pr_oa in pr_oa_from:
            count = pr_oa.get('count', 0)
            material_id = pr_oa.get('material_id')
            if not material_id > 0 or not count > 0:
                continue
            param_update = tuple([count, material_id])
            cur = yield trans.execute(sql_update, param_update)
        # 提交返回
        yield trans.commit()
        ret = True
    except:
        trans.rollback()
        app_log.error('store pr_oa error:', exc_info=True)
    raise gen.Return(ret)

@gen.coroutine
def pr_oa_recalculate_order(trans, sn, oa_status, oa_amount, oa_cast):
    if not sn or not isinstance(sn, basestring) or not len(sn) == 20:
        raise gen.Return(None)
    # 读取订单信息
    sql_price = 'select o.staff_cast, o.accessory_cast, o.accessory_amount, oe.is_exchange_for_repair, oe.balance_amount from `order` o ' \
                'left join order_extend oe on o.sn = oe.sn ' \
                'where o.sn = %s '
    cur = yield trans.execute(sql_price, sn)
    data = cur.fetchone()
    if not data or not isinstance(data, dict):
        raise gen.Return(None)
    # 计算金额
    new_amount = data.get('staff_cast', Decimal('0.00')) + data.get('accessory_amount', Decimal('0.00')) + oa_amount
    new_pay_amount = data.get('staff_cast', Decimal('0.00')) + data.get('accessory_cast', Decimal('0.00')) + oa_cast
    if data.get('is_exchange_for_repair') == 1:
        new_amount += data.get('balance_amount', Decimal('0.00'))
        new_pay_amount += data.get('balance_amount', Decimal('0.00'))
    # 更新订单表
    sql_order = 'update `order` set ' \
                '    optional_accessory_status = %s, ' \
                '    optional_accessory_amount = %s, ' \
                '    optional_accessory_cast = %s, ' \
                '    amount = %s, pay_amount = %s ' \
                'where sn = %s '
    param_order = tuple([oa_status, oa_amount, oa_cast, new_amount, new_pay_amount, sn])
    yield trans.execute(sql_order, param_order)
    raise gen.Return(data)

@gen.coroutine
def pr_oa_delete_all(trans, sn):
    if not sn or not isinstance(sn, basestring) or not len(sn) == 20:
        raise gen.Return(None)
    sql = 'delete from pr_optional_accessory where pr_sn = %s '
    param = tuple([sn])
    cur = yield trans.execute(sql, param)
    raise gen.Return(cur)

@gen.coroutine
def pr_oa_material_sum(trans, sn):
    # 读取配件信息
    sql_sum_select = 'select material_id, sum(count) as sum from pr_optional_accessory poa '
    sql_sum_where = 'where pr_sn = %s and status = %s and cancel_at is null '
    sql_sum_group = 'group by material_id '
    sql_sum = sql_sum_select + sql_sum_where + sql_sum_group
    param_sum = tuple([sn, PR_OA_STATUS_NORMAL])
    cur = yield trans.execute(sql_sum, param_sum)
    data_material = cur.fetchall()
    raise gen.Return(data_material)

@gen.coroutine
def pr_oa_recover_material(trans, sn):
    data_material = yield pr_oa_material_sum(trans, sn)
    # 恢复物料库存
    if data_material and len(data_material) > 0:
        sql_material = 'update material set quantity = quantity + %s where id = %s '
        for mat in data_material:
            sum = mat.get('sum')
            material_id = mat.get('material_id')
            if not material_id > 0 or not sum > 0:
                continue
            param_material = tuple([sum, material_id])
            cur = yield trans.execute(sql_material, param_material)
    raise gen.Return(data_material)

@gen.coroutine
def pr_oa_cancel_all(sn):
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    trans = yield DB_PR.begin()
    ret = False
    try:
        yield pr_oa_recover_material(trans, sn)
        # 查询pr_oa表
        # sql_pr_oa_select = 'select * from pr_optional_accessory where pr_sn = %s '
        # cur = yield trans.execute(sql_pr_oa_select, tuple([sn]))
        # pr_oa_data = cur.fetchall()
        # 更新pr_oa表
        sql_pr_oa = 'update pr_optional_accessory set `status` = %s, cancel_at = %s, updated_at = %s where pr_sn = %s '
        param_pr_oa = tuple([PR_OA_STATUS_CANCEL, now, now, sn])
        cur = yield trans.execute(sql_pr_oa, param_pr_oa)
        # 重新计算订单信息
        yield pr_oa_recalculate_order(trans, sn, OA_STATUS_CANCEL_TOTAL, Decimal('0.00'), Decimal('0.00'))
        # 提交返回
        yield trans.commit()
        ret = True
    except:
        trans.rollback()
        app_log.error('cancel pr_oa error', exc_info=True)
    raise gen.Return(ret)
