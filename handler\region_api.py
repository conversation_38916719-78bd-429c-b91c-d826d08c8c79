# encoding=utf-8
# __author__ = 'lch'

from tornado import gen
from tornado.log import app_log
import traceback

import util.com as com
from handler import ApiHandler, tokenauth
import cache.user_cache as auth_cache
import store.user_model as user_model
import store.region_model as region_model


class Districts(ApiHandler):
    @gen.coroutine
    # @tokenauth
    def get(self):
        # token = self.get_argument('access_token')
        # if not auth_cache.checkAuth(token):
            # raise gen.Return(self.error(u'未登录或登录失效', com.ERROR_BAD_TOKEN))
        pid = com.safeInt(self.get_argument('pid', None), -1)
        level = com.safeInt(self.get_argument('level', 0))
        if pid >= 0:
            dists = yield region_model.subDistricts(pid)
        else:
            dists = yield region_model.batchDistricts(level)
        self.success(dists) if dists else self.error(u'未找到相关地区信息', com.ERROR_EMPTY_DATA)


class Agencies(ApiHandler):
    @gen.coroutine
    def get(self):
        try:
            regions, agencies = yield [region_model.regions(), region_model.agencies()]
            ret = {
                'partition': regions,
                'agency': agencies
            }
            self.success(ret)
        except Exception as e:
            traceback.print_exc()
            self.error(e.message, com.ERROR_UNKNOWN)
