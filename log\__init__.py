#coding=utf-8
#encoding=utf-8
#__author__="QZL"

import logging
from pythonjsonlogger import jsonlogger
from conf import config
#if config.DEBUG:
#    from logging.handlers import TimedRotatingFileHandler
#else:
#    from safe_logger import TimedRotatingFileHandlerSafe
from logging.handlers import WatchedFileHandler
import json
import os
from conf.config import LOG_DIR, API_LOG_DIR
from tornado.log import LogFormatter

class MyJsonFormatter(jsonlogger.JsonFormatter):
    def __init__(self, *args, **kwargs):
        super(MyJsonFormatter, self).__init__(*args, **kwargs)

    def format(self, record):
        """Formats a log record and serializes to json"""
        message_dict = {}
        if isinstance(record.msg, dict):
            message_dict = record.msg
            record.message = None
        else:
            record.message = record.getMessage()
        # only format time if needed
        if "asctime" in self._required_fields:
            record.asctime = self.formatTime(record, self.datefmt)

        # Display formatted exception, but allow overriding it in the
        # user-supplied dict.
        if record.exc_info and not message_dict.get('exc_info'):
            message_dict['exc_info'] = self.formatException(record.exc_info)

        try:
            from collections import OrderedDict
            log_record = OrderedDict()
        except NameError:
            log_record = {}

        self.add_fields(log_record, record, message_dict)
        log_record = self.process_log_record(log_record)

        return "%s%s" % (self.prefix,
                         json.dumps(log_record, ensure_ascii=False,
                                    default=self.json_default,
                                    cls=self.json_encoder))


class LoggerStore(object):
    '''日志方式存储事件数据'''
    def __init__(self):
        super(LoggerStore, self).__init__()
        # self.event_logger = self._getLogger('event', conf.LOG_EVENT_PATH)
        # self.session_logger = self._getLogger('session', conf.LOG_SESSION_PATH)
        self.event_loggers = {}
        self.session_loggers = {}
        pass

    def _makeLogger(self, name, path):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        #logHandler = TimedRotatingFileHandler(path, when='midnight') if config.DEBUG else TimedRotatingFileHandlerSafe(path, when='midnight')
        # logHandler = TimedRotatingFileHandlerSafe(path, when='midnight')
        # logHandler = logging.StreamHandler()
        logHandler = WatchedFileHandler(path)
        formatter = LogFormatter()
        logHandler.setFormatter(formatter)
        logger.addHandler(logHandler)
        return logger

    def _makeApiLogger(self, name, path):
        logger = logging.getLogger(name)  # 创建指定名称记录器  不然返回根记录器
        logger.setLevel(logging.INFO)  # 日志记录级别
        #logHandler = TimedRotatingFileHandler(path, when='midnight') if config.DEBUG else TimedRotatingFileHandlerSafe(
        #    path, when='midnight')
        logHandler = WatchedFileHandler(path)
        formatter = logging.Formatter("%(message)s")
        logHandler.setFormatter(formatter)
        logger.addHandler(logHandler)
        logger.propagate = False
        return logger


    def getAppLogger(self, app, etype='event'):
        name = app+'_'+etype
        logger = self.event_loggers.get(name)
        if not logger:
            app_dir = os.path.join(LOG_DIR, app)
            path = os.path.join(app_dir, etype+'.log')
            if not os.path.exists(app_dir):
                os.makedirs(app_dir)
            logger = self._makeLogger(name, path)
            self.event_loggers[name] = logger
        return logger

    def getApiLogger(self, app, etype='event'):
        name = app+'_'+etype
        logger = self.event_loggers.get(name)
        if not logger:
            app_dir = os.path.join(API_LOG_DIR, app)
            path = os.path.join(app_dir, etype+'.log')
            if not os.path.exists(app_dir):
                os.makedirs(app_dir)
            logger = self._makeApiLogger(name, path)
            self.event_loggers[name] = logger
        return logger

    def getEventLogger(self, app):
        return self.getAppLogger(app, 'event')

    def getSessionLogger(self, app):
        return self.getAppLogger(app, 'session')

    def saveEvents(self, evs):
        if evs:
            for ev in evs:
                app = ev.get('app', 'none')
                self.getEventLogger(app).info(ev)
        pass

    def saveSessions(self, ss):
        if ss:
            for s in ss:
                app = s.get('app', 'none')
                self.getSessionLogger(app).info(s)
        pass

store = LoggerStore()
db_log = store.getAppLogger('db')
alipay_log = store.getAppLogger('pay', 'alipay')
wxpay_log = store.getAppLogger('pay', 'wxpay')
sf_log = store.getAppLogger('exp', 'sf')

api_log = store.getApiLogger('post_repair', 'app')