# encoding=utf-8
# __author__ = 'lch'
import datetime
import time
import random
import os
from hashlib import md5
from tornado import gen
from tornado.log import app_log
from tornado.concurrent import run_on_executor
from concurrent.futures import Thread<PERSON>oolExecutor
import oss2

from handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, tokenauth
import util.com as com
import conf.config as config


MAX_WORKERS = 20


oss_auth = oss2.Auth(config.OSS_ACCESS_ID, config.OSS_ACCESS_KEY)
bucket = oss2.Bucket(oss_auth, config.OSS_ENDPOINT, config.OSS_BUCKET)


class UploadImage(ApiHandler):
    executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)

    @gen.coroutine
    @tokenauth
    def post(self):
        f = self.request.files['images'][0]
        original_fname = f['filename']
        extension = os.path.splitext(original_fname)[1]
        hashsum = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        oss_filename = 'rbcare/endpoint/images/' + hashsum + extension
        final_filename = config.OSS_CNAME+oss_filename
        app_log.info('upload image: '+final_filename)
        ret = yield self._ossUpload(oss_filename, f['body'])
        # app_log.info(ret.status)

        self.success(final_filename) if ret.status == 200 else self.error(u'上传文件失败', com.ERROR_UNKNOWN)
        pass

    @run_on_executor
    def _ossUpload(self, filename, fbody):
        return bucket.put_object(filename, fbody)

class PR_UploadPeriod(ApiHandler):
    executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)

    @gen.coroutine
    # @tokenauth
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        f = self.request.files['images'][0]
        original_fname = f['filename']
        extension = os.path.splitext(original_fname)[1]
        hashsum = '%s_%d' % (datetime.datetime.now().strftime('%Y%m%d%H%M%S'), com.safeInt(time.time() * 1000))
        sub = str(uid) + '/'
        oss_filename = 'rbcare/repair/upload/' + sub + hashsum + extension
        final_filename = config.OSS_CNAME+oss_filename
        app_log.info('upload image: '+final_filename)
        ret = yield self._ossUpload(oss_filename, f['body'])
        # app_log.info(ret.status)

        self.success(final_filename) if ret.status == 200 else self.error(u'上传文件失败', com.ERROR_UNKNOWN)
        pass

    @run_on_executor
    def _ossUpload(self, filename, fbody):
        return bucket.put_object(filename, fbody)

class PR_UploadFile(ApiHandler):
    executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)

    @gen.coroutine
    # @tokenauth
    def post(self):
        uid = yield self.check_uid()
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        f = self.request.files['images'][0]
        original_fname = f['filename']
        extension = os.path.splitext(original_fname)[1]
        hashsum = '%s_%d' % (datetime.datetime.now().strftime('%Y%m%d%H%M%S'), com.safeInt(time.time() * 1000))
        sub = str(uid) + '/'
        oss_filename = 'rbcare/repair/upload/' + sub + hashsum + extension
        final_filename = config.OSS_CNAME+oss_filename
        app_log.info('upload image: '+final_filename)
        ret = yield self._ossUpload(oss_filename, f['body'])
        # app_log.info(ret.status)

        self.success(final_filename) if ret.status == 200 else self.error(u'上传文件失败', com.ERROR_UNKNOWN)
        pass

    @run_on_executor
    def _ossUpload(self, filename, fbody):
        return bucket.put_object(filename, fbody)


class BSI_UploadFile(ApiHandler):
    executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)

    @gen.coroutine
    # @tokenauth
    def post(self):
        # uid = yield self.check_uid()
        uid = 1
        if not uid:
            raise gen.Return(self.error(u'未登录或令牌过期', com.ERROR_BAD_TOKEN))
        f = self.request.files['file'][0]
        original_fname = f['filename']
        # 分离后缀
        extension = os.path.splitext(original_fname)[1]
        # 设置文件名
        hashsum = datetime.datetime.now().strftime('%Y%m%d%H%M%S') + (str)(random.randint(100000, 999999))
        sub = str(uid) + '/'
        oss_filename = 'rbcare/broken_screen_insurance/upload/' + sub + hashsum + extension
        # 最后路径
        final_filename = config.OSS_CNAME+oss_filename
        app_log.info('upload image: '+final_filename)
        # 上传oss
        ret = yield self._ossUpload(oss_filename, f['body'])
        app_log.info(ret.status)
        #
        self.success(final_filename) if ret.status == 200 else self.error(u'上传文件失败', com.ERROR_UNKNOWN)
        pass

    @run_on_executor
    def _ossUpload(self, filename, fbody):
        return bucket.put_object(filename, fbody)